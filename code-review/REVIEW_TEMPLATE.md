# Code Review Template

## Review Information
- **Date**: [YYYY-MM-DD]
- **Reviewer**: [Name]
- **Branch/PR**: [branch-name or PR#]
- **Files Changed**: [number]
- **Lines Changed**: [+added/-removed]

## Review Checklist

### 🔍 Code Quality
- [ ] **Variable Naming**: All variables use descriptive names (no abbreviations or single letters)
- [ ] **Function Naming**: Functions clearly describe what they do
- [ ] **Code Comments**: Complex logic is properly commented
- [ ] **No Dead Code**: Removed any commented-out or unused code
- [ ] **DRY Principle**: No significant code duplication

### 🛡️ Safe Scraping (if applicable)
- [ ] **Rate Limiting**: Proper delays implemented (10-30s for STRICT mode)
- [ ] **Error Handling**: Circuit breakers configured correctly
- [ ] **Monitoring**: Logs written to `logs/` directory
- [ ] **Fallbacks**: Graceful degradation implemented
- [ ] **User Agents**: Rotation configured if needed

### 📦 TypeScript/JavaScript
- [ ] **Type Safety**: No `any` types without justification
- [ ] **Async/Await**: Proper error handling for promises
- [ ] **Null Checks**: Proper null/undefined checking
- [ ] **ESLint**: Passes `npm run lint` without errors
- [ ] **Prettier**: Passes `npm run prettier:check`

### 🧪 Testing
- [ ] **Unit Tests**: New features have tests
- [ ] **Integration Tests**: API changes tested
- [ ] **Manual Testing**: Feature tested locally
- [ ] **Edge Cases**: Handled error scenarios

### 📚 Documentation
- [ ] **README**: Updated if adding new features
- [ ] **CLAUDE.md**: Updated for significant changes
- [ ] **JSDoc**: Functions have proper documentation
- [ ] **API Changes**: Documented breaking changes

### 🔒 Security
- [ ] **No Secrets**: No API keys or passwords in code
- [ ] **Input Validation**: User inputs are validated
- [ ] **Dependencies**: No vulnerable dependencies added
- [ ] **Permissions**: File permissions appropriate

## Review Summary

### ✅ Approved Changes
- [List what looks good]

### 🔧 Required Changes
- [ ] [Issue 1]
- [ ] [Issue 2]

### 💡 Suggestions (Optional)
- [Suggestion 1]
- [Suggestion 2]

### 📝 Notes
[Additional comments or context]

## Final Status
- [ ] **Approved** - Ready to merge
- [ ] **Approved with Changes** - Minor fixes needed
- [ ] **Request Changes** - Significant issues to address
- [ ] **Comment Only** - No approval, just feedback