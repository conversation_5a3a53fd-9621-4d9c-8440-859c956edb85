# Quick Code Review Checklist

## Pre-Review Checks
```bash
npm run build         # TypeScript compiles
npm run lint          # ESLint passes
npm run prettier:check # Formatting correct
```

## Critical Items

### 1. Variable Naming
❌ BAD:
```typescript
const d = new Date();
const u = await getUser();
const res = await fetch(url);
```

✅ GOOD:
```typescript
const currentDate = new Date();
const authenticatedUser = await getUser();
const apiResponse = await fetch(url);
```

### 2. Safe Scraping Checks
- Delays between requests: minimum 10 seconds (STRICT mode)
- Error handling with circuit breakers
- Logging to `logs/` directory
- User agent rotation enabled

### 3. TypeScript Best Practices
```typescript
// ❌ Avoid
function process(data: any): any { }

// ✅ Prefer
interface ProcessedData {
  result: string;
  timestamp: Date;
}
function processUserData(userData: UserInput): ProcessedData { }
```

### 4. Async Error Handling
```typescript
// ❌ Avoid
async function risky() {
  const data = await fetch(url);
  return data.json();
}

// ✅ Prefer
async function safeOperation(): Promise<Result> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    logger.error('Operation failed:', error);
    throw new SafeScrapingError('Failed to fetch data', { cause: error });
  }
}
```

### 5. Logging Standards
```typescript
// All logs should go to the logs/ directory
import { createWriteStream } from 'fs';

const logStream = createWriteStream('./logs/operation.log', { flags: 'a' });

// Use descriptive log levels
logger.info('Starting operation', { userId, timestamp });
logger.warn('Rate limit approaching', { remaining: 5 });
logger.error('Operation failed', { error, context });
```

## Quick Approval Guide

### ✅ Fast Approve If:
- All automated checks pass
- Variable names are descriptive
- No `console.log` in production code
- Proper error handling throughout
- Tests included for new features

### 🚫 Request Changes If:
- Single letter variables or abbreviations
- Missing error handling on async operations
- Hardcoded secrets or API keys
- No logging for critical operations
- Breaking changes without documentation

### 🤔 Discuss If:
- Large architectural changes
- New dependencies added
- Performance concerns
- Alternative approaches available