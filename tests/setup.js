/**
 * Jest Test Setup
 * Global test configuration and mocks
 */

// Global test timeout
jest.setTimeout(10000);

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.OPENAI_API_KEY = 'test-api-key';

// Global mocks for browser-related functionality
global.mockBrowser = {
  newPage: jest.fn(),
  close: jest.fn()
};

// Mock Puppeteer
jest.mock('puppeteer', () => ({
  launch: jest.fn().mockResolvedValue(global.mockBrowser)
}));

// Mock file system operations
jest.mock('fs', () => ({
  existsSync: jest.fn().mockReturnValue(true),
  readFileSync: jest.fn().mockReturnValue('mock file content'),
  writeFileSync: jest.fn(),
  readdirSync: jest.fn().mockReturnValue([])
}));

// Mock child_process
jest.mock('child_process', () => ({
  spawn: jest.fn().mockReturnValue({
    stdout: {
      on: jest.fn()
    },
    stderr: {
      on: jest.fn()
    },
    stdin: {
      write: jest.fn()
    },
    on: jest.fn()
  })
}));

// Global test utilities
global.createMockResponse = (data) => ({
  content: [
    {
      type: 'text',
      text: JSON.stringify(data, null, 2)
    }
  ]
});

global.createMockError = (message) => new Error(message);

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});
