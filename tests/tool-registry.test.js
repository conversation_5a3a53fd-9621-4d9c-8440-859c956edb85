/**
 * Unit Tests for Tool Registry
 * Tests the central tool registration and execution system
 */

const { ToolRegistry } = require('../build/tool-registry.js');

// Mock all handler classes
jest.mock('../build/handlers/twitter-handlers.js');
jest.mock('../build/handlers/reddit-handlers.js');
jest.mock('../build/handlers/browser-handlers.js');
jest.mock('../build/handlers/ai-handlers.js');
jest.mock('../build/handlers/target-handlers.js');
jest.mock('../build/handlers/enhanced-handlers.js');

// Mock component classes
jest.mock('../build/scraper.js');
jest.mock('../build/reddit-scraper.js');
jest.mock('../build/analyzer.js');
jest.mock('../build/target-manager.js');
jest.mock('../build/ai-discovery.js');
jest.mock('../build/component-manager.js');

describe('ToolRegistry', () => {
    let toolRegistry;

    beforeEach(() => {
        jest.clearAllMocks();
        toolRegistry = new ToolRegistry();
    });

    describe('Tool Registration', () => {
        it('should register all 49 tools', () => {
            const tools = toolRegistry.getTools();
            expect(tools).toHaveLength(49);
        });

        it('should register Twitter tools', () => {
            const tools = toolRegistry.getTools();
            const twitterTools = tools.filter(tool => ['get_comments', 'get_key_contributors', 'get_trending_topics', 'analyze_sentiment', 'get_trending_keywords', 'analyze_keywords']
                .includes(tool.name)
            );
            expect(twitterTools).toHaveLength(6);
        });

        it('should register Reddit tools', () => {
            const tools = toolRegistry.getTools();
            const redditTools = tools.filter(tool => ['get_subreddit_posts', 'get_reddit_comments', 'get_reddit_trending', 'search_reddit', 'analyze_reddit_trends', 'analyze_reddit_comments']
                .includes(tool.name)
            );
            expect(redditTools).toHaveLength(6);
        });

        it('should register Browser tools', () => {
            const tools = toolRegistry.getTools();
            const browserTools = tools.filter(tool => ['navigate', 'screenshot', 'click', 'fill', 'scroll', 'analyze_page_visually']
                .includes(tool.name)
            );
            expect(browserTools).toHaveLength(6);
        });

        it('should register AI tools', () => {
            const tools = toolRegistry.getTools();
            const aiTools = tools.filter(tool => ['create_chat_session', 'chat_with_agent', '@chat', 'get_session_info', 'export_report',
                    'list_fabric_patterns', 'apply_fabric_pattern', 'chain_fabric_patterns', 'create_custom_pattern',
                    'analyze_with_fabric', 'batch_apply_pattern'
                ]
                .includes(tool.name)
            );
            expect(aiTools).toHaveLength(11);
        });

        it('should register Target Management tools', () => {
            const tools = toolRegistry.getTools();
            const targetTools = tools.filter(tool => ['list_targets', 'get_target_categories', 'add_influencer', 'add_subreddit', 'remove_target',
                    'get_top_targets', 'batch_analyze_targets', 'discover_new_targets', 'get_recent_discoveries',
                    'promote_discovered_target', 'update_target_relevance'
                ]
                .includes(tool.name)
            );
            expect(targetTools).toHaveLength(11);
        });

        it('should register Enhanced tools', () => {
            const tools = toolRegistry.getTools();
            const enhancedTools = tools.filter(tool => ['get_system_health', 'get_kpi_snapshot', 'get_blocking_stats', 'get_enhancement_stats',
                    'generate_enhancement_proposal', 'get_research_summary', 'send_notification',
                    'get_dashboard_data', 'test_notification_channel'
                ]
                .includes(tool.name)
            );
            expect(enhancedTools).toHaveLength(9);
        });
    });

    describe('Tool Execution', () => {
        it('should route Twitter tools to TwitterHandlers', async() => {
            const mockTwitterHandlers = {
                getComments: jest.fn().mockResolvedValue({ content: [{ type: 'text', text: 'mock result' }] })
            };

            // Mock the handler initialization
            toolRegistry.handlers.set('twitter', mockTwitterHandlers);

            const result = await toolRegistry.executeHandler('get_comments', { username: 'test' });

            expect(mockTwitterHandlers.getComments).toHaveBeenCalledWith({ username: 'test' });
            expect(result.content[0].text).toBe('mock result');
        });

        it('should route AI tools to AIHandlers', async() => {
            const mockAIHandlers = {
                chatShorthand: jest.fn().mockResolvedValue({ content: [{ type: 'text', text: 'AI response' }] })
            };

            toolRegistry.handlers.set('ai', mockAIHandlers);

            const result = await toolRegistry.executeHandler('@chat', { message: 'Hello' });

            expect(mockAIHandlers.chatShorthand).toHaveBeenCalledWith({ message: 'Hello' });
            expect(result.content[0].text).toBe('AI response');
        });

        it('should throw error for unknown tools', async() => {
            await expect(toolRegistry.executeHandler('unknown_tool', {}))
                .rejects.toThrow('Unknown tool: unknown_tool');
        });

        it('should throw error for missing handlers', async() => {
            // Clear handlers to simulate missing handler
            toolRegistry.handlers.clear();

            await expect(toolRegistry.executeHandler('get_comments', {}))
                .rejects.toThrow('Handler not found for category: twitter');
        });

        it('should throw error for missing methods', async() => {
            const mockHandler = {}; // Handler without the required method
            toolRegistry.handlers.set('twitter', mockHandler);

            await expect(toolRegistry.executeHandler('get_comments', {}))
                .rejects.toThrow('Method getComments not found in twitter handler');
        });
    });

    describe('Tool Definitions', () => {
        it('should have proper tool structure', () => {
            const tools = toolRegistry.getTools();

            tools.forEach(tool => {
                expect(tool).toHaveProperty('name');
                expect(tool).toHaveProperty('description');
                expect(tool).toHaveProperty('inputSchema');
                expect(typeof tool.name).toBe('string');
                expect(typeof tool.description).toBe('string');
                expect(tool.inputSchema).toBeDefined();
            });
        });

        it('should have unique tool names', () => {
            const tools = toolRegistry.getTools();
            const names = tools.map(tool => tool.name);
            const uniqueNames = [...new Set(names)];

            expect(names.length).toBe(uniqueNames.length);
        });
    });

    describe('Handler Mapping', () => {
        it('should correctly execute tools without throwing mapping errors', async() => {
            const tools = toolRegistry.getTools();

            // Test a few representative tools to ensure mapping works
            const testTools = ['get_comments', 'list_fabric_patterns', 'list_targets'];

            for (const toolName of testTools) {
                const tool = tools.find(t => t.name === toolName);
                expect(tool).toBeDefined();

                // Mock the handler for this test
                const mockHandler = {
                    [toolName.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()).replace(/^[a-z]/, c => c.toLowerCase())]: jest.fn().mockResolvedValue({ content: [{ type: 'text', text: 'test' }] })
                };

                // The actual mapping test is implicit in executeHandler working
                expect(tool.name).toBe(toolName);
            }
        });
    });
});