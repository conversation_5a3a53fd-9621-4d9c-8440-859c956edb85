/**
 * Unit Tests for Twitter Handlers
 * Tests the modular Twitter handler functionality
 */

const { TwitterHandlers } = require('../../build/handlers/twitter-handlers.js');

// Mock dependencies
const mockScraper = {
  getComments: jest.fn(),
  getKeyContributors: jest.fn(),
  getTrendingTopics: jest.fn()
};

const mockAnalyzer = {
  analyzeSentiment: jest.fn(),
  getTrendingKeywords: jest.fn(),
  analyzeKeywords: jest.fn()
};

describe('TwitterHandlers', () => {
  let twitterHandlers;

  beforeEach(() => {
    twitterHandlers = new TwitterHandlers(mockScraper, mockAnalyzer);
    jest.clearAllMocks();
  });

  describe('getComments', () => {
    it('should successfully get comments for a user', async () => {
      const mockComments = [
        { id: '1', text: 'Great post!', author: 'user1' },
        { id: '2', text: 'Interesting perspective', author: 'user2' }
      ];
      
      mockScraper.getComments.mockResolvedValue(mockComments);

      const result = await twitterHandlers.getComments({
        username: 'testuser',
        limit: 10,
        includeReplies: true
      });

      expect(mockScraper.getComments).toHaveBeenCalledWith('testuser', 10, true);
      expect(result.content[0].text).toContain(JSON.stringify(mockComments, null, 2));
    });

    it('should handle errors gracefully', async () => {
      mockScraper.getComments.mockRejectedValue(new Error('API Error'));

      await expect(twitterHandlers.getComments({
        username: 'testuser',
        limit: 10
      })).rejects.toThrow('API Error');
    });
  });

  describe('getKeyContributors', () => {
    it('should get key contributors for a topic', async () => {
      const mockContributors = [
        { username: 'expert1', influence_score: 0.95 },
        { username: 'expert2', influence_score: 0.87 }
      ];
      
      mockScraper.getKeyContributors.mockResolvedValue(mockContributors);

      const result = await twitterHandlers.getKeyContributors({
        topic: 'AI',
        limit: 5
      });

      expect(mockScraper.getKeyContributors).toHaveBeenCalledWith('AI', 5);
      expect(result.content[0].text).toContain(JSON.stringify(mockContributors, null, 2));
    });
  });

  describe('getTrendingTopics', () => {
    it('should get trending topics with optional filters', async () => {
      const mockTopics = [
        { topic: 'AI', volume: 15000, sentiment: 0.7 },
        { topic: 'Crypto', volume: 12000, sentiment: 0.3 }
      ];
      
      mockScraper.getTrendingTopics.mockResolvedValue(mockTopics);

      const result = await twitterHandlers.getTrendingTopics({
        category: 'technology',
        location: 'US'
      });

      expect(mockScraper.getTrendingTopics).toHaveBeenCalledWith('technology', 'US');
      expect(result.content[0].text).toContain(JSON.stringify(mockTopics, null, 2));
    });
  });

  describe('analyzeSentiment', () => {
    it('should analyze sentiment of posts', async () => {
      const mockSentiment = {
        overall_sentiment: 0.6,
        positive_count: 15,
        negative_count: 5,
        neutral_count: 10
      };
      
      mockAnalyzer.analyzeSentiment.mockResolvedValue(mockSentiment);

      const result = await twitterHandlers.analyzeSentiment({
        posts: ['Great news!', 'This is terrible'],
        granularity: 'detailed'
      });

      expect(mockAnalyzer.analyzeSentiment).toHaveBeenCalledWith(['Great news!', 'This is terrible'], 'detailed');
      expect(result.content[0].text).toContain(JSON.stringify(mockSentiment, null, 2));
    });
  });

  describe('getTrendingKeywords', () => {
    it('should get trending keywords', async () => {
      const mockKeywords = [
        { keyword: 'AI', volume: 50000, growth: 0.25 },
        { keyword: 'blockchain', volume: 30000, growth: 0.15 }
      ];
      
      mockAnalyzer.getTrendingKeywords.mockResolvedValue(mockKeywords);

      const result = await twitterHandlers.getTrendingKeywords({
        platform: 'twitter',
        timeframe: '24h',
        limit: 10
      });

      expect(mockAnalyzer.getTrendingKeywords).toHaveBeenCalledWith('twitter', '24h', 10);
      expect(result.content[0].text).toContain(JSON.stringify(mockKeywords, null, 2));
    });
  });

  describe('analyzeKeywords', () => {
    it('should analyze keyword performance', async () => {
      const mockAnalysis = {
        keywords: ['AI', 'ML'],
        performance: {
          AI: { engagement: 0.8, reach: 100000 },
          ML: { engagement: 0.6, reach: 75000 }
        }
      };
      
      mockAnalyzer.analyzeKeywords.mockResolvedValue(mockAnalysis);

      const result = await twitterHandlers.analyzeKeywords({
        keywords: ['AI', 'ML'],
        platforms: ['twitter'],
        timeframe: '7d'
      });

      expect(mockAnalyzer.analyzeKeywords).toHaveBeenCalledWith(['AI', 'ML'], ['twitter'], '7d');
      expect(result.content[0].text).toContain(JSON.stringify(mockAnalysis, null, 2));
    });
  });
});
