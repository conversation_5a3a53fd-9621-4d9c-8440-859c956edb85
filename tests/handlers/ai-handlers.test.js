/**
 * Unit Tests for AI Handlers
 * Tests the modular AI handler functionality including chat and Fabric integration
 */

const { AIHandlers } = require('../../build/handlers/ai-handlers.js');

// Mock dependencies
const mockAgentBridge = {
  createChatSession: jest.fn(),
  chat: jest.fn(),
  getSessionInfo: jest.fn(),
  exportReport: jest.fn()
};

const mockFabricBridge = {
  listPatterns: jest.fn(),
  applyPattern: jest.fn(),
  chainPatterns: jest.fn(),
  createCustomPattern: jest.fn(),
  analyzeContent: jest.fn(),
  batchApplyPattern: jest.fn()
};

// Mock the bridge getters
jest.mock('../../build/agent-bridge.js', () => ({
  getAgentBridge: () => mockAgentBridge
}));

jest.mock('../../build/fabric-bridge.js', () => ({
  getFabricBridge: () => mockFabricBridge
}));

describe('AIHandlers', () => {
  let aiHandlers;

  beforeEach(() => {
    aiHandlers = new AIHandlers();
    jest.clearAllMocks();
  });

  describe('createChatSession', () => {
    it('should create a new chat session', async () => {
      const mockSessionId = 'chat_20250722_123456';
      mockAgentBridge.createChatSession.mockResolvedValue(mockSessionId);

      const result = await aiHandlers.createChatSession({});

      expect(mockAgentBridge.createChatSession).toHaveBeenCalled();
      expect(result.content[0].text).toContain(`Created chat session: ${mockSessionId}`);
    });

    it('should handle session creation errors', async () => {
      mockAgentBridge.createChatSession.mockRejectedValue(new Error('Session creation failed'));

      await expect(aiHandlers.createChatSession({})).rejects.toThrow('Session creation failed');
    });
  });

  describe('chatWithAgent', () => {
    it('should chat with existing session', async () => {
      const mockResponse = {
        session_id: 'chat_123',
        response: 'Hello! How can I help you?',
        metadata: { context_used: [] }
      };
      
      mockAgentBridge.chat.mockResolvedValue(mockResponse);

      const result = await aiHandlers.chatWithAgent({
        sessionId: 'chat_123',
        message: 'Hello'
      });

      expect(mockAgentBridge.chat).toHaveBeenCalledWith('chat_123', 'Hello');
      expect(result.content[0].text).toContain(JSON.stringify(mockResponse, null, 2));
    });

    it('should create new session if none provided', async () => {
      const mockSessionId = 'chat_new_123';
      const mockResponse = {
        session_id: mockSessionId,
        response: 'Hello! How can I help you?',
        metadata: { context_used: [] }
      };
      
      mockAgentBridge.createChatSession.mockResolvedValue(mockSessionId);
      mockAgentBridge.chat.mockResolvedValue(mockResponse);

      const result = await aiHandlers.chatWithAgent({
        message: 'Hello'
      });

      expect(mockAgentBridge.createChatSession).toHaveBeenCalled();
      expect(mockAgentBridge.chat).toHaveBeenCalledWith(mockSessionId, 'Hello');
    });
  });

  describe('listFabricPatterns', () => {
    it('should list available Fabric patterns', async () => {
      const mockPatterns = [
        { name: 'analyze_social_sentiment', category: 'analysis' },
        { name: 'extract_trending_insights', category: 'extraction' }
      ];
      
      mockFabricBridge.listPatterns.mockResolvedValue(mockPatterns);

      const result = await aiHandlers.listFabricPatterns({
        category: 'analysis'
      });

      expect(mockFabricBridge.listPatterns).toHaveBeenCalledWith('analysis');
      expect(result.content[0].text).toContain(JSON.stringify(mockPatterns, null, 2));
    });
  });

  describe('applyFabricPattern', () => {
    it('should apply Fabric pattern successfully', async () => {
      const mockResult = {
        success: true,
        pattern: 'analyze_social_sentiment',
        output: 'Sentiment analysis results...',
        executionTime: 1500
      };
      
      mockFabricBridge.applyPattern.mockResolvedValue(mockResult);

      const result = await aiHandlers.applyFabricPattern({
        patternName: 'analyze_social_sentiment',
        content: 'This is great news!',
        model: 'gpt-4',
        temperature: 0.7
      });

      expect(mockFabricBridge.applyPattern).toHaveBeenCalledWith(
        'analyze_social_sentiment',
        'This is great news!',
        'gpt-4',
        0.7
      );
      expect(result.content[0].text).toContain('**Pattern:** analyze_social_sentiment');
      expect(result.content[0].text).toContain('**Execution Time:** 1500ms');
      expect(result.content[0].text).toContain('Sentiment analysis results...');
    });

    it('should handle Fabric pattern errors', async () => {
      const mockResult = {
        success: false,
        pattern: 'invalid_pattern',
        error: 'Pattern not found'
      };
      
      mockFabricBridge.applyPattern.mockResolvedValue(mockResult);

      const result = await aiHandlers.applyFabricPattern({
        patternName: 'invalid_pattern',
        content: 'Test content'
      });

      expect(result.content[0].text).toContain('**Error applying pattern invalid_pattern:**');
      expect(result.content[0].text).toContain('Pattern not found');
    });
  });

  describe('chainFabricPatterns', () => {
    it('should chain multiple Fabric patterns', async () => {
      const mockResults = [
        { success: true, output: 'First pattern result' },
        { success: true, output: 'Second pattern result' }
      ];
      
      mockFabricBridge.chainPatterns.mockResolvedValue(mockResults);

      const result = await aiHandlers.chainFabricPatterns({
        patterns: ['extract_trending_insights', 'generate_social_strategy'],
        content: 'Social media data...',
        model: 'gpt-4'
      });

      expect(mockFabricBridge.chainPatterns).toHaveBeenCalledWith(
        ['extract_trending_insights', 'generate_social_strategy'],
        'Social media data...',
        'gpt-4'
      );
      expect(result.content[0].text).toContain(JSON.stringify(mockResults, null, 2));
    });
  });

  describe('analyzeWithFabric', () => {
    it('should perform quick analysis with Fabric', async () => {
      const mockResult = {
        success: true,
        pattern: 'auto_selected_pattern',
        output: 'Analysis results...'
      };
      
      mockFabricBridge.analyzeContent.mockResolvedValue(mockResult);

      const result = await aiHandlers.analyzeWithFabric({
        content: 'Content to analyze',
        analysisType: 'sentiment'
      });

      expect(mockFabricBridge.analyzeContent).toHaveBeenCalledWith(
        'Content to analyze',
        'sentiment'
      );
      expect(result.content[0].text).toContain(JSON.stringify(mockResult, null, 2));
    });
  });

  describe('batchApplyPattern', () => {
    it('should apply pattern to multiple content items', async () => {
      const mockResults = [
        { content: 'Content 1', result: 'Analysis 1' },
        { content: 'Content 2', result: 'Analysis 2' }
      ];
      
      mockFabricBridge.batchApplyPattern.mockResolvedValue(mockResults);

      const result = await aiHandlers.batchApplyPattern({
        patternName: 'analyze_social_sentiment',
        contentItems: ['Content 1', 'Content 2'],
        model: 'gpt-4'
      });

      expect(mockFabricBridge.batchApplyPattern).toHaveBeenCalledWith(
        'analyze_social_sentiment',
        ['Content 1', 'Content 2'],
        'gpt-4'
      );
      expect(result.content[0].text).toContain(JSON.stringify(mockResults, null, 2));
    });
  });
});
