version: '3.8'

services:
  mcp-x-scraper:
    build: .
    container_name: mcp-x-scraper
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DEFAULT_MODEL=${DEFAULT_MODEL:-gpt-4.1}
      - DEFAULT_VENDOR=${DEFAULT_VENDOR:-OpenAI}
      - NODE_ENV=production
    volumes:
      # Mount screenshots directory
      - ./screenshots:/app/screenshots
      # Mount Fabric patterns (optional custom patterns)
      - ./custom-patterns:/root/.config/fabric/patterns/custom:ro
    ports:
      - "3000:3000"  # If web interface is added
    restart: unless-stopped
    networks:
      - mcp-network
    
  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    container_name: mcp-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge

volumes:
  redis-data:
    driver: local