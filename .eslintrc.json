{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-floating-promises": "off", "@typescript-eslint/no-misused-promises": "off", "@typescript-eslint/await-thenable": "off", "@typescript-eslint/no-unnecessary-type-assertion": "warn", "@typescript-eslint/prefer-nullish-coalescing": "off", "@typescript-eslint/prefer-optional-chain": "warn", "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-return": "off", "@typescript-eslint/no-unsafe-argument": "off", "@typescript-eslint/restrict-template-expressions": "off", "@typescript-eslint/require-await": "off", "no-console": "off", "no-case-declarations": "off", "no-useless-escape": "warn", "prefer-const": "error", "no-var": "error", "eqeqeq": ["error", "always"], "curly": ["error", "all"]}, "env": {"node": true, "es2020": true}, "ignorePatterns": ["build/", "node_modules/", "*.js", "scripts/", "tests/", "src/index-modular.ts"]}