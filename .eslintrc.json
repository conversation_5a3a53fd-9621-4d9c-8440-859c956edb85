{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "rules": {"@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/no-misused-promises": "error", "@typescript-eslint/await-thenable": "error", "@typescript-eslint/no-unnecessary-type-assertion": "warn", "@typescript-eslint/prefer-nullish-coalescing": "warn", "@typescript-eslint/prefer-optional-chain": "warn", "no-console": ["warn", {"allow": ["warn", "error", "info"]}], "prefer-const": "error", "no-var": "error", "eqeqeq": ["error", "always"], "curly": ["error", "all"]}, "env": {"node": true, "es2020": true}, "ignorePatterns": ["build/", "node_modules/", "*.js", "scripts/"]}