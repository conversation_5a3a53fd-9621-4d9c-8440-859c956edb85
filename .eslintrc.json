{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@typescript-eslint/recommended-requiring-type-checking"], "rules": {"@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/no-misused-promises": "error", "@typescript-eslint/await-thenable": "off", "@typescript-eslint/no-unnecessary-type-assertion": "warn", "@typescript-eslint/prefer-nullish-coalescing": "off", "@typescript-eslint/prefer-optional-chain": "warn", "@typescript-eslint/no-unsafe-assignment": "warn", "@typescript-eslint/no-unsafe-member-access": "warn", "@typescript-eslint/no-unsafe-call": "warn", "@typescript-eslint/no-unsafe-return": "warn", "@typescript-eslint/no-unsafe-argument": "warn", "@typescript-eslint/restrict-template-expressions": "off", "@typescript-eslint/require-await": "off", "no-console": "off", "no-case-declarations": "off", "no-useless-escape": "warn", "prefer-const": "error", "no-var": "error", "eqeqeq": ["error", "always"], "curly": ["error", "all"]}, "env": {"node": true, "es2020": true}, "ignorePatterns": ["build/", "node_modules/", "*.js", "scripts/", "tests/", "src/index-modular.ts"]}