# MCP X/Reddit Scraper - Project Directory Structure

```
mcp-x-reddit-scraper/
├── 📁 .github/
│   └── workflows/
│       └── publish.yml                    # GitHub Actions CI/CD workflow
├── 📁 .husky/                            # Git hooks (Husky configuration)
│   ├── _/                                # Husky internal files
│   └── pre-commit                        # Pre-commit hook
├── 📁 Commands/                          # CLI command scripts
│   ├── .gitkeep
│   ├── analyze-targets.js                # Target analysis and insights
│   ├── cleanup.js                        # Log and temporary file cleanup
│   ├── github-manager.js                 # GitHub repository management
│   ├── health-check.js                   # System health verification
│   ├── mcp-cli.js                        # Master command interface
│   ├── quick-scrape.js                   # Test scraping functionality
│   └── status.js                         # System status and metrics
├── 📁 code-review/                       # Code review templates and standards
│   ├── .gitkeep
│   ├── CHECKLIST.md                      # Quick reference for reviewers
│   ├── REVIEW_TEMPLATE.md                # Comprehensive review checklist
│   └── reviews/                          # Individual review records
│       └── 2025-01-21-safe-scraping.md   # Example review
├── 📁 config/                            # Configuration files
│   └── targets.json                      # 70+ pre-configured targets (8 categories)
├── 📁 docs/                              # Documentation directory
├── 📁 hooks/                             # Git hooks system
│   ├── .gitkeep
│   ├── hook-config.json                  # Centralized hook configuration
│   ├── hook-manager.js                   # Hook configuration and management
│   ├── install-hooks.js                  # Hook installation utility
│   ├── post-install                     # Post-install setup hook
│   ├── pre-commit                       # Code quality checks
│   └── pre-push                         # Safety checks before push
├── 📁 logs/                             # Centralized logging directory
│   └── .gitkeep                         # (All log files written here)
├── 📁 screenshots/                       # Browser automation screenshots
├── 📁 scripts/                          # Installation and setup scripts
│   ├── install.js                       # MCP server installation
│   └── verify.js                        # System verification
├── 📁 src/                              # Source code (TypeScript)
│   ├── 📁 agent/                        # Python AI agent components
│   │   ├── __init__.py
│   │   ├── chat_agent.py                # Conversational AI interface
│   │   ├── fabric_integration.py        # Fabric pattern integration
│   │   ├── models.py                    # Pydantic data models
│   │   ├── report_generator.py          # AI report generation
│   │   └── scraping_coordinator.py      # Multi-source coordination
│   ├── agent-bridge.ts                  # TypeScript-Python bridge
│   ├── ai-discovery.ts                  # AI-powered target discovery
│   ├── analyzer.ts                      # Data analysis utilities
│   ├── circuit-breaker.ts               # Error recovery patterns
│   ├── fabric-bridge.ts                 # Fabric framework integration
│   ├── index.ts                         # Main MCP server (34+ tools)
│   ├── python_bridge.py                 # Python bridge helper
│   ├── reddit-scraper.ts               # Reddit scraping tools
│   ├── request-monitor.ts               # Rate limiting and monitoring
│   ├── safe-browser-manager.ts          # Browser management with anti-detection
│   ├── safe-scraper-config.ts           # Safety configuration management
│   ├── safe-scraping-orchestrator.ts    # Main safety coordinator
│   ├── scraper.ts                       # Core scraping functionality
│   ├── target-manager.ts                # Target management system
│   └── types.ts                         # TypeScript type definitions
├── 📁 tests/                            # Test directory
├── 📁 venv/                             # Python virtual environment
├── .DS_Store                            # macOS system file
├── .env                                 # Environment variables (API keys)
├── .eslintrc.json                       # ESLint configuration
├── .gitignore                           # Git ignore patterns
├── .mcp.json                            # MCP server configuration
├── .prettierrc                          # Prettier formatting configuration
├── CHANGELOG.md                         # Version history and changes
├── CLAUDE.md                            # Claude Code project context
├── Dockerfile                           # Docker container configuration
├── PROJECT_STRUCTURE.md                 # This file - project structure
├── README.md                            # Project documentation
├── deploy.sh                            # Docker deployment script
├── docker-compose.yml                   # Docker Compose configuration
├── fabric.tar.gz                        # Daniel Miessler's Fabric framework
├── live-monitoring-test.js              # Live data collection testing
├── live-scraping-monitor.js             # Live scraping monitor
├── mcp.json                             # MCP configuration
├── package-lock.json                    # NPM lock file
├── package.json                         # Project dependencies and scripts
├── requirements.txt                     # Python dependencies
├── safe-scraping-test.js                # Safe scraping system test
├── scraping-monitor.json                # Real-time monitoring state
├── setup.sh                             # Initial setup script
├── simple-test.js                       # Simple functionality test
├── test-*.js                           # Various test files
├── tsconfig.json                        # TypeScript configuration
└── 📄 Generated Files (excluded from repo):
    ├── 📁 build/                        # TypeScript compilation output
    ├── 📁 logs/                         # Runtime logs
    │   ├── live-monitoring.log
    │   ├── safe-scraping-test.log
    │   └── scraping-monitor.json
    ├── 📁 node_modules/                 # NPM dependencies
    └── 📁 screenshots/                   # Runtime screenshots
```

## 📊 Project Statistics

- **Total Files**: ~80+ source files
- **Languages**: TypeScript (primary), Python, JavaScript, Shell
- **Key Directories**: 12 main directories
- **CLI Commands**: 6 specialized commands
- **Git Hooks**: 3 active hooks
- **MCP Tools**: 34+ registered tools
- **Target Categories**: 8 categories with 70+ targets
- **Documentation**: 5 comprehensive markdown files

## 🏗️ Architecture Overview

### **Frontend Layer** (CLI Commands)
- `Commands/` - User-facing command-line utilities
- `hooks/` - Development workflow automation

### **Core Layer** (MCP Server)
- `src/index.ts` - Main MCP server with tool registration
- `src/*-scraper.ts` - Platform-specific scraping logic
- `src/safe-*.ts` - Comprehensive safety infrastructure

### **AI Layer** (Intelligence)
- `src/agent/` - Python-based AI analysis
- `src/ai-discovery.ts` - Target discovery algorithms
- `src/fabric-bridge.ts` - Pattern-based analysis

### **Data Layer** (Configuration & Storage)
- `config/` - Static configuration and targets
- `logs/` - Runtime data and monitoring
- `screenshots/` - Visual debugging artifacts

### **Infrastructure Layer** (Deployment)
- `.github/workflows/` - CI/CD automation
- `Dockerfile` + `docker-compose.yml` - Containerization
- `scripts/` - Setup and installation utilities