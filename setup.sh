#!/bin/bash

echo "🚀 Setting up X & Reddit Scraper MCP Server with AI Agent + Fabric Integration..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

# Check if pip is installed
if ! command -v pip &> /dev/null && ! command -v pip3 &> /dev/null; then
    echo "❌ pip is not installed. Please install pip first."
    exit 1
fi

# Check if Go is installed for Fabric
if ! command -v go &> /dev/null; then
    echo "⚠️  Go is not installed. Installing Go for Fabric integration..."
    if command -v brew &> /dev/null; then
        brew install go
    else
        echo "❌ Please install Go manually to use Fabric features"
    fi
fi

echo "✅ Prerequisites check passed"

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

# Install Python dependencies
echo "🐍 Installing Python dependencies..."
if command -v pip3 &> /dev/null; then
    pip3 install -r requirements.txt
else
    pip install -r requirements.txt
fi

# Build TypeScript
echo "🔨 Building TypeScript..."
npm run build

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📄 Creating .env file from example..."
    cp .env.example .env
    echo "⚠️  Please edit .env file and add your OpenAI API key"
else
    echo "✅ .env file already exists"
fi

# Create screenshots directory
mkdir -p screenshots

# Make Python bridge executable
chmod +x src/python_bridge.py

# Install Fabric
echo "🧵 Installing Fabric..."
if command -v go &> /dev/null; then
    go install github.com/danielmiessler/fabric@latest
    echo "✅ Fabric installed successfully"
else
    echo "⚠️  Skipping Fabric installation - Go not available"
fi

# Create Fabric config directory
mkdir -p ~/.config/fabric

# Create custom patterns directory if it doesn't exist
if [ -d ~/.config/fabric/patterns ]; then
    echo "✅ Fabric patterns directory exists"
else
    echo "📁 Creating Fabric patterns directory..."
    mkdir -p ~/.config/fabric/patterns
fi

echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file and add your OpenAI API key"
echo "2. (Optional) Setup Fabric patterns: ~/go/bin/fabric --setup"
echo "3. Add this server to your MCP configuration:"
echo "   {"
echo "     \"mcpServers\": {"
echo "       \"x-scraper\": {"
echo "         \"command\": \"node\","
echo "         \"args\": [\"$(pwd)/build/index.js\"]"
echo "       }"
echo "     }"
echo "   }"
echo ""
echo "3. Test the installation:"
echo "   node build/index.js"