{"name": "mcp-x-reddit-scraper", "version": "0.3.0", "description": "MCP server for safe scraping of X (Twitter) and Reddit content with IP protection, AI analysis, and Fabric integration", "main": "build/index.js", "types": "build/index.d.ts", "type": "module", "bin": {"mcp-x-reddit-scraper": "build/index.js"}, "scripts": {"build": "tsc", "start": "node build/index.js", "dev": "node --watch build/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:handlers": "jest tests/handlers/", "prepare": "husky", "install-mcp": "node scripts/install.js", "verify": "node scripts/verify.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "prettier": "prettier --write \"src/**/*.{ts,js,json}\" \"*.{json,md}\"", "prettier:check": "prettier --check \"src/**/*.{ts,js,json}\" \"*.{json,md}\"", "hooks:install": "node hooks/install-hooks.js", "hooks:status": "node hooks/hook-manager.js status", "hooks:test": "node hooks/hook-manager.js test", "postinstall": "node hooks/hook-manager.js install 2>/dev/null || true"}, "keywords": ["mcp", "model-context-protocol", "twitter", "x", "reddit", "scraping", "trending", "analysis", "sentiment", "ai-agent", "fabric", "claude"], "author": "MCP Community", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/username/mcp-x-reddit-scraper.git"}, "bugs": {"url": "https://github.com/username/mcp-x-reddit-scraper/issues"}, "homepage": "https://github.com/username/mcp-x-reddit-scraper#readme", "engines": {"node": ">=18.0.0"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "axios": "^1.6.0", "canvas": "^3.1.2", "cheerio": "^1.0.0-rc.12", "dotenv": "^17.2.0", "node-cache": "^5.1.2", "prom-client": "^15.1.3", "puppeteer": "^23.0.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-proxy": "^1.0.2", "puppeteer-extra-plugin-stealth": "^2.11.2", "sharp": "^0.34.3", "tesseract.js": "^6.0.1"}, "devDependencies": {"@octokit/rest": "^22.0.0", "@types/jest": "^30.0.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "claude-code-boost": "^0.3.0", "eslint": "^8.0.0", "husky": "^9.1.7", "jest": "^30.0.5", "lint-staged": "^16.1.2", "prettier": "^3.0.0", "ts-jest": "^29.4.0", "typescript": "^5.3.0"}, "lint-staged": {"src/**/*.{ts,js}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}