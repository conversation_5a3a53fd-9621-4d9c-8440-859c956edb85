#!/usr/bin/env node

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packageRoot = resolve(__dirname, '..');

/**
 * MCP X & Reddit Scraper Verification Script
 * Tests installation and functionality of the MCP server
 */

class MCPVerifier {
  constructor() {
    this.serverName = 'x-reddit-scraper';
    this.serverPath = resolve(packageRoot, 'build', 'index.js');
    this.testTimeout = 15000; // 15 seconds
    this.testsPassed = 0;
    this.testsTotal = 0;
  }

  log(message) {
    console.log(`🔍 ${message}`);
  }

  error(message) {
    console.error(`❌ ${message}`);
  }

  success(message) {
    console.log(`✅ ${message}`);
  }

  warn(message) {
    console.log(`⚠️  ${message}`);
  }

  async runTest(testName, testFn) {
    this.testsTotal++;
    this.log(`Running: ${testName}`);
    
    try {
      const result = await testFn();
      if (result !== false) {
        this.success(`${testName} - PASSED`);
        this.testsPassed++;
        return true;
      }
    } catch (error) {
      this.error(`${testName} - FAILED: ${error.message}`);
    }
    return false;
  }

  async testPrerequisites() {
    return this.runTest('Prerequisites Check', async () => {
      // Check Node.js version
      const nodeVersion = process.version;
      if (!nodeVersion.match(/^v(18|19|20|21|22)\./)) {
        throw new Error(`Node.js version ${nodeVersion} not supported. Requires 18+`);
      }

      // Check if server build exists
      if (!existsSync(this.serverPath)) {
        throw new Error('Server build not found. Run "npm run build" first.');
      }

      // Check for required files
      const requiredFiles = ['package.json', 'tsconfig.json', '.mcp.json'];
      for (const file of requiredFiles) {
        if (!existsSync(resolve(packageRoot, file))) {
          throw new Error(`Required file missing: ${file}`);
        }
      }

      return true;
    });
  }

  async testServerStartup() {
    return this.runTest('Server Startup', () => {
      return new Promise((resolve, reject) => {
        const serverProcess = spawn('node', [this.serverPath], {
          stdio: ['pipe', 'pipe', 'pipe'],
          cwd: packageRoot
        });

        let stdout = '';
        let stderr = '';
        
        serverProcess.stdout.on('data', (data) => {
          stdout += data.toString();
        });
        
        serverProcess.stderr.on('data', (data) => {
          stderr += data.toString();
        });

        // Test if server responds to initialization
        setTimeout(() => {
          const initMessage = {
            jsonrpc: "2.0",
            id: 1,
            method: "initialize",
            params: {
              protocolVersion: "2024-11-05",
              capabilities: {},
              clientInfo: { name: "test", version: "1.0" }
            }
          };
          
          serverProcess.stdin.write(JSON.stringify(initMessage) + '\n');
        }, 1000);

        const timeout = setTimeout(() => {
          serverProcess.kill();
          reject(new Error('Server startup timeout'));
        }, this.testTimeout);

        serverProcess.on('close', (code) => {
          clearTimeout(timeout);
          if (stdout.includes('result') || stdout.includes('capabilities')) {
            resolve(true);
          } else {
            reject(new Error(`Server failed to initialize. Exit code: ${code}. Stderr: ${stderr}`));
          }
        });

        serverProcess.on('error', (error) => {
          clearTimeout(timeout);
          reject(new Error(`Failed to start server: ${error.message}`));
        });
      });
    });
  }

  async testToolsListing() {
    return this.runTest('Tools Listing', () => {
      return new Promise((resolve, reject) => {
        const serverProcess = spawn('node', [this.serverPath], {
          stdio: ['pipe', 'pipe', 'pipe'],
          cwd: packageRoot
        });

        let output = '';
        
        serverProcess.stdout.on('data', (data) => {
          output += data.toString();
        });

        setTimeout(() => {
          const toolsMessage = {
            jsonrpc: "2.0",
            id: 2,
            method: "tools/list",
            params: {}
          };
          
          serverProcess.stdin.write(JSON.stringify(toolsMessage) + '\n');
        }, 1000);

        const timeout = setTimeout(() => {
          serverProcess.kill();
          reject(new Error('Tools listing timeout'));
        }, this.testTimeout);

        serverProcess.on('close', () => {
          clearTimeout(timeout);
          
          if (output.includes('get_comments') && 
              output.includes('search_reddit') && 
              output.includes('apply_fabric_pattern') && 
              output.includes('create_chat_session')) {
            resolve(true);
          } else {
            reject(new Error('Expected tools not found in server response'));
          }
        });

        serverProcess.on('error', (error) => {
          clearTimeout(timeout);
          reject(new Error(`Failed to test tools: ${error.message}`));
        });
      });
    });
  }

  async testEnvironmentVariables() {
    return this.runTest('Environment Variables', async () => {
      const requiredEnvVars = ['OPENAI_API_KEY'];
      const missingVars = [];

      for (const envVar of requiredEnvVars) {
        if (!process.env[envVar]) {
          missingVars.push(envVar);
        }
      }

      if (missingVars.length > 0) {
        this.warn(`Missing environment variables: ${missingVars.join(', ')}`);
        this.warn('Some features may not work without proper environment setup');
      }

      return true; // Don't fail the test for missing env vars, just warn
    });
  }

  async testMCPConfiguration() {
    return this.runTest('MCP Configuration', async () => {
      const mcpConfigPath = resolve(packageRoot, '.mcp.json');
      
      if (!existsSync(mcpConfigPath)) {
        throw new Error('.mcp.json configuration file not found');
      }

      const { readFileSync } = await import('fs');
      const config = JSON.parse(readFileSync(mcpConfigPath, 'utf8'));
      
      if (!config.mcpServers || !config.mcpServers['x-reddit-scraper']) {
        throw new Error('MCP server configuration not found in .mcp.json');
      }

      if (!config.tools || config.tools.length === 0) {
        throw new Error('No tools defined in MCP configuration');
      }

      return true;
    });
  }

  async testPackageStructure() {
    return this.runTest('Package Structure', async () => {
      const packageJsonPath = resolve(packageRoot, 'package.json');
      const { readFileSync } = await import('fs');
      const pkg = JSON.parse(readFileSync(packageJsonPath, 'utf8'));

      if (pkg.name !== 'mcp-x-reddit-scraper') {
        throw new Error(`Incorrect package name: ${pkg.name}`);
      }

      if (!pkg.bin || !pkg.bin['mcp-x-reddit-scraper']) {
        throw new Error('Missing bin configuration in package.json');
      }

      if (!pkg.keywords.includes('mcp')) {
        throw new Error('Missing "mcp" keyword in package.json');
      }

      return true;
    });
  }

  async runAllTests() {
    console.log('🚀 MCP X & Reddit Scraper Verification');
    console.log('=' .repeat(50));

    await this.testPrerequisites();
    await this.testPackageStructure();
    await this.testMCPConfiguration();
    await this.testEnvironmentVariables();
    await this.testServerStartup();
    await this.testToolsListing();

    console.log('\n📊 Test Results');
    console.log('=' .repeat(50));
    console.log(`Tests Passed: ${this.testsPassed}/${this.testsTotal}`);

    if (this.testsPassed === this.testsTotal) {
      this.success('All tests passed! MCP server is ready for use.');
      console.log(`
🎉 Verification Complete!
━━━━━━━━━━━━━━━━━━━━━━━

✨ Next Steps:
1. Install globally: npm install -g mcp-x-reddit-scraper
2. Register with Claude Code: npm run install-mcp
3. Set environment variables (especially OPENAI_API_KEY)
4. Start using the 25+ tools in Claude Code!

📚 See README.md for usage examples and troubleshooting.
      `);
      return true;
    } else {
      this.error(`${this.testsTotal - this.testsPassed} tests failed. Please fix issues before deployment.`);
      return false;
    }
  }
}

// Run verifier if called directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  const verifier = new MCPVerifier();
  verifier.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  });
}

export { MCPVerifier };