#!/usr/bin/env node

import { execSync } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packageRoot = resolve(__dirname, '..');

/**
 * MCP X & Reddit Scraper Installation Script
 * Automatically registers the server with Claude Code MCP configuration
 */

class MCPInstaller {
  constructor() {
    this.serverName = 'x-reddit-scraper';
    this.serverPath = resolve(packageRoot, 'build', 'index.js');
  }

  log(message) {
    console.log(`🔧 ${message}`);
  }

  error(message) {
    console.error(`❌ ${message}`);
  }

  success(message) {
    console.log(`✅ ${message}`);
  }

  async checkPrerequisites() {
    this.log('Checking prerequisites...');
    
    // Check if build exists
    if (!existsSync(this.serverPath)) {
      this.error('Server build not found. Run "npm run build" first.');
      process.exit(1);
    }

    // Check if Claude Code is installed
    try {
      execSync('claude --version', { stdio: 'ignore' });
      this.success('Claude Code CLI found');
    } catch (error) {
      this.error('Claude Code CLI not found. Please install it first:');
      console.log('  npm install -g @anthropic-ai/claude-code');
      process.exit(1);
    }
  }

  async installServer() {
    this.log(`Installing MCP server: ${this.serverName}`);
    
    try {
      // Use Claude Code's MCP add command
      const command = `claude mcp add ${this.serverName} -s local -- node "${this.serverPath}"`;
      
      this.log('Executing: ' + command);
      execSync(command, { stdio: 'inherit' });
      
      this.success(`MCP server "${this.serverName}" installed successfully!`);
      
    } catch (error) {
      this.error('Failed to install MCP server automatically.');
      this.log('Manual installation instructions:');
      this.logManualInstructions();
    }
  }

  logManualInstructions() {
    console.log(`
📋 Manual Installation Instructions:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

1. Add this configuration to your Claude Code MCP settings:

{
  "mcpServers": {
    "${this.serverName}": {
      "command": "node",
      "args": ["${this.serverPath}"],
      "env": {
        "OPENAI_API_KEY": "your-api-key-here"
      }
    }
  }
}

2. Or use the command:
   claude mcp add ${this.serverName} -s local -- node "${this.serverPath}"

3. Restart Claude Code after installation.

🔑 Don't forget to set your OPENAI_API_KEY environment variable!
    `);
  }

  async verifyInstallation() {
    this.log('Verifying installation...');
    
    try {
      // Check if server is listed
      const result = execSync('claude mcp list', { encoding: 'utf-8' });
      
      if (result.includes(this.serverName)) {
        this.success('MCP server is registered and available!');
        return true;
      } else {
        this.error('Server not found in MCP list');
        return false;
      }
    } catch (error) {
      this.log('Could not verify installation automatically');
      return false;
    }
  }

  async showUsageInstructions() {
    console.log(`
🎉 Installation Complete!
━━━━━━━━━━━━━━━━━━━━━━━

📱 Available Tools:
  • X/Twitter scraping (5 tools)
  • Reddit scraping (6 tools) 
  • AI agent chat (6 tools)
  • Fabric patterns (6 tools)
  • Browser automation (5+ tools)

💡 Quick Start Examples:
  • get_comments({"username": "elonmusk", "limit": 10})
  • search_reddit({"query": "artificial intelligence", "subreddit": "technology"})
  • create_chat_session({})
  • apply_fabric_pattern({"patternName": "analyze_social_sentiment", "content": "Amazing AI breakthrough!"})

🔧 Configuration:
  • Set OPENAI_API_KEY environment variable for AI features
  • Screenshots saved to ./screenshots/ directory
  • Custom Fabric patterns can be added

📚 Full documentation: See README.md for complete usage examples
    `);
  }

  async run() {
    console.log('🚀 MCP X & Reddit Scraper Installation');
    console.log('=' .repeat(50));
    
    await this.checkPrerequisites();
    await this.installServer();
    
    const verified = await this.verifyInstallation();
    if (verified) {
      await this.showUsageInstructions();
    } else {
      this.logManualInstructions();
    }
    
    console.log('\n🎯 Installation process complete!');
  }
}

// Run installer if called directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  const installer = new MCPInstaller();
  installer.run().catch(error => {
    console.error('❌ Installation failed:', error.message);
    process.exit(1);
  });
}

export { MCPInstaller };