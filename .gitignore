# Dependencies
node_modules/
venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python

# Build output
build/
dist/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.production
.env.staging

# API Keys and secrets
*.key
*.pem
secrets/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Mac
.DS_Store

# Windows
Thumbs.db
ehthumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
screenshots/
temp/
tmp/

# Fabric config (contains API keys)
~/.config/fabric/.env

# Test files
test-*.js
simple-test.js
live-monitoring-test.js
safe-scraping-test.js

# Monitoring files
scraping-monitor.json
live-monitoring.log
safe-scraping-test.log

# Code review history (keep templates)
code-review/reviews/