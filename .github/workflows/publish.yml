name: Publish MCP Server

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20, 22]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.19'

    - name: Install dependencies
      run: |
        npm ci
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        go install github.com/danielmiessler/fabric@latest

    - name: Build TypeScript
      run: npm run build

    - name: Test server startup
      run: |
        timeout 10s node build/index.js --help || exit_code=$?
        if [ $exit_code -ne 124 ] && [ $exit_code -ne 0 ]; then
          echo "Server failed to start properly"
          exit 1
        fi
        echo "Server startup test passed"

    - name: Validate MCP configuration
      run: |
        if [ ! -f .mcp.json ]; then
          echo "Missing .mcp.json configuration file"
          exit 1
        fi
        echo "MCP configuration validation passed"

  publish:
    needs: test
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        registry-url: 'https://registry.npmjs.org'

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Setup Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.19'

    - name: Install dependencies
      run: |
        npm ci
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        go install github.com/danielmiessler/fabric@latest

    - name: Build
      run: npm run build

    - name: Create package tarball
      run: npm pack

    - name: Publish to NPM
      run: npm publish
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref_name }}
        release_name: Release ${{ github.ref_name }}
        body: |
          ## MCP X & Reddit Scraper ${{ github.ref_name }}
          
          ### Installation
          ```bash
          npm install -g mcp-x-reddit-scraper@${{ github.ref_name }}
          npm run install-mcp
          ```
          
          ### What's Included
          - 25+ MCP tools for social media analysis
          - X/Twitter and Reddit scraping capabilities
          - AI agent with GPT-4.1 integration
          - Daniel Miessler's Fabric pattern analysis
          - Browser automation with Puppeteer
          - Docker deployment support
          
          See README.md for complete usage instructions.
        draft: false
        prerelease: false

    - name: Upload package to release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./mcp-x-reddit-scraper-${{ github.ref_name }}.tgz
        asset_name: mcp-x-reddit-scraper-${{ github.ref_name }}.tgz
        asset_content_type: application/gzip

  docker:
    needs: test
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: mcpserver/x-reddit-scraper
        tags: |
          type=ref,event=tag
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max