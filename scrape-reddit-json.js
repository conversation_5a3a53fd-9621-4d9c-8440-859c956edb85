#!/usr/bin/env node

// Simple Reddit JSON API scraper - more reliable than web scraping
import 'dotenv/config';
import fs from 'fs';
import path from 'path';

// Priority AI subreddits to check
const AI_SUBREDDITS = [
  { name: 'MachineLearning', subscribers: '2.5M', focus: 'Technical AI discussions' },
  { name: 'LocalLLaMA', subscribers: '400K', focus: 'Open-source AI developments' },
  { name: 'ChatGPT', subscribers: '300K', focus: 'AI tool experiences' },
  { name: 'datascience', subscribers: '800K', focus: 'Business AI applications' },
  { name: 'Futurology', subscribers: '18M', focus: 'AI future impact' },
  { name: 'programming', subscribers: '4M', focus: 'Developer AI perspectives' },
  { name: 'artificial', subscribers: '800K', focus: 'General AI discussions' }
];

async function fetchSubredditData(subredditName, limit = 25) {
  try {
    const url = `https://www.reddit.com/r/${subredditName}/hot.json?limit=${limit}`;
    console.log(`   🔗 Fetching: ${url}`);
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'AI-Discussion-Scraper/1.0'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data.data.children.map(child => child.data);
  } catch (error) {
    console.log(`   ❌ API Error: ${error.message}`);
    return [];
  }
}

function isRecentPost(timestamp) {
  const postTime = new Date(timestamp * 1000); // Reddit uses Unix timestamp
  const twoDaysAgo = new Date(Date.now() - (2 * 24 * 60 * 60 * 1000));
  return postTime >= twoDaysAgo;
}

function isAIRelated(title, selftext = '') {
  const content = (title + ' ' + selftext).toLowerCase();
  const aiKeywords = [
    'ai', 'artificial intelligence', 'machine learning', 'ml', 'deep learning', 'dl',
    'neural network', 'neural', 'gpt', 'llm', 'large language model', 'transformer',
    'bert', 'openai', 'anthropic', 'claude', 'chatgpt', 'prompt', 'fine-tuning',
    'training', 'model', 'algorithm', 'automation', 'computer vision', 'nlp',
    'natural language processing', 'generative', 'diffusion', 'stable diffusion',
    'midjourney', 'dalle', 'reinforcement learning', 'supervised learning',
    'unsupervised learning', 'pytorch', 'tensorflow', 'hugging face', 'llama',
    'mistral', 'gemini', 'palm', 'agi', 'artificial general intelligence'
  ];
  
  return aiKeywords.some(keyword => content.includes(keyword));
}

async function scrapeAIDiscussions() {
  console.log('🚀 Starting AI Discussion Scraping via Reddit JSON API...\n');
  
  const results = {
    timestamp: new Date().toISOString(),
    total_subreddits: AI_SUBREDDITS.length,
    subreddits: [],
    trending_topics: [],
    hot_discussions: [],
    summary: {
      total_posts: 0,
      total_ai_posts: 0,
      avg_engagement: 0,
      top_themes: []
    }
  };

  for (const subreddit of AI_SUBREDDITS) {
    console.log(`📊 Scraping r/${subreddit.name} (${subreddit.subscribers} subscribers)`);
    console.log(`   Focus: ${subreddit.focus}`);
    
    const posts = await fetchSubredditData(subreddit.name, 25);
    
    if (posts.length === 0) {
      results.subreddits.push({
        name: subreddit.name,
        subscribers: subreddit.subscribers,
        focus: subreddit.focus,
        error: 'No data retrieved',
        posts_analyzed: 0
      });
      console.log(`   ⚠️  No posts retrieved\n`);
      continue;
    }

    // Filter for recent posts
    const recentPosts = posts.filter(post => isRecentPost(post.created_utc));
    
    // For non-AI specific subreddits, filter for AI-related content
    const relevantPosts = ['programming', 'Futurology', 'datascience'].includes(subreddit.name) 
      ? recentPosts.filter(post => isAIRelated(post.title, post.selftext))
      : recentPosts;

    console.log(`   ✅ Found ${posts.length} posts, ${recentPosts.length} recent, ${relevantPosts.length} AI-relevant\n`);

    // Store subreddit results
    const subredditData = {
      name: subreddit.name,
      subscribers: subreddit.subscribers,
      focus: subreddit.focus,
      posts_analyzed: relevantPosts.length,
      total_posts_checked: posts.length,
      hot_posts: relevantPosts.slice(0, 10).map(post => ({
        id: post.id,
        title: post.title,
        author: post.author,
        score: post.score,
        comments: post.num_comments,
        url: `https://reddit.com${post.permalink}`,
        full_url: post.url,
        timestamp: new Date(post.created_utc * 1000).toISOString(),
        content_preview: post.selftext ? post.selftext.substring(0, 200) + '...' : '',
        is_self_post: post.is_self,
        domain: post.domain,
        flair: post.link_flair_text,
        upvote_ratio: post.upvote_ratio
      })),
      engagement_metrics: {
        avg_score: relevantPosts.reduce((sum, post) => sum + post.score, 0) / relevantPosts.length || 0,
        avg_comments: relevantPosts.reduce((sum, post) => sum + post.num_comments, 0) / relevantPosts.length || 0,
        total_engagement: relevantPosts.reduce((sum, post) => sum + post.score + post.num_comments, 0)
      }
    };

    results.subreddits.push(subredditData);
    results.summary.total_posts += relevantPosts.length;
    results.summary.total_ai_posts += relevantPosts.length;

    // Identify high-engagement posts
    const highEngagementPosts = relevantPosts
      .filter(post => post.score > 50 || post.num_comments > 10)
      .slice(0, 5);

    highEngagementPosts.forEach(post => {
      results.hot_discussions.push({
        subreddit: `r/${subreddit.name}`,
        title: post.title,
        score: post.score,
        comments: post.num_comments,
        url: `https://reddit.com${post.permalink}`,
        engagement_score: post.score + (post.num_comments * 5),
        upvote_ratio: post.upvote_ratio,
        created: new Date(post.created_utc * 1000).toISOString()
      });
    });

    // Small delay to be respectful
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Calculate summary metrics
  const validSubreddits = results.subreddits.filter(s => !s.error);
  if (validSubreddits.length > 0) {
    results.summary.avg_engagement = validSubreddits.reduce((sum, s) => 
      sum + (s.engagement_metrics?.total_engagement || 0), 0) / validSubreddits.length;
  }

  // Sort hot discussions by engagement
  results.hot_discussions.sort((a, b) => b.engagement_score - a.engagement_score);
  results.hot_discussions = results.hot_discussions.slice(0, 25);

  // Extract trending topics from titles
  const allTitles = results.subreddits
    .filter(s => s.hot_posts)
    .flatMap(s => s.hot_posts.map(p => p.title.toLowerCase()));
  
  const topicCounts = {};
  const aiKeywords = [
    'gpt', 'llm', 'chatgpt', 'claude', 'ai', 'artificial intelligence', 'machine learning',
    'deep learning', 'neural', 'transformer', 'model', 'training', 'fine-tuning', 
    'prompt', 'openai', 'anthropic', 'google', 'microsoft', 'llama', 'mistral',
    'automation', 'agi', 'reasoning', 'multimodal', 'vision', 'embeddings',
    'pytorch', 'tensorflow', 'hugging face', 'stable diffusion', 'midjourney',
    'reinforcement learning', 'computer vision', 'nlp', 'generative'
  ];

  aiKeywords.forEach(keyword => {
    const count = allTitles.filter(title => title.includes(keyword)).length;
    if (count > 0) {
      topicCounts[keyword] = count;
    }
  });

  results.trending_topics = Object.entries(topicCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 15)
    .map(([topic, count]) => ({ topic, mentions: count }));

  results.summary.top_themes = results.trending_topics.slice(0, 8).map(t => t.topic);

  // Save results
  const outputPath = path.join(process.cwd(), 'logs', `ai-discussions-${Date.now()}.json`);
  fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));

  // Generate readable summary
  console.log('\n🎯 AI DISCUSSION SUMMARY');
  console.log('='.repeat(60));
  console.log(`📊 Analyzed ${results.summary.total_posts} AI-relevant posts across ${results.total_subreddits} subreddits`);
  console.log(`📈 Average engagement: ${Math.round(results.summary.avg_engagement)}`);
  console.log(`🔥 Top trending topics: ${results.summary.top_themes.join(', ')}`);
  
  console.log('\n🏆 TRENDING AI TOPICS:');
  results.trending_topics.slice(0, 10).forEach((topic, i) => {
    console.log(`${i + 1}. ${topic.topic} (${topic.mentions} mentions)`);
  });
  
  console.log('\n🚀 TOP HIGH-ENGAGEMENT AI DISCUSSIONS:');
  results.hot_discussions.slice(0, 15).forEach((discussion, i) => {
    console.log(`\n${i + 1}. ${discussion.subreddit}: "${discussion.title}"`);
    console.log(`   📊 ${discussion.score} upvotes, ${discussion.comments} comments (${Math.round(discussion.upvote_ratio * 100)}% upvoted)`);
    console.log(`   🕒 ${new Date(discussion.created).toLocaleString()}`);
    console.log(`   🔗 ${discussion.url}`);
  });

  console.log('\n📋 SUBREDDIT BREAKDOWN:');
  results.subreddits
    .filter(s => !s.error && s.posts_analyzed > 0)
    .sort((a, b) => b.engagement_metrics.total_engagement - a.engagement_metrics.total_engagement)
    .forEach(s => {
      console.log(`\n📂 r/${s.name} (${s.focus})`);
      console.log(`   📊 ${s.posts_analyzed} AI posts analyzed, avg score: ${Math.round(s.engagement_metrics.avg_score)}`);
      if (s.hot_posts.length > 0) {
        console.log(`   🔥 Top post: "${s.hot_posts[0].title}" (${s.hot_posts[0].score} points)`);
      }
    });

  console.log(`\n💾 Full results saved to: ${outputPath}`);
  
  return results;
}

// Run the scraper
scrapeAIDiscussions()
  .then(() => {
    console.log('\n✅ AI Discussion scraping completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Scraping failed:', error);
    process.exit(1);
  });