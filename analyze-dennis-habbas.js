#!/usr/bin/env node

/**
 * Demis Hassabis Analysis Script
 * Scrapes and analyzes recent posts from <PERSON><PERSON> (Google DeepMind CEO & Nobel Laureate)
 */

import { XScraper } from './build/scraper.js';
import { TrendingAnalyzer } from './build/analyzer.js';

async function analyzeDennisHabbas() {
  console.log('🔍 Starting analysis of <PERSON><PERSON> (Google DeepMind CEO & Nobel Laureate)...\n');
  
  const scraper = new XScraper();
  const analyzer = new TrendingAnalyzer();
  
  try {
    // Get recent comments/tweets from Demis Hassabis
    console.log('📱 Fetching recent posts from @demishassabis...');
    const posts = await scraper.getComments('demishassabis', 20, true);
    
    if (posts.length === 0) {
      console.log('⚠️  No posts found. User may not exist or account may be private/protected.');
      return;
    }
    
    console.log(`✅ Found ${posts.length} recent posts\n`);
    
    // Display recent posts
    console.log('📋 Recent Posts:');
    console.log('=' .repeat(80));
    posts.slice(0, 5).forEach((post, index) => {
      console.log(`${index + 1}. ${post.text.substring(0, 200)}${post.text.length > 200 ? '...' : ''}`);
      console.log(`   📊 Engagement: ${post.likes} likes, ${post.retweets} retweets, ${post.replies} replies`);
      console.log(`   📅 Posted: ${post.timestamp}`);
      console.log('');
    });
    
    // Analyze sentiment of posts
    console.log('🧠 Analyzing sentiment and trends...');
    const sentimentAnalysis = await analyzer.analyzeSentiment(
      posts.map(p => p.text),
      'aggregate'
    );
    
    console.log('\n📈 Sentiment Analysis:');
    console.log('=' .repeat(50));
    console.log(`Overall Sentiment: ${sentimentAnalysis.overall.toUpperCase()}`);
    console.log(`Sentiment Score: ${sentimentAnalysis.score.toFixed(3)}`);
    
    if (sentimentAnalysis.breakdown) {
      console.log(`Positive: ${sentimentAnalysis.breakdown.positive.toFixed(1)}%`);
      console.log(`Negative: ${sentimentAnalysis.breakdown.negative.toFixed(1)}%`);
      console.log(`Neutral: ${sentimentAnalysis.breakdown.neutral.toFixed(1)}%`);
    }
    
    if (sentimentAnalysis.keywords && sentimentAnalysis.keywords.length > 0) {
      console.log(`Key Topics: ${sentimentAnalysis.keywords.join(', ')}`);
    }
    
    // Extract trending hashtags and topics
    console.log('\n🏷️  Trending Topics and Hashtags:');
    console.log('=' .repeat(50));
    const trendingHashtags = analyzer.extractTrendingHashtags(posts.map(p => p.text));
    
    if (trendingHashtags.length > 0) {
      trendingHashtags.slice(0, 10).forEach((tag, index) => {
        console.log(`${index + 1}. ${tag.hashtag} (${tag.count} mentions)`);
      });
    } else {
      console.log('No hashtags found in recent posts');
    }
    
    // Engagement analysis
    console.log('\n📊 Engagement Patterns:');
    console.log('=' .repeat(50));
    const engagementPatterns = analyzer.identifyEngagementPatterns(posts);
    console.log(`Best posting time: ${engagementPatterns.bestPostingTime}`);
    console.log(`Average engagement: ${engagementPatterns.averageEngagement.toFixed(0)} interactions per post`);
    console.log(`High-engagement keywords: ${engagementPatterns.highEngagementKeywords.slice(0, 5).join(', ')}`);
    
    // Recent trending topics analysis
    console.log('\n🔥 Current Focus Areas:');
    console.log('=' .repeat(50));
    
    // Extract key themes from posts
    const allText = posts.map(p => p.text).join(' ');
    const aiKeywords = ['AI', 'artificial intelligence', 'machine learning', 'ML', 'DeepMind', 'AGI', 'neural', 'model', 'training', 'research', 'safety', 'alignment', 'reasoning', 'LLM', 'transformer'];
    const foundKeywords = aiKeywords.filter(keyword => 
      allText.toLowerCase().includes(keyword.toLowerCase())
    );
    
    if (foundKeywords.length > 0) {
      console.log(`AI/ML Topics Mentioned: ${foundKeywords.join(', ')}`);
    }
    
    // Recent high-engagement posts
    const topPosts = posts
      .sort((a, b) => (b.likes + b.retweets + b.replies) - (a.likes + a.retweets + a.replies))
      .slice(0, 3);
    
    console.log('\n🌟 Top Engaging Posts:');
    console.log('=' .repeat(80));
    topPosts.forEach((post, index) => {
      const totalEngagement = post.likes + post.retweets + post.replies;
      console.log(`${index + 1}. "${post.text.substring(0, 150)}${post.text.length > 150 ? '...' : ''}"`);
      console.log(`   💫 Total Engagement: ${totalEngagement} (${post.likes}L, ${post.retweets}RT, ${post.replies}R)`);
      console.log('');
    });
    
    console.log('\n✅ Analysis Complete!');
    console.log('\n📝 Summary:');
    console.log(`Demis Hassabis has ${posts.length} recent posts with ${sentimentAnalysis.overall} sentiment.`);
    console.log(`Average engagement per post: ${engagementPatterns.averageEngagement.toFixed(0)} interactions.`);
    if (foundKeywords.length > 0) {
      console.log(`Current focus areas: ${foundKeywords.slice(0, 5).join(', ')}`);
    }
    
  } catch (error) {
    console.error('❌ Error during analysis:', error.message);
    
    // Check if it's a user not found error
    if (error.message.includes('user not found') || error.message.includes('protected') || error.message.includes('suspended')) {
      console.log('\n💡 Possible reasons:');
      console.log('   • Username may be incorrect (@demishassabis)');
      console.log('   • Account may be protected/private');
      console.log('   • Account may be suspended or deactivated');
      console.log('   • Rate limiting may be in effect');
      console.log('\n🔍 You may want to verify the correct Twitter handle for Dennis Habbas');
    }
  } finally {
    // Cleanup
    await scraper.cleanup();
    console.log('\n🧹 Cleanup completed');
  }
}

// Run the analysis
analyzeDennisHabbas().catch(console.error);