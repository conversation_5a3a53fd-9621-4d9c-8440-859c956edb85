#!/usr/bin/env node

import 'dotenv/config';
import { RedditScraper } from './build/reddit-scraper.js';
import fs from 'fs';
import path from 'path';

const scraper = new RedditScraper();

// Priority AI subreddits to check
const AI_SUBREDDITS = [
  { name: 'MachineLearning', subscribers: '2.5M', focus: 'Technical AI discussions' },
  { name: 'LocalLLaMA', subscribers: '400K', focus: 'Open-source AI developments' },
  { name: 'ChatGPT', subscribers: '300K', focus: 'AI tool experiences' },
  { name: 'datascience', subscribers: '800K', focus: 'Business AI applications' },
  { name: 'Futurology', subscribers: '18M', focus: 'AI future impact' },
  { name: 'programming', subscribers: '4M', focus: 'Developer AI perspectives' },
  { name: 'artificial', subscribers: '800K', focus: 'General AI discussions' }
];

async function scrapeAIDiscussions() {
  console.log('🚀 Starting AI Discussion Scraping...\n');
  
  const results = {
    timestamp: new Date().toISOString(),
    total_subreddits: AI_SUBREDDITS.length,
    subreddits: [],
    trending_topics: [],
    hot_discussions: [],
    summary: {
      total_posts: 0,
      avg_engagement: 0,
      top_themes: []
    }
  };

  for (const subreddit of AI_SUBREDDITS) {
    console.log(`📊 Scraping r/${subreddit.name} (${subreddit.subscribers} subscribers)`);
    console.log(`   Focus: ${subreddit.focus}`);
    
    try {
      // Get hot posts from the last 24-48 hours
      const hotPosts = await scraper.getSubredditPosts(subreddit.name, 'hot', 15);
      
      // Filter for recent posts (last 2 days)
      const recentPosts = hotPosts.filter(post => {
        if (!post.timestamp) return true;
        const postTime = new Date(post.timestamp);
        const twoDaysAgo = new Date(Date.now() - (2 * 24 * 60 * 60 * 1000));
        return postTime >= twoDaysAgo;
      });

      console.log(`   ✅ Found ${recentPosts.length} recent hot posts\n`);

      // Store subreddit results
      const subredditData = {
        name: subreddit.name,
        subscribers: subreddit.subscribers,
        focus: subreddit.focus,
        posts_analyzed: recentPosts.length,
        hot_posts: recentPosts.slice(0, 10).map(post => ({
          title: post.title,
          author: post.author,
          score: post.score,
          comments: post.commentCount,
          url: post.url,
          timestamp: post.timestamp,
          content_preview: post.content ? post.content.substring(0, 200) + '...' : ''
        })),
        engagement_metrics: {
          avg_score: recentPosts.reduce((sum, post) => sum + (post.score || 0), 0) / recentPosts.length || 0,
          avg_comments: recentPosts.reduce((sum, post) => sum + (post.commentCount || 0), 0) / recentPosts.length || 0,
          total_engagement: recentPosts.reduce((sum, post) => sum + (post.score || 0) + (post.commentCount || 0), 0)
        }
      };

      results.subreddits.push(subredditData);
      results.summary.total_posts += recentPosts.length;

      // Identify high-engagement posts
      const highEngagementPosts = recentPosts
        .filter(post => (post.score || 0) > 100 || (post.commentCount || 0) > 20)
        .slice(0, 3);

      highEngagementPosts.forEach(post => {
        results.hot_discussions.push({
          subreddit: `r/${subreddit.name}`,
          title: post.title,
          score: post.score,
          comments: post.commentCount,
          url: post.url,
          engagement_score: (post.score || 0) + (post.commentCount || 0) * 5
        });
      });

      // Small delay to be respectful to Reddit's servers
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      console.log(`   ❌ Error scraping r/${subreddit.name}: ${error.message}\n`);
      results.subreddits.push({
        name: subreddit.name,
        error: error.message,
        posts_analyzed: 0
      });
    }
  }

  // Calculate summary metrics
  const validSubreddits = results.subreddits.filter(s => !s.error);
  if (validSubreddits.length > 0) {
    results.summary.avg_engagement = validSubreddits.reduce((sum, s) => 
      sum + (s.engagement_metrics?.total_engagement || 0), 0) / validSubreddits.length;
  }

  // Sort hot discussions by engagement
  results.hot_discussions.sort((a, b) => b.engagement_score - a.engagement_score);
  results.hot_discussions = results.hot_discussions.slice(0, 20);

  // Extract trending topics from titles
  const allTitles = results.subreddits
    .filter(s => s.hot_posts)
    .flatMap(s => s.hot_posts.map(p => p.title.toLowerCase()));
  
  const topicCounts = {};
  const aiKeywords = [
    'gpt', 'llm', 'ai', 'artificial intelligence', 'machine learning', 'deep learning',
    'neural', 'transformer', 'model', 'training', 'fine-tuning', 'prompt', 'chatbot',
    'openai', 'anthropic', 'google', 'microsoft', 'llama', 'mistral', 'claude',
    'automation', 'agi', 'reasoning', 'multimodal', 'vision', 'embeddings'
  ];

  aiKeywords.forEach(keyword => {
    const count = allTitles.filter(title => title.includes(keyword)).length;
    if (count > 0) {
      topicCounts[keyword] = count;
    }
  });

  results.trending_topics = Object.entries(topicCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([topic, count]) => ({ topic, mentions: count }));

  results.summary.top_themes = results.trending_topics.slice(0, 5).map(t => t.topic);

  // Save results
  const outputPath = path.join(process.cwd(), 'logs', `ai-discussions-${Date.now()}.json`);
  fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));

  // Generate readable summary
  console.log('\n🎯 AI DISCUSSION SUMMARY');
  console.log('='.repeat(50));
  console.log(`📊 Analyzed ${results.summary.total_posts} posts across ${results.total_subreddits} subreddits`);
  console.log(`📈 Average engagement: ${Math.round(results.summary.avg_engagement)}`);
  console.log(`🔥 Top trending topics: ${results.summary.top_themes.join(', ')}`);
  
  console.log('\n🚀 TOP HIGH-ENGAGEMENT DISCUSSIONS:');
  results.hot_discussions.slice(0, 10).forEach((discussion, i) => {
    console.log(`${i + 1}. ${discussion.subreddit}: "${discussion.title}"`);
    console.log(`   📊 ${discussion.score} upvotes, ${discussion.comments} comments`);
    console.log(`   🔗 ${discussion.url}\n`);
  });

  console.log(`\n💾 Full results saved to: ${outputPath}`);
  
  await scraper.close();
  return results;
}

// Run the scraper
scrapeAIDiscussions()
  .then(() => {
    console.log('\n✅ AI Discussion scraping completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Scraping failed:', error);
    process.exit(1);
  });