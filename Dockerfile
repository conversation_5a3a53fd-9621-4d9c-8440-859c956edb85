FROM node:20-alpine

# Install Python and Go for dependencies
RUN apk add --no-cache python3 py3-pip go git curl

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY requirements.txt ./

# Install Node.js dependencies
RUN npm ci --only=production

# Install Python dependencies
RUN python3 -m venv venv && \
    source venv/bin/activate && \
    pip install -r requirements.txt

# Install Fabric
ENV GOPATH=/go
ENV PATH=$GOPATH/bin:$PATH
RUN go install github.com/danielmiessler/fabric@latest

# Create Fabric config directory
RUN mkdir -p ~/.config/fabric/patterns

# Copy application code
COPY src/ ./src/
COPY tsconfig.json ./

# Build TypeScript
RUN npm run build

# Copy Python files to build directory
RUN cp src/python_bridge.py build/ && \
    cp -r src/agent build/

# Copy custom Fabric patterns
COPY src/agent/patterns ~/.config/fabric/patterns/

# Create screenshots directory
RUN mkdir -p screenshots

# Environment variables
ENV NODE_ENV=production
ENV OPENAI_API_KEY=""
ENV DEFAULT_MODEL=gpt-4.1
ENV DEFAULT_VENDOR=OpenAI

# Create Fabric config
RUN echo "DEFAULT_MODEL=gpt-4.1" > ~/.config/fabric/.env && \
    echo "DEFAULT_VENDOR=OpenAI" >> ~/.config/fabric/.env && \
    echo "OPENAI_API_KEY=" >> ~/.config/fabric/.env

# Expose port (if needed for web interface)
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD echo '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}' | node build/index.js || exit 1

# Start the MCP server
CMD ["node", "build/index.js"]