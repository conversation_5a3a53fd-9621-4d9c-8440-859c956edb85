# Web Scraping Intelligence Knowledge Base

## Anti-Detection Techniques by Site Type

### Social Media Sites (Twitter/X, Reddit)
- **High sensitivity to automation detection**
  - Require residential proxies and realistic timing
  - User-agent rotation critical
  - JavaScript challenges common
  - Rate limiting strictly enforced
  - Session tracking and behavioral analysis
  - Dynamic loading patterns make static scraping difficult

### E-commerce Sites (Amazon, eBay, Shopify)
- **Strong bot protection** (Cloudflare, Akamai, DataDome)
  - Browser fingerprinting prevalent
  - Session management important
  - TLS fingerprint analysis
  - Product data often protected by sophisticated anti-bot measures
  - Require consistent header sets and HTTP/2 compliance

### News/Content Sites
- **Generally more permissive**
  - Standard rate limiting sufficient
  - Focus on respectful scraping
  - RSS feeds often available as alternative
  - Usually comply with robots.txt guidelines

### Financial/Data Sites
- **Extremely strict protection**
  - Real-time monitoring for suspicious patterns
  - IP geolocation restrictions
  - Multi-factor authentication required
  - Legal compliance considerations

## Blocking Indicators and Patterns

### HTTP Response Indicators
- **403 Forbidden** responses
- **429 Too Many Requests**
- **503 Service Unavailable**
- Unusual redirects to bot detection pages
- Connection timeouts and refused connections
- Invalid or empty response bodies

### Content-Based Indicators
- CAPTCHA challenges (reCAPTCHA, hCaptcha)
- JavaScript bot detection scripts
- "Access Denied" or "Bot Detected" messages
- Cloudflare challenge pages
- Abnormal page layouts or missing content

### Behavioral Indicators
- Sudden drop in success rates
- Increased response times
- Consistent empty responses
- IP address getting different content than expected

## Successful Anti-Detection Patterns

### Infrastructure Level
- **Residential proxy rotation** with sticky sessions
- **Geographic IP diversity** matching target audience
- **HTTP/2 protocol compliance** with proper ALPN negotiation
- **TLS fingerprint randomization** matching real browsers
- **DNS-over-HTTPS** to avoid DNS-based detection

### Browser Level
- **Puppeteer-extra stealth plugin** with all evasions enabled
- **Consistent header sets** from real browser profiles
- **WebGL and canvas fingerprint spoofing**
- **Screen resolution and viewport randomization**
- **Plugin and codec simulation**

### Behavioral Level
- **Human-like mouse movements** with realistic bezier curves
- **Realistic scroll patterns** with variable speeds
- **Random timing variations** with jitter
- **Authentic form filling** with typing delays
- **Tab switching and focus events** simulation

### Request Patterns
- **Exponential backoff** for retries
- **Session warmup** before main scraping
- **Mixed request types** (images, CSS, JS) to appear authentic
- **Referer chain consistency** throughout session
- **Cookie jar management** with proper persistence

## Domain-Specific Strategies

### Twitter/X (x.com)
- **Strict Mode Required**: 10-30s delays minimum
- **Session Management**: Login required for most data
- **Rate Limits**: 300 requests per 15-minute window per endpoint
- **Anti-Bot Measures**: Advanced behavioral analysis, device fingerprinting
- **Success Factors**: Mobile user agents, authentic headers, realistic timing

### Reddit (reddit.com)
- **API Preferred**: Official API when possible
- **User-Agent Requirements**: Must identify application and contact
- **Rate Limits**: 60 requests per minute
- **Session Handling**: OAuth 2.0 for authenticated requests
- **Success Factors**: Respect for robots.txt, proper attribution

### E-commerce Sites
- **Product Pages**: Often the most protected
- **Search Results**: Usually less protected than detail pages
- **Image Requests**: Can reveal bot behavior if not handled properly
- **Shopping Cart**: Never interact to avoid disrupting real users
- **Success Factors**: Authentic shopping behavior patterns

## Error Recovery Strategies

### Temporary Blocks (HTTP 429, 503)
- **Exponential backoff**: Start with 1s, double each time, max 300s
- **IP rotation**: Switch to different IP from pool
- **User agent rotation**: Change browser signature
- **Session reset**: Clear cookies and start fresh

### Hard Blocks (HTTP 403, Cloudflare)
- **IP address change**: Required immediately
- **Strategy downgrade**: Move from aggressive to strict mode
- **Cooling period**: Wait 1-24 hours before retry
- **Pattern analysis**: Review what triggered the block

### CAPTCHA Challenges
- **Screenshot capture**: For manual solving or AI analysis
- **Service integration**: 2captcha, Anti-Captcha services
- **Avoidance strategy**: Modify behavior to prevent triggers
- **Alternative endpoints**: Find unprotected data sources

## Success Metrics and KPIs

### Primary Metrics
- **Success Rate**: >95% for production targets
- **Block Rate**: <1% across all attempts
- **Data Quality**: >98% complete and accurate
- **Response Time**: <3s average per request

### Secondary Metrics
- **Cost Efficiency**: <$0.01 per successful data point
- **Coverage**: Ability to access all required data points
- **Reliability**: Consistent performance over time
- **Compliance**: Adherence to robots.txt and ToS

## Best Practices Summary

### Before Starting
1. **Research target site** thoroughly
2. **Check robots.txt** and respect guidelines
3. **Review terms of service** for legal compliance
4. **Test with minimal requests** first
5. **Set up proper monitoring** and alerting

### During Scraping
1. **Monitor success rates** continuously
2. **Adjust strategies** based on real-time feedback
3. **Log all decisions** for analysis
4. **Respect rate limits** strictly
5. **Have fallback strategies** ready

### After Issues
1. **Analyze failure patterns** immediately
2. **Update strategies** based on learnings
3. **Document new techniques** discovered
4. **Share knowledge** with team
5. **Plan prevention** for future occurrences

## Ethical Considerations

### Responsible Scraping
- **Respect server resources** - don't overload
- **Honor robots.txt** and meta tags
- **Provide proper attribution** when required
- **Don't interfere** with normal site operations
- **Consider API alternatives** first

### Data Privacy
- **Avoid personal information** collection
- **Comply with GDPR/CCPA** regulations
- **Secure data storage** and transmission
- **Regular data purging** of unnecessary information
- **Audit trails** for all data access

This knowledge base should be continuously updated with new learnings, successful techniques, and changing patterns in anti-bot detection systems.