#!/bin/sh
# Post-install hook - runs after npm install

echo "🔧 Running post-install setup..."

# Ensure logs directory exists
mkdir -p logs

# Ensure screenshots directory exists  
mkdir -p screenshots

# Check if build directory exists, if not suggest building
if [ ! -d "build" ]; then
    echo "💡 Run 'npm run build' to compile TypeScript"
fi

# Check for .env file
if [ ! -f ".env" ]; then
    echo "💡 Create .env file with your OPENAI_API_KEY"
fi

echo "✅ Post-install setup complete"
