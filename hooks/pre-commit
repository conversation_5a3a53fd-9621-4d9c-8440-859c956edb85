#!/bin/sh
# Pre-commit hook to ensure code quality before commits

echo "🔍 Running pre-commit checks..."

# Check TypeScript compilation
echo "📦 Building TypeScript..."
npm run build
if [ $? -ne 0 ]; then
    echo "❌ TypeScript compilation failed. Please fix errors before committing."
    exit 1
fi

# Run ESLint
echo "🔧 Running ESLint..."
npm run lint
if [ $? -ne 0 ]; then
    echo "❌ ESLint found issues. Run 'npm run lint:fix' to auto-fix or fix manually."
    exit 1
fi

# Check Prettier formatting
echo "💅 Checking code formatting..."
npm run prettier:check
if [ $? -ne 0 ]; then
    echo "❌ Code formatting issues found. Run 'npm run prettier' to fix."
    exit 1
fi

echo "✅ All pre-commit checks passed!"
exit 0