{"version": "1.0.0", "description": "Git hooks configuration for MCP X/Reddit Scraper", "hooks": {"pre-commit": {"enabled": true, "description": "Code quality checks before commit", "checks": ["typescript-build", "eslint", "prettier-check"], "timeout": 120000, "failOnError": true}, "pre-push": {"enabled": true, "description": "Safety checks before push", "checks": ["health-check", "secret-scan", "monitoring-check"], "timeout": 60000, "failOnError": true}, "post-install": {"enabled": true, "description": "Setup after npm install", "checks": ["directory-setup", "env-check", "build-reminder"], "timeout": 30000, "failOnError": false}, "post-merge": {"enabled": false, "description": "Actions after git merge", "checks": ["dependency-check", "build-check"], "timeout": 60000, "failOnError": false}}, "settings": {"colorOutput": true, "verbose": true, "logHookExecution": true, "logDirectory": "logs", "bypassWithFlag": "--no-verify"}, "integrations": {"claudeCodeBoost": {"enabled": true, "autoApprove": ["file-reads", "builds", "tests", "lint-checks"]}, "monitoring": {"trackHookExecution": true, "reportFailures": true}}}