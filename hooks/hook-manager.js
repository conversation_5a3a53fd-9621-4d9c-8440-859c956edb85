#!/usr/bin/env node

/**
 * Hook Manager
 * Manages git hooks configuration and execution
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync, readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🎣 Hook Manager');
console.log('===============\n');

const args = process.argv.slice(2);
const command = args[0];

if (!command || command === 'help') {
  console.log('Usage: node hook-manager.js <command> [options]\n');
  console.log('Commands:');
  console.log('  list          List all available hooks');
  console.log('  status        Show hook installation status');
  console.log('  install       Install hooks to git repository');
  console.log('  uninstall     Remove hooks from git repository');
  console.log('  enable        Enable a specific hook');
  console.log('  disable       Disable a specific hook');
  console.log('  config        Show hook configuration');
  console.log('  test          Test a specific hook');
  console.log('\nExamples:');
  console.log('  node hook-manager.js status');
  console.log('  node hook-manager.js install');
  console.log('  node hook-manager.js test pre-commit');
  process.exit(0);
}

// Load hook configuration
function loadConfig() {
  const configPath = join(__dirname, 'hook-config.json');
  if (!existsSync(configPath)) {
    console.log('❌ Hook configuration not found');
    process.exit(1);
  }
  
  try {
    return JSON.parse(readFileSync(configPath, 'utf-8'));
  } catch (error) {
    console.log('❌ Failed to load hook configuration:', error.message);
    process.exit(1);
  }
}

// List available hooks
function listHooks() {
  console.log('📋 Available Hooks:');
  console.log('-------------------');
  
  const hookFiles = readdirSync(__dirname)
    .filter(file => !file.includes('.') || file.endsWith('.sh'))
    .filter(file => !['install-hooks.js', 'hook-manager.js', 'hook-config.json', '.gitkeep'].includes(file));
  
  if (hookFiles.length === 0) {
    console.log('No hook files found');
    return;
  }
  
  hookFiles.forEach(hook => {
    const hookPath = join(__dirname, hook);
    const stats = statSync(hookPath);
    const size = (stats.size / 1024).toFixed(1);
    const executable = (stats.mode & parseInt('111', 8)) ? '✅' : '❌';
    console.log(`  ${hook.padEnd(15)} ${executable} ${size}KB`);
  });
}

// Show hook installation status
function showStatus() {
  console.log('📊 Hook Installation Status:');
  console.log('-----------------------------');
  
  const gitHooksDir = join(projectRoot, '.git', 'hooks');
  
  if (!existsSync(gitHooksDir)) {
    console.log('❌ Git hooks directory not found');
    return;
  }
  
  const config = loadConfig();
  
  Object.entries(config.hooks).forEach(([hookName, hookConfig]) => {
    const hookPath = join(gitHooksDir, hookName);
    const installed = existsSync(hookPath);
    const enabled = hookConfig.enabled;
    
    const status = installed ? (enabled ? '✅ Installed & Enabled' : '⚠️  Installed but Disabled') : '❌ Not Installed';
    console.log(`  ${hookName.padEnd(15)} ${status}`);
    
    if (installed) {
      const stats = statSync(hookPath);
      const lastModified = stats.mtime.toLocaleDateString();
      console.log(`                       Last modified: ${lastModified}`);
    }
  });
}

// Install hooks
function installHooks() {
  console.log('🔧 Installing hooks...');
  
  try {
    execSync('node install-hooks.js', { cwd: __dirname, stdio: 'inherit' });
    console.log('✅ Hooks installation completed');
  } catch (error) {
    console.log('❌ Hook installation failed');
    process.exit(1);
  }
}

// Uninstall hooks
function uninstallHooks() {
  console.log('🗑️  Uninstalling hooks...');
  
  const gitHooksDir = join(projectRoot, '.git', 'hooks');
  const config = loadConfig();
  
  let removed = 0;
  
  Object.keys(config.hooks).forEach(hookName => {
    const hookPath = join(gitHooksDir, hookName);
    if (existsSync(hookPath)) {
      try {
        execSync(`rm "${hookPath}"`);
        console.log(`   Removed: ${hookName}`);
        removed++;
      } catch (error) {
        console.log(`   Failed to remove: ${hookName}`);
      }
    }
  });
  
  console.log(`✅ Removed ${removed} hooks`);
}

// Enable/disable a hook
function toggleHook(hookName, enable) {
  const config = loadConfig();
  const configPath = join(__dirname, 'hook-config.json');
  
  if (!config.hooks[hookName]) {
    console.log(`❌ Unknown hook: ${hookName}`);
    return;
  }
  
  config.hooks[hookName].enabled = enable;
  
  try {
    writeFileSync(configPath, JSON.stringify(config, null, 2));
    console.log(`✅ ${enable ? 'Enabled' : 'Disabled'} hook: ${hookName}`);
  } catch (error) {
    console.log(`❌ Failed to update configuration: ${error.message}`);
  }
}

// Show configuration
function showConfig() {
  const config = loadConfig();
  
  console.log('⚙️  Hook Configuration:');
  console.log('-----------------------');
  console.log(`Version: ${config.version}`);
  console.log(`Description: ${config.description}\n`);
  
  console.log('Hooks:');
  Object.entries(config.hooks).forEach(([name, hookConfig]) => {
    console.log(`  ${name}:`);
    console.log(`    Enabled: ${hookConfig.enabled ? '✅' : '❌'}`);
    console.log(`    Description: ${hookConfig.description}`);
    console.log(`    Timeout: ${hookConfig.timeout / 1000}s`);
    console.log(`    Fail on Error: ${hookConfig.failOnError ? 'Yes' : 'No'}`);
    if (hookConfig.checks.length > 0) {
      console.log(`    Checks: ${hookConfig.checks.join(', ')}`);
    }
    console.log();
  });
  
  console.log('Settings:');
  Object.entries(config.settings).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
}

// Test a hook
function testHook(hookName) {
  if (!hookName) {
    console.log('❌ Please specify a hook name to test');
    process.exit(1);
  }
  
  const hookPath = join(__dirname, hookName);
  
  if (!existsSync(hookPath)) {
    console.log(`❌ Hook not found: ${hookName}`);
    process.exit(1);
  }
  
  console.log(`🧪 Testing hook: ${hookName}`);
  console.log('-------------------');
  
  try {
    execSync(`node "${hookPath}"`, { 
      cwd: projectRoot, 
      stdio: 'inherit',
      timeout: 60000
    });
    console.log(`✅ Hook test completed: ${hookName}`);
  } catch (error) {
    console.log(`❌ Hook test failed: ${hookName}`);
    process.exit(1);
  }
}

// Execute commands
switch (command) {
  case 'list':
    listHooks();
    break;
  case 'status':
    showStatus();
    break;
  case 'install':
    installHooks();
    break;
  case 'uninstall':
    uninstallHooks();
    break;
  case 'enable':
    toggleHook(args[1], true);
    break;
  case 'disable':
    toggleHook(args[1], false);
    break;
  case 'config':
    showConfig();
    break;
  case 'test':
    testHook(args[1]);
    break;
  default:
    console.log(`❌ Unknown command: ${command}`);
    console.log('Run "node hook-manager.js help" for usage information');
    process.exit(1);
}