#!/usr/bin/env node

/**
 * Install Hooks Script
 * Sets up git hooks for the project
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync, copyFileSync, chmodSync, mkdirSync, writeFileSync } from 'fs';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🪝 Installing Git Hooks');
console.log('=======================\n');

// Check if we're in a git repository
function checkGitRepo() {
  try {
    execSync('git rev-parse --git-dir', { cwd: projectRoot, stdio: 'pipe' });
    console.log('✅ Git repository detected');
    return true;
  } catch {
    console.log('❌ Not a git repository. Initialize with: git init');
    return false;
  }
}

// Install a specific hook
function installHook(hookName) {
  const sourceHook = join(__dirname, hookName);
  const gitHooksDir = join(projectRoot, '.git', 'hooks');
  const targetHook = join(gitHooksDir, hookName);
  
  if (!existsSync(sourceHook)) {
    console.log(`⚠️  Hook '${hookName}' not found in hooks directory`);
    return false;
  }
  
  if (!existsSync(gitHooksDir)) {
    mkdirSync(gitHooksDir, { recursive: true });
  }
  
  try {
    copyFileSync(sourceHook, targetHook);
    chmodSync(targetHook, 0o755);
    console.log(`✅ Installed ${hookName} hook`);
    return true;
  } catch (error) {
    console.log(`❌ Failed to install ${hookName}: ${error.message}`);
    return false;
  }
}

// Create post-install hook
function createPostInstallHook() {
  const postInstallHook = join(__dirname, 'post-install');
  const hookContent = `#!/bin/sh
# Post-install hook - runs after npm install

echo "🔧 Running post-install setup..."

# Ensure logs directory exists
mkdir -p logs

# Ensure screenshots directory exists  
mkdir -p screenshots

# Check if build directory exists, if not suggest building
if [ ! -d "build" ]; then
    echo "💡 Run 'npm run build' to compile TypeScript"
fi

# Check for .env file
if [ ! -f ".env" ]; then
    echo "💡 Create .env file with your OPENAI_API_KEY"
fi

echo "✅ Post-install setup complete"
`;

  try {
    writeFileSync(postInstallHook, hookContent);
    chmodSync(postInstallHook, 0o755);
    console.log('✅ Created post-install hook');
    return true;
  } catch (error) {
    console.log(`❌ Failed to create post-install hook: ${error.message}`);
    return false;
  }
}

// Create pre-push hook for safety checks
function createPrePushHook() {
  const prePushHook = join(__dirname, 'pre-push');
  const hookContent = `#!/bin/sh
# Pre-push hook - additional safety checks before pushing

echo "🚀 Running pre-push safety checks..."

# Run health check
echo "🏥 Running health check..."
if node Commands/health-check.js > /dev/null 2>&1; then
    echo "✅ Health check passed"
else
    echo "⚠️  Health check failed - pushing anyway (review recommended)"
fi

# Check for secrets in staged files
echo "🔍 Checking for secrets..."
if git diff --cached --name-only | xargs grep -l "api.*key\\|secret\\|password\\|token" 2>/dev/null; then
    echo "❌ Potential secrets detected in staged files!"
    echo "Please review and remove any sensitive data before pushing."
    exit 1
fi

# Check if monitoring is active (for production pushes)
if [ -f "logs/scraping-monitor.json" ]; then
    echo "📊 Monitoring system active"
fi

echo "✅ Pre-push safety checks passed!"
exit 0
`;

  try {
    writeFileSync(prePushHook, hookContent);
    chmodSync(prePushHook, 0o755);
    console.log('✅ Created pre-push hook');
    return true;
  } catch (error) {
    console.log(`❌ Failed to create pre-push hook: ${error.message}`);
    return false;
  }
}

// Main installation process
async function installHooks() {
  if (!checkGitRepo()) {
    process.exit(1);
  }
  
  console.log('\n📦 Installing available hooks...');
  
  const hooks = ['pre-commit'];
  let installed = 0;
  
  hooks.forEach(hook => {
    if (installHook(hook)) {
      installed++;
    }
  });
  
  // Create additional hooks
  createPostInstallHook();
  createPrePushHook();
  
  // Install the new hooks
  if (installHook('post-install')) installed++;
  if (installHook('pre-push')) installed++;
  
  console.log(`\n📊 Installation Summary:`);
  console.log(`   Hooks installed: ${installed}`);
  console.log(`   Git hooks directory: .git/hooks/`);
  
  if (installed > 0) {
    console.log('\n💡 Hooks are now active! They will run automatically on:');
    console.log('   • pre-commit: Before each commit (linting, formatting, build)');
    console.log('   • pre-push: Before each push (health check, secret scan)');
    console.log('   • post-install: After npm install (directory setup)');
  }
  
  console.log('\n✅ Hook installation complete!');
}

// Run installation
installHooks().catch(error => {
  console.error('❌ Hook installation failed:', error.message);
  process.exit(1);
});