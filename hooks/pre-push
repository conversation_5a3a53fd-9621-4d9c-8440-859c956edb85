#!/bin/sh
# Pre-push hook - additional safety checks before pushing

echo "🚀 Running pre-push safety checks..."

# Run health check
echo "🏥 Running health check..."
if node Commands/health-check.js > /dev/null 2>&1; then
    echo "✅ Health check passed"
else
    echo "⚠️  Health check failed - pushing anyway (review recommended)"
fi

# Check for secrets in staged files
echo "🔍 Checking for secrets..."
if git diff --cached --name-only | xargs grep -l "api.*key\|secret\|password\|token" 2>/dev/null; then
    echo "❌ Potential secrets detected in staged files!"
    echo "Please review and remove any sensitive data before pushing."
    exit 1
fi

# Check if monitoring is active (for production pushes)
if [ -f "logs/scraping-monitor.json" ]; then
    echo "📊 Monitoring system active"
fi

echo "✅ Pre-push safety checks passed!"
exit 0
