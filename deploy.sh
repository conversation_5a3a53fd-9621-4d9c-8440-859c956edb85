#!/bin/bash

# MCP X & Reddit Scraper - Self-Contained Deployment Script
# This script sets up the MCP server to run remotely without local installations

set -e

echo "🚀 MCP X & Reddit Scraper - Remote Deployment"
echo "================================================"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is required for remote deployment"
    echo "Please install Docker: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if required environment variables are set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  OPENAI_API_KEY not set in environment"
    read -p "Enter your OpenAI API Key: " OPENAI_API_KEY
    export OPENAI_API_KEY
fi

echo "✅ OpenAI API Key configured"

# Build the Docker container
echo "🏗️  Building Docker container..."
docker build -t mcp-x-scraper:latest .

# Stop existing container if running
echo "🛑 Stopping existing containers..."
docker-compose down 2>/dev/null || true

# Start the services
echo "🚀 Starting MCP server..."
docker-compose up -d

# Wait for container to be ready
echo "⏳ Waiting for server to start..."
sleep 5

# Test the deployment
echo "🧪 Testing deployment..."
if docker exec mcp-x-scraper node -e "
const { spawn } = require('child_process');
const proc = spawn('node', ['build/index.js']);
setTimeout(() => {
  proc.stdin.write(JSON.stringify({jsonrpc:'2.0',id:1,method:'tools/list',params:{}}) + '\\n');
  setTimeout(() => proc.kill(), 2000);
}, 1000);
proc.stdout.on('data', (data) => {
  if (data.toString().includes('tools')) {
    console.log('✅ MCP Server is responding');
    process.exit(0);
  }
});
proc.on('exit', () => process.exit(1));
" 2>/dev/null; then
    echo "🎉 Deployment successful!"
else
    echo "⚠️  Server started but may need configuration"
fi

# Show container status
echo ""
echo "📊 Container Status:"
docker ps --filter name=mcp-x-scraper --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Show logs
echo ""
echo "📝 Recent Logs:"
docker logs mcp-x-scraper --tail 20

echo ""
echo "🎯 Deployment Complete!"
echo "================================================"
echo "Server is running in Docker container: mcp-x-scraper"
echo ""
echo "📱 Usage:"
echo "  • View logs: docker logs -f mcp-x-scraper"
echo "  • Stop server: docker-compose down"
echo "  • Restart: docker-compose restart"
echo ""
echo "🔧 Configuration:"
echo "  • API Key: Set via OPENAI_API_KEY environment variable"
echo "  • Patterns: Add custom patterns to ./custom-patterns/"
echo "  • Screenshots: Saved to ./screenshots/"
echo ""
echo "📚 Tools Available: 25+ tools including:"
echo "  • X/Twitter scraping (5 tools)"
echo "  • Reddit scraping (6 tools)"
echo "  • Fabric patterns (6 tools)"  
echo "  • AI agent chat (6 tools)"
echo "  • Browser automation (5+ tools)"