export interface SystemHealth {
  timestamp: number;
  score: {
    overall: number;
    categories: {
      performance: number;
      reliability: number;
      quality: number;
      security: number;
      cost: number;
    };
    trend: 'improving' | 'stable' | 'declining';
  };
  metrics: {
    uptime: number;
    successRate: number;
    blockRate: number;
    dataQuality: number;
    avgResponseTime: number;
    errorRate: number;
    costEfficiency: number;
  };
  issues: HealthIssue[];
  recommendations: string[];
  alerts: Alert[];
}

export interface LiveMetrics {
  requestsPerMinute: number;
  successRate: number;
  blockRate: number;
  avgResponseTime: number;
  activeTargets: number;
  queueSize: number;
  timestamp: number;
}

export interface Alert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  severity: 'critical' | 'high' | 'medium' | 'low';
  title: string;
  message: string;
  timestamp: number;
  acknowledged: boolean;
  source: string;
}

export interface SearchFilters {
  dateRange: {
    start: Date;
    end: Date;
  };
  platforms: ('twitter' | 'reddit')[];
  keywords: string[];
  contentType: 'all' | 'posts' | 'comments' | 'reports';
  sentiment?: 'positive' | 'negative' | 'neutral';
}

export interface ReportMetadata {
  id: string;
  title: string;
  type: 'analysis' | 'summary' | 'insights' | 'benchmark';
  platform: 'twitter' | 'reddit' | 'combined';
  createdAt: Date;
  size: number;
  tags: string[];
  thumbnail?: string;
}