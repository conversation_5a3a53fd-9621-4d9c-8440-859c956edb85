import { useState, useEffect, useCallback } from 'react';
import { SystemHealth, LiveMetrics } from '../types/dashboard';

export function useSystemHealth() {
  const [health, setHealth] = useState<SystemHealth | null>(null);
  const [liveMetrics, setLiveMetrics] = useState<LiveMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchHealth = useCallback(async () => {
    try {
      const response = await fetch('/api/health');
      if (!response.ok) throw new Error('Failed to fetch health data');
      const data = await response.json();
      setHealth(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  }, []);

  const fetchLiveMetrics = useCallback(async () => {
    try {
      const response = await fetch('/api/metrics/live');
      if (!response.ok) throw new Error('Failed to fetch live metrics');
      const data = await response.json();
      setLiveMetrics(data);
    } catch (err) {
      console.error('Failed to fetch live metrics:', err);
    }
  }, []);

  useEffect(() => {
    const initializeData = async () => {
      setLoading(true);
      await Promise.all([fetchHealth(), fetchLiveMetrics()]);
      setLoading(false);
    };

    initializeData();

    // Set up polling for live updates
    const healthInterval = setInterval(fetchHealth, 30000); // 30 seconds
    const metricsInterval = setInterval(fetchLiveMetrics, 5000); // 5 seconds

    return () => {
      clearInterval(healthInterval);
      clearInterval(metricsInterval);
    };
  }, [fetchHealth, fetchLiveMetrics]);

  return { health, liveMetrics, loading, error, refetch: fetchHealth };
}