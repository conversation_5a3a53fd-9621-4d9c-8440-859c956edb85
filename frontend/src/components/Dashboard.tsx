import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from './ui/tabs';
import { SystemOverview } from './SystemOverview';
import { AlertsPanel } from './AlertsPanel';
import { SearchInterface } from './SearchInterface';
import { ReportGallery } from './ReportGallery';
import { AIChatSidebar } from './AIChatSidebar';
import { useSystemHealth } from '../hooks/useSystemHealth';
import { ErrorBoundary } from './ErrorBoundary';
import { LoadingSpinner } from './ui/loading-spinner';

export function Dashboard() {
  const { health, liveMetrics, loading, error } = useSystemHealth();
  const [chatOpen, setChatOpen] = useState(false);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-lg">Loading dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">
            Dashboard Error
          </h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  MCP Scraper Dashboard
                </h1>
                <div className="ml-4 flex items-center">
                  <div className={`w-3 h-3 rounded-full ${
                    health?.score.overall >= 80 ? 'bg-green-500' :
                    health?.score.overall >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <span className="ml-2 text-sm text-gray-600 dark:text-gray-300">
                    System Health: {health?.score.overall || 0}/100
                  </span>
                </div>
              </div>
              <button
                onClick={() => setChatOpen(!chatOpen)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                AI Assistant
              </button>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="search">Search</TabsTrigger>
              <TabsTrigger value="reports">Reports</TabsTrigger>
              <TabsTrigger value="alerts">Alerts</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <SystemOverview health={health} liveMetrics={liveMetrics} />
            </TabsContent>

            <TabsContent value="search" className="space-y-6">
              <SearchInterface />
            </TabsContent>

            <TabsContent value="reports" className="space-y-6">
              <ReportGallery />
            </TabsContent>

            <TabsContent value="alerts" className="space-y-6">
              <AlertsPanel alerts={health?.alerts || []} />
            </TabsContent>
          </Tabs>
        </main>

        <AIChatSidebar open={chatOpen} onClose={() => setChatOpen(false)} />
      </div>
    </ErrorBoundary>
  );
}