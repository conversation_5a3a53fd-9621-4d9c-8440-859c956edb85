import React from 'react';

export default function Dashboard() {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Dashboard Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900">System Status</h3>
            <p className="text-blue-700">All systems operational</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-green-900">Active Tools</h3>
            <p className="text-green-700">49 tools registered</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-purple-900">Health Score</h3>
            <p className="text-purple-700">85/100</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="text-gray-700">System health check completed</span>
            <span className="text-sm text-gray-500">2 minutes ago</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="text-gray-700">49 tools successfully registered</span>
            <span className="text-sm text-gray-500">5 minutes ago</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="text-gray-700">Handler modules loaded</span>
            <span className="text-sm text-gray-500">5 minutes ago</span>
          </div>
        </div>
      </div>
    </div>
  );
}
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">
            Dashboard Error
          </h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  MCP Scraper Dashboard
                </h1>
                <div className="ml-4 flex items-center">
                  <div className={`w-3 h-3 rounded-full ${
                    health?.score.overall >= 80 ? 'bg-green-500' :
                    health?.score.overall >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <span className="ml-2 text-sm text-gray-600 dark:text-gray-300">
                    System Health: {health?.score.overall || 0}/100
                  </span>
                </div>
              </div>
              <button
                onClick={() => setChatOpen(!chatOpen)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                AI Assistant
              </button>
            </div>
          </div>
        </header>

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="search">Search</TabsTrigger>
              <TabsTrigger value="reports">Reports</TabsTrigger>
              <TabsTrigger value="alerts">Alerts</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <SystemOverview health={health} liveMetrics={liveMetrics} />
            </TabsContent>

            <TabsContent value="search" className="space-y-6">
              <SearchInterface />
            </TabsContent>

            <TabsContent value="reports" className="space-y-6">
              <ReportGallery />
            </TabsContent>

            <TabsContent value="alerts" className="space-y-6">
              <AlertsPanel alerts={health?.alerts || []} />
            </TabsContent>
          </Tabs>
        </main>

        <AIChatSidebar open={chatOpen} onClose={() => setChatOpen(false)} />
      </div>
    </ErrorBoundary>
  );
}