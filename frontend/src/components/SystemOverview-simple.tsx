import React from 'react';

export default function SystemOverview() {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">System Overview</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-green-900">System Health</h3>
            <p className="text-2xl font-bold text-green-700">95%</p>
            <p className="text-sm text-green-600">All systems operational</p>
          </div>
          
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900">Active Tools</h3>
            <p className="text-2xl font-bold text-blue-700">49</p>
            <p className="text-sm text-blue-600">Tools registered</p>
          </div>
          
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-purple-900">Handler Modules</h3>
            <p className="text-2xl font-bold text-purple-700">6</p>
            <p className="text-sm text-purple-600">Modules loaded</p>
          </div>
          
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-yellow-900">Browser Pool</h3>
            <p className="text-2xl font-bold text-yellow-700">4</p>
            <p className="text-sm text-yellow-600">Browsers active</p>
          </div>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Component Status</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="text-gray-700">Tool Registry</span>
            <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">Operational</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="text-gray-700">Python Bridge</span>
            <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">Connected</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="text-gray-700">Fabric Integration</span>
            <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">Available</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
            <span className="text-gray-700">Browser Pool</span>
            <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm">Ready</span>
          </div>
        </div>
      </div>
    </div>
  );
}
