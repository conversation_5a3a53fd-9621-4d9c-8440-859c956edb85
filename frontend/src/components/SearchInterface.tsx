import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Calendar } from './ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Search, Filter, Download, Calendar as CalendarIcon, X } from 'lucide-react';
import { SearchFilters } from '../types/dashboard';
import { format } from 'date-fns';

interface SearchResult {
  id: string;
  title: string;
  content: string;
  platform: 'twitter' | 'reddit';
  author: string;
  timestamp: Date;
  sentiment: 'positive' | 'negative' | 'neutral';
  engagement: number;
  tags: string[];
}

export function SearchInterface() {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({
    dateRange: {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      end: new Date()
    },
    platforms: ['twitter', 'reddit'],
    keywords: [],
    contentType: 'all'
  });
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalResults, setTotalResults] = useState(0);

  const performSearch = async () => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      const searchParams = new URLSearchParams({
        q: query,
        platforms: filters.platforms.join(','),
        contentType: filters.contentType,
        startDate: filters.dateRange.start.toISOString(),
        endDate: filters.dateRange.end.toISOString(),
        ...(filters.sentiment && { sentiment: filters.sentiment })
      });

      const response = await fetch(`/api/search?${searchParams}`);
      if (!response.ok) throw new Error('Search failed');

      const data = await response.json();
      setResults(data.results);
      setTotalResults(data.total);
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
      setTotalResults(0);
    } finally {
      setLoading(false);
    }
  };

  const addKeyword = (keyword: string) => {
    if (keyword && !filters.keywords.includes(keyword)) {
      setFilters(prev => ({
        ...prev,
        keywords: [...prev.keywords, keyword]
      }));
    }
  };

  const removeKeyword = (keyword: string) => {
    setFilters(prev => ({
      ...prev,
      keywords: prev.keywords.filter(k => k !== keyword)
    }));
  };

  const exportResults = () => {
    const exportData = {
      query,
      filters,
      results,
      timestamp: new Date().toISOString(),
      totalResults
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `search-results-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'negative': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Search Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Content Search
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Search */}
          <div className="flex gap-2">
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search scraped content, reports, and analysis..."
              onKeyPress={(e) => e.key === 'Enter' && performSearch()}
              className="flex-1"
            />
            <Button onClick={performSearch} disabled={loading}>
              <Search className="w-4 h-4 mr-2" />
              Search
            </Button>
          </div>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Platform Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Platforms</label>
              <div className="flex gap-2">
                {['twitter', 'reddit'].map((platform) => (
                  <Button
                    key={platform}
                    variant={filters.platforms.includes(platform as any) ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => {
                      const newPlatforms = filters.platforms.includes(platform as any)
                        ? filters.platforms.filter(p => p !== platform)
                        : [...filters.platforms, platform as any];
                      setFilters(prev => ({ ...prev, platforms: newPlatforms }));
                    }}
                  >
                    {platform}
                  </Button>
                ))}
              </div>
            </div>

            {/* Content Type Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Content Type</label>
              <Select
                value={filters.contentType}
                onValueChange={(value) => setFilters(prev => ({ ...prev, contentType: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Content</SelectItem>
                  <SelectItem value="posts">Posts Only</SelectItem>
                  <SelectItem value="comments">Comments Only</SelectItem>
                  <SelectItem value="reports">Reports Only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date Range */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Date Range</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {format(filters.dateRange.start, 'MMM dd')} - {format(filters.dateRange.end, 'MMM dd')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="range"
                    selected={{
                      from: filters.dateRange.start,
                      to: filters.dateRange.end
                    }}
                    onSelect={(range) => {
                      if (range?.from && range?.to) {
                        setFilters(prev => ({
                          ...prev,
                          dateRange: { start: range.from!, end: range.to! }
                        }));
                      }
                    }}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Sentiment Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Sentiment</label>
              <Select
                value={filters.sentiment || 'all'}
                onValueChange={(value) => setFilters(prev => ({ 
                  ...prev, 
                  sentiment: value === 'all' ? undefined : value as any 
                }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sentiments</SelectItem>
                  <SelectItem value="positive">Positive</SelectItem>
                  <SelectItem value="negative">Negative</SelectItem>
                  <SelectItem value="neutral">Neutral</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Keywords */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Keywords</label>
            <div className="flex flex-wrap gap-2 mb-2">
              {filters.keywords.map((keyword) => (
                <Badge key={keyword} variant="secondary" className="flex items-center gap-1">
                  {keyword}
                  <X 
                    className="w-3 h-3 cursor-pointer" 
                    onClick={() => removeKeyword(keyword)}
                  />
                </Badge>
              ))}
            </div>
            <Input
              placeholder="Add keywords (press Enter)"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  addKeyword((e.target as HTMLInputElement).value);
                  (e.target as HTMLInputElement).value = '';
                }
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>
            Search Results {totalResults > 0 && `(${totalResults} found)`}
          </CardTitle>
          {results.length > 0 && (
            <Button variant="outline" onClick={exportResults}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Searching...</p>
            </div>
          ) : results.length > 0 ? (
            <div className="space-y-4">
              {results.map((result) => (
                <div key={result.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{result.platform}</Badge>
                      <Badge className={getSentimentColor(result.sentiment)}>
                        {result.sentiment}
                      </Badge>
                      <span className="text-sm text-gray-600">by {result.author}</span>
                    </div>
                    <span className="text-sm text-gray-500">
                      {format(result.timestamp, 'MMM dd, yyyy HH:mm')}
                    </span>
                  </div>
                  
                  <h3 className="font-medium mb-2">{result.title}</h3>
                  <p className="text-gray-700 text-sm mb-3 line-clamp-3">{result.content}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex flex-wrap gap-1">
                      {result.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <span className="text-sm text-gray-500">
                      {result.engagement} engagements
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : query ? (
            <div className="text-center py-8 text-gray-500">
              No results found for "{query}"
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Enter a search query to find content
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}