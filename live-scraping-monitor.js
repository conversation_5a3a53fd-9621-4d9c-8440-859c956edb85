#!/usr/bin/env node

import { spawn } from 'child_process';
import { createWriteStream } from 'fs';

console.log('🕷️  Live Scraping Monitor with Real-Time Error Tracking');
console.log('=====================================================');

// Create log file for monitoring
const logStream = createWriteStream('./scraping-monitor.log', { flags: 'a' });

function logWithTimestamp(level, message) {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${level}: ${message}\n`;
  logStream.write(logEntry);
  
  const color = {
    'INFO': '\x1b[36m',    // Cyan
    'WARN': '\x1b[33m',    // Yellow  
    'ERROR': '\x1b[31m',   // Red
    'SUCCESS': '\x1b[32m', // Green
    'SCRAPING': '\x1b[35m' // Magenta
  };
  
  console.log(`${color[level] || ''}[${timestamp.substring(11,19)}] ${level}: ${message}\x1b[0m`);
}

const serverProcess = spawn('node', ['build/index.js'], {
  stdio: ['pipe', 'pipe', 'pipe'],
  cwd: process.cwd()
});

let requestCount = 0;
let successCount = 0;
let errorCount = 0;
let scrapingResults = [];

serverProcess.stdout.on('data', (data) => {
  const output = data.toString();
  
  if (output.includes('"result"')) {
    try {
      const jsonMatch = output.match(/{"result".*?}(?=\n|$)/);
      if (jsonMatch) {
        const response = JSON.parse(jsonMatch[0]);
        requestCount++;
        
        if (response.result && response.result.content) {
          const content = response.result.content[0].text;
          
          if (content.includes('Error') || content.includes('Failed') || content.includes('❌')) {
            errorCount++;
            logWithTimestamp('ERROR', content.substring(0, 200));
          } else if (content.includes('@') || content.includes('reddit.com') || content.includes('posts')) {
            successCount++;
            logWithTimestamp('SUCCESS', `Scraping successful - ${content.substring(0, 100)}...`);
            scrapingResults.push(content.substring(0, 200));
          } else {
            logWithTimestamp('INFO', content.substring(0, 150));
          }
        }
      }
    } catch (e) {
      errorCount++;
      logWithTimestamp('ERROR', `JSON parsing failed: ${e.message}`);
    }
  }
});

serverProcess.stderr.on('data', (data) => {
  const error = data.toString();
  if (!error.includes('X Scraper MCP server running')) {
    logWithTimestamp('WARN', error.trim());
  } else {
    logWithTimestamp('INFO', 'MCP server initialized');
  }
});

// Error monitoring
serverProcess.on('error', (error) => {
  logWithTimestamp('ERROR', `Process error: ${error.message}`);
});

serverProcess.on('exit', (code) => {
  logWithTimestamp('INFO', `Server process exited with code ${code}`);
});

// Initialize server
setTimeout(() => {
  logWithTimestamp('INFO', 'Initializing MCP server for live scraping tests...');
  serverProcess.stdin.write(JSON.stringify({
    jsonrpc: "2.0", id: 0, method: "initialize",
    params: { protocolVersion: "2024-11-05", capabilities: {}, clientInfo: { name: "monitor", version: "1.0" } }
  }) + '\n');
}, 1000);

// Status reporting every 5 seconds
const statusInterval = setInterval(() => {
  const successRate = requestCount > 0 ? ((successCount / requestCount) * 100).toFixed(1) : 0;
  logWithTimestamp('INFO', `Status - Requests: ${requestCount}, Success: ${successCount}, Errors: ${errorCount}, Success Rate: ${successRate}%`);
}, 5000);

function testScraping(testName, params, delay) {
  setTimeout(() => {
    logWithTimestamp('SCRAPING', `Starting ${testName}...`);
    serverProcess.stdin.write(JSON.stringify({
      jsonrpc: "2.0",
      id: Date.now(),
      method: "tools/call",
      params: params
    }) + '\n');
  }, delay);
}

// Schedule live scraping tests with monitoring
logWithTimestamp('INFO', '🚨 About to begin LIVE SCRAPING TESTS with real data');
logWithTimestamp('INFO', '📊 Monitoring: Success rate, error types, response times');
logWithTimestamp('INFO', '📝 All results logged to scraping-monitor.log');

console.log('\n⚠️  LIVE SCRAPING ABOUT TO START');
console.log('================================');
console.log('🎯 Target: Real Twitter/Reddit data');
console.log('📊 Monitoring: Errors, success rates, timeouts');
console.log('📝 Logging: All results saved to scraping-monitor.log');
console.log('⏱️  Tests will run every 3 seconds');
console.log('\n🔍 Press Ctrl+C to stop monitoring\n');

// Test 1: Try real Twitter user (Karpathy - from our targets)
testScraping('Twitter Comments (Karpathy)', {
  name: "get_comments",
  arguments: { username: "karpathy", limit: 2, includeReplies: false }
}, 3000);

// Test 2: Try Reddit search (MachineLearning - from our targets)  
testScraping('Reddit Search (MachineLearning)', {
  name: "search_reddit", 
  arguments: { query: "transformer", subreddit: "MachineLearning", limit: 2 }
}, 6000);

// Test 3: Try getting trending topics
testScraping('Twitter Trending Topics', {
  name: "get_trending_topics",
  arguments: { category: "tech" }
}, 9000);

// Test 4: Try subreddit posts
testScraping('Reddit Posts (technology)', {
  name: "get_subreddit_posts",
  arguments: { subreddit: "technology", sort: "hot", limit: 2 }
}, 12000);

// Test 5: Try key contributors discovery
testScraping('Key Contributors Discovery', {
  name: "get_key_contributors", 
  arguments: { topic: "artificial intelligence", limit: 3 }
}, 15000);

// Final report
setTimeout(() => {
  clearInterval(statusInterval);
  
  logWithTimestamp('INFO', '=== LIVE SCRAPING TEST COMPLETE ===');
  logWithTimestamp('INFO', `Total Requests: ${requestCount}`);
  logWithTimestamp('INFO', `Successful: ${successCount}`);
  logWithTimestamp('INFO', `Errors: ${errorCount}`);
  logWithTimestamp('INFO', `Success Rate: ${requestCount > 0 ? ((successCount / requestCount) * 100).toFixed(1) : 0}%`);
  
  console.log('\n🎯 Live Scraping Test Results:');
  console.log(`   Total Tests: ${requestCount}`);
  console.log(`   Successful: ${successCount}`);
  console.log(`   Errors: ${errorCount}`);
  console.log(`   Success Rate: ${requestCount > 0 ? ((successCount / requestCount) * 100).toFixed(1) : 0}%`);
  
  if (scrapingResults.length > 0) {
    console.log('\n✅ Sample Results:');
    scrapingResults.slice(0, 3).forEach((result, i) => {
      console.log(`   ${i+1}. ${result}...`);
    });
  }
  
  console.log('\n📝 Full log saved to: scraping-monitor.log');
  console.log('🔍 Check log file for detailed error analysis');
  
  logStream.end();
  serverProcess.kill();
  process.exit(0);
}, 20000);

// Handle Ctrl+C
process.on('SIGINT', () => {
  logWithTimestamp('INFO', 'Monitoring stopped by user');
  clearInterval(statusInterval);
  logStream.end();
  serverProcess.kill();
  process.exit(0);
});