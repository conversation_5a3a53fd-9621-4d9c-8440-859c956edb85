{"$schema": "https://raw.githubusercontent.com/modelcontextprotocol/specification/main/schema/mcp_config_schema.json", "mcpServers": {"x-reddit-scraper": {"command": "node", "args": ["./build/index.js"], "description": "Advanced MCP server for X (Twitter) and Reddit scraping with AI analysis", "env": {"OPENAI_API_KEY": "${OPENAI_API_KEY}", "DEFAULT_MODEL": "${DEFAULT_MODEL:-gpt-4.1}", "DEFAULT_VENDOR": "${DEFAULT_VENDOR:-OpenAI}", "NODE_ENV": "production"}}}, "tools": [{"name": "get_comments", "description": "Scrape comments from specific X (Twitter) users"}, {"name": "get_key_contributors", "description": "Find influential users for topics/hashtags"}, {"name": "get_trending_topics", "description": "Retrieve trending topics with category filtering"}, {"name": "analyze_sentiment", "description": "Perform sentiment analysis on posts"}, {"name": "get_subreddit_posts", "description": "Get posts from specific subreddits"}, {"name": "get_reddit_comments", "description": "Extract comments from Reddit posts"}, {"name": "search_reddit", "description": "Search posts across Reddit or within subreddits"}, {"name": "analyze_reddit_trends", "description": "Analyze Reddit posting patterns and engagement metrics"}, {"name": "create_chat_session", "description": "Create AI chat session for analysis"}, {"name": "chat_with_agent", "description": "Send messages to AI agent for analysis and reports"}, {"name": "chat", "description": "Simple chat interface with AI assistant"}, {"name": "ai", "description": "Direct AI chat - alternative interface"}, {"name": "ask", "description": "Ask the AI a question - simple interface"}, {"name": "talk", "description": "Talk to the AI assistant directly"}, {"name": "export_report", "description": "Export AI-generated reports in multiple formats"}, {"name": "apply_fabric_pattern", "description": "Apply Fabric AI patterns to content analysis"}, {"name": "chain_fabric_patterns", "description": "Chain multiple Fabric patterns for complex analysis"}, {"name": "navigate", "description": "Navigate to URLs with browser automation"}, {"name": "screenshot", "description": "Capture screenshots of web pages or elements"}], "requirements": {"node": ">=18.0.0", "env_vars": ["OPENAI_API_KEY"], "optional_env_vars": ["DEFAULT_MODEL", "DEFAULT_VENDOR"]}, "installation": {"npm": "npm install mcp-x-reddit-scraper", "global": "npm install -g mcp-x-reddit-scraper", "build": "npm run build", "install_mcp": "npm run install-mcp"}}