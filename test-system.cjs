#!/usr/bin/env node

/**
 * Comprehensive Test Suite for MCP X/Reddit Scraper
 * Tests all major components and functionality
 */

const { ToolRegistry } = require('./build/tool-registry.js');
const { ConfigValidator } = require('./build/config-validator.js');

console.log('🧪 MCP X/Reddit Scraper - Comprehensive Test Suite\n');

async function testToolRegistry() {
  console.log('📋 Testing Tool Registry...');
  
  try {
    const registry = new ToolRegistry();
    const tools = registry.getTools();
    
    console.log(`✅ Tools registered: ${tools.length}`);
    
    // Test tool categories
    const categories = {
      twitter: tools.filter(t => ['get_comments', 'get_key_contributors', 'get_trending_topics', 'analyze_sentiment', 'get_trending_keywords', 'analyze_keywords'].includes(t.name)),
      reddit: tools.filter(t => ['get_subreddit_posts', 'get_reddit_comments', 'get_reddit_trending', 'search_reddit', 'analyze_reddit_trends', 'analyze_reddit_comments'].includes(t.name)),
      browser: tools.filter(t => ['navigate', 'screenshot', 'click', 'fill', 'scroll', 'analyze_page_visually'].includes(t.name)),
      ai: tools.filter(t => ['create_chat_session', 'chat_with_agent', '@chat', 'get_session_info', 'export_report'].includes(t.name)),
      fabric: tools.filter(t => ['list_fabric_patterns', 'apply_fabric_pattern', 'chain_fabric_patterns', 'create_custom_pattern', 'analyze_with_fabric', 'batch_apply_pattern'].includes(t.name)),
      target: tools.filter(t => ['list_targets', 'get_target_categories', 'add_influencer', 'add_subreddit', 'remove_target', 'get_top_targets', 'batch_analyze_targets', 'discover_new_targets', 'get_recent_discoveries', 'promote_discovered_target', 'update_target_relevance'].includes(t.name)),
      enhanced: tools.filter(t => ['get_system_health', 'get_kpi_snapshot', 'get_blocking_stats', 'get_enhancement_stats', 'generate_enhancement_proposal', 'get_research_summary', 'send_notification', 'get_dashboard_data', 'test_notification_channel'].includes(t.name))
    };
    
    console.log('📊 Tool Distribution:');
    Object.entries(categories).forEach(([category, categoryTools]) => {
      console.log(`   ${category}: ${categoryTools.length} tools`);
    });
    
    // Verify expected tool count
    const expectedTotal = Object.values(categories).reduce((sum, cat) => sum + cat.length, 0);
    if (expectedTotal === 49) {
      console.log('✅ All 49 tools properly categorized');
    } else {
      console.log(`⚠️  Tool count mismatch: expected 49, found ${expectedTotal}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Tool Registry test failed:', error.message);
    return false;
  }
}

async function testConfigValidation() {
  console.log('\n🔧 Testing Configuration Validation...');
  
  try {
    const validator = new ConfigValidator();
    const results = await validator.validateAll();
    
    const success = results.filter(r => r.status === 'success').length;
    const warnings = results.filter(r => r.status === 'warning').length;
    const errors = results.filter(r => r.status === 'error').length;
    
    console.log(`✅ Validation completed: ${results.length} checks`);
    console.log(`   Success: ${success}, Warnings: ${warnings}, Errors: ${errors}`);
    
    if (errors === 0) {
      console.log('✅ No critical configuration errors');
      return true;
    } else {
      console.log('⚠️  Configuration has errors that need attention');
      results.filter(r => r.status === 'error').forEach(r => {
        console.log(`   ❌ ${r.component}: ${r.message}`);
      });
      return false;
    }
  } catch (error) {
    console.error('❌ Configuration validation failed:', error.message);
    return false;
  }
}

async function testHandlerModules() {
  console.log('\n🔄 Testing Handler Modules...');
  
  try {
    // Test that handler modules can be imported
    const handlers = [
      './build/handlers/twitter-handlers.js',
      './build/handlers/reddit-handlers.js', 
      './build/handlers/browser-handlers.js',
      './build/handlers/ai-handlers.js',
      './build/handlers/target-handlers.js',
      './build/handlers/enhanced-handlers.js'
    ];
    
    let successCount = 0;
    for (const handlerPath of handlers) {
      try {
        require(handlerPath);
        successCount++;
      } catch (error) {
        console.log(`❌ Failed to load ${handlerPath}: ${error.message}`);
      }
    }
    
    console.log(`✅ Handler modules loaded: ${successCount}/${handlers.length}`);
    return successCount === handlers.length;
  } catch (error) {
    console.error('❌ Handler module test failed:', error.message);
    return false;
  }
}

async function testSystemIntegration() {
  console.log('\n🔗 Testing System Integration...');
  
  try {
    // Test that main components can be initialized
    const { ComponentManager } = require('./build/component-manager.js');
    const componentManager = new ComponentManager();
    
    console.log('✅ Component Manager initialized');
    
    // Test tool registry integration
    const registry = new ToolRegistry();
    const hasHandler = registry.hasHandler('enhanced');
    
    if (hasHandler) {
      console.log('✅ Handler integration working');
    } else {
      console.log('⚠️  Handler integration issue detected');
    }
    
    return true;
  } catch (error) {
    console.error('❌ System integration test failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  const tests = [
    { name: 'Tool Registry', fn: testToolRegistry },
    { name: 'Configuration Validation', fn: testConfigValidation },
    { name: 'Handler Modules', fn: testHandlerModules },
    { name: 'System Integration', fn: testSystemIntegration }
  ];
  
  let passed = 0;
  let total = tests.length;
  
  for (const test of tests) {
    const result = await test.fn();
    if (result) passed++;
  }
  
  console.log('\n📊 Test Summary:');
  console.log(`   Passed: ${passed}/${total}`);
  console.log(`   Success Rate: ${Math.round((passed/total) * 100)}%`);
  
  if (passed === total) {
    console.log('\n🎉 All tests passed! System is ready for use.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the output above for details.');
  }
  
  return passed === total;
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testToolRegistry, testConfigValidation, testHandlerModules, testSystemIntegration };
