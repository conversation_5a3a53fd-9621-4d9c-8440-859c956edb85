{"permissions": {"allow": ["Bash(npm run build:*)", "Bash(node:*)", "Bash(npm run:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(npm install:*)", "Bash(git push:*)", "<PERSON><PERSON>(mkdir:*)", "WebFetch(domain:github.com)", "Bash(git checkout:*)", "Bash(gh pr create:*)", "Bash(git stash push:*)", "Bash(npm view:*)", "Bash(npm start)", "Bash(kill:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm start:*)", "Bash([ -n \"$OPENAI_API_KEY\" ])", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(git pull:*)", "Bash(git merge:*)", "<PERSON><PERSON>(chmod:*)", "Bash(gh pr:*)", "Bash(git branch:*)", "Bash(grep:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["x-reddit-scraper"]}