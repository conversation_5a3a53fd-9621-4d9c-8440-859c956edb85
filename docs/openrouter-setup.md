# OpenRouter Setup Guide

## Overview

OpenRouter provides access to multiple LLM providers through a single API, allowing you to use models from <PERSON>throp<PERSON> (Claude), Google (Gemini), Meta (Llama), and many others without managing multiple API keys.

## Benefits

1. **Cost Efficiency**: Often cheaper than direct API access
2. **Model Variety**: Access to 100+ models from different providers
3. **Fallback Options**: Automatic failover between models
4. **Usage Analytics**: Built-in tracking and monitoring

## Setup Instructions

### 1. Get OpenRouter API Key

1. Sign up at [OpenRouter.ai](https://openrouter.ai)
2. Generate an API key from your dashboard
3. Add credits to your account

### 2. Configure Environment Variables

Update your `.env` file:

```bash
# OpenRouter Configuration
OPENROUTER_API_KEY=sk-or-v1-your-key-here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Model Selection
# Fast model for quick decisions (cheaper)
OPENROUTER_FAST_MODEL=anthropic/claude-3-haiku-********

# Smart model for complex analysis (more powerful)
OPENROUTER_MODEL=anthropic/claude-3-opus-********
```

### 3. Available Models

#### Recommended for Scraping Tasks:

**Fast Models** (Quick decisions, lower cost):
- `anthropic/claude-3-haiku-********` - $0.25/1M input tokens
- `google/gemini-flash-1.5` - $0.07/1M input tokens
- `meta-llama/llama-3-8b-instruct` - $0.07/1M input tokens

**Smart Models** (Complex analysis, higher quality):
- `anthropic/claude-3-opus-********` - $15/1M input tokens
- `anthropic/claude-3.5-sonnet` - $3/1M input tokens
- `google/gemini-pro-1.5` - $2.50/1M input tokens
- `openai/gpt-4-turbo` - $10/1M input tokens

**Ultra-Low Cost Options**:
- `mistralai/mistral-7b-instruct` - $0.07/1M input tokens
- `nousresearch/nous-hermes-2-mixtral-8x7b-dpo` - $0.18/1M input tokens

### 4. Cost Optimization Tips

1. **Use Tiered Models**: Fast models for routine decisions, smart models only when needed
2. **Implement Caching**: Cache LLM responses to avoid duplicate API calls
3. **Batch Requests**: Group similar requests together
4. **Monitor Usage**: Use OpenRouter dashboard to track spending

### 5. Example Configuration for Different Use Cases

#### Maximum Cost Efficiency
```bash
OPENROUTER_FAST_MODEL=mistralai/mistral-7b-instruct
OPENROUTER_MODEL=meta-llama/llama-3-70b-instruct
```

#### Best Quality/Performance
```bash
OPENROUTER_FAST_MODEL=anthropic/claude-3-haiku-********
OPENROUTER_MODEL=anthropic/claude-3-opus-********
```

#### Balanced Approach
```bash
OPENROUTER_FAST_MODEL=google/gemini-flash-1.5
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet
```

### 6. Testing Your Setup

Run the test command:
```bash
node Commands/test-llm.js
```

This will verify your OpenRouter configuration and show token usage.

## Troubleshooting

1. **Invalid API Key**: Ensure your key starts with `sk-or-v1-`
2. **Rate Limits**: OpenRouter has generous rate limits, but consider upgrading for heavy usage
3. **Model Availability**: Some models may have limited availability during peak times
4. **Credits**: Ensure you have sufficient credits in your OpenRouter account

## Alternative Providers

If you prefer not to use OpenRouter, you can configure direct access:

### OpenAI Direct
```bash
OPENAI_API_KEY=your-openai-key
OPENAI_MODEL=gpt-4
```

### Anthropic Direct (not yet supported)
```bash
ANTHROPIC_API_KEY=your-anthropic-key
ANTHROPIC_MODEL=claude-3-opus-********
```

## Cost Comparison

For the enhanced scraping engine with heavy AI usage:

| Provider | Fast Model | Smart Model | Est. Monthly Cost* |
|----------|------------|-------------|-------------------|
| OpenRouter (Mistral/Llama) | $0.07/1M | $0.50/1M | ~$50-100 |
| OpenRouter (Claude) | $0.25/1M | $15/1M | ~$200-500 |
| OpenAI Direct | $0.50/1M | $30/1M | ~$500-1000 |

*Based on moderate usage: 10M fast tokens, 1M smart tokens per month

## Monitoring Usage

The enhanced scraping engine tracks LLM usage. Check the logs:

```bash
cat logs/llm-usage.log
```

Or use the monitoring command:

```bash
node Commands/monitor-costs.js
```