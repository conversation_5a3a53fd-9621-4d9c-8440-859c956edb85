# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
DEFAULT_MODEL=gpt-4.1
DEFAULT_VENDOR=OpenAI

# Safe Scraping Configuration
SCRAPING_MODE=strict           # Options: strict, moderate, aggressive
MAX_CONCURRENT_REQUESTS=1      # Number of concurrent requests allowed
ENABLE_MONITORING=true         # Enable real-time monitoring

# Proxy Configuration (Optional)
PROXY_ENABLED=false                           # Set to true to enable proxy support
PROXY_URL=http://proxy.example.com:8080      # Proxy server URL
PROXY_USERNAME=your_proxy_username           # Proxy authentication username
PROXY_PASSWORD=your_proxy_password           # Proxy authentication password
PROXY_ROTATE_ENDPOINT=                       # Optional: Endpoint for rotating proxy services

# Example configurations for popular proxy services:

# Bright Data (formerly Luminati)
# PROXY_URL=http://zproxy.lum-superproxy.io:22225
# PROXY_USERNAME=customer-USERNAME-zone-ZONE
# PROXY_PASSWORD=PASSWORD

# Oxylabs
# PROXY_URL=http://pr.oxylabs.io:7777
# PROXY_USERNAME=customer-USERNAME
# PROXY_PASSWORD=PASSWORD

# Smartproxy
# PROXY_URL=http://gate.smartproxy.com:7000
# PROXY_USERNAME=USERNAME
# PROXY_PASSWORD=PASSWORD

# ProxyMesh
# PROXY_URL=http://us-wa.proxymesh.com:31280
# PROXY_USERNAME=USERNAME
# PROXY_PASSWORD=PASSWORD