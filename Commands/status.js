#!/usr/bin/env node

/**
 * Status Command
 * Displays current system status and operational metrics
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync, readFileSync, statSync } from 'fs';
import { SafeScrapingOrchestrator } from '../build/safe-scraping-orchestrator.js';
import { TargetManager } from '../build/target-manager.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('📊 MCP X/Reddit Scraper Status');
console.log('===============================\n');

const args = process.argv.slice(2);
const detailed = args.includes('--detailed') || args.includes('-d');
const json = args.includes('--json');

if (args.includes('--help')) {
  console.log('Usage: node status.js [options]');
  console.log('\nOptions:');
  console.log('  -d, --detailed    Show detailed status information');
  console.log('  --json           Output status as JSON');
  console.log('  --help           Show this help message');
  process.exit(0);
}

const status = {
  timestamp: new Date().toISOString(),
  uptime: process.uptime(),
  system: {
    healthy: true,
    issues: []
  },
  targets: {},
  monitoring: {},
  scraping: {},
  logs: {}
};

// Get system information
function getSystemStatus() {
  console.log('⚙️  System Status');
  console.log('----------------');
  
  // Check core files
  const coreFiles = [
    'package.json',
    'tsconfig.json',
    '.env'
  ];
  
  const missingFiles = coreFiles.filter(file => !existsSync(join(projectRoot, file)));
  
  if (missingFiles.length > 0) {
    status.system.healthy = false;
    status.system.issues.push(`Missing files: ${missingFiles.join(', ')}`);
    console.log(`❌ Missing files: ${missingFiles.join(', ')}`);
  } else {
    console.log('✅ Core configuration files present');
  }
  
  // Check dependencies
  const nodeModulesExists = existsSync(join(projectRoot, 'node_modules'));
  if (!nodeModulesExists) {
    status.system.healthy = false;
    status.system.issues.push('Dependencies not installed');
    console.log('❌ Dependencies not installed');
  } else {
    console.log('✅ Dependencies installed');
  }
  
  // Check build
  const buildExists = existsSync(join(projectRoot, 'build'));
  if (!buildExists) {
    status.system.healthy = false;
    status.system.issues.push('Project not built');
    console.log('❌ Project not built');
  } else {
    console.log('✅ Project built');
  }
  
  status.system.nodeModules = nodeModulesExists;
  status.system.built = buildExists;
}

// Get target statistics
function getTargetStatus() {
  console.log('\n🎯 Target Status');
  console.log('----------------');
  
  try {
    const targetManager = new TargetManager();
    
    // Twitter influencers
    const influencersData = targetManager.getInfluencers();
    const twitterCategories = Object.keys(influencersData);
    const totalTwitter = Object.values(influencersData).flat().length;
    
    // Reddit communities  
    const subredditsData = targetManager.getSubreddits();
    const redditCategories = Object.keys(subredditsData);
    const totalReddit = Object.values(subredditsData).flat().length;
    
    status.targets = {
      twitter: {
        categories: twitterCategories.length,
        total: totalTwitter
      },
      reddit: {
        categories: redditCategories.length,
        total: totalReddit
      }
    };
    
    console.log(`📱 Twitter: ${totalTwitter} influencers across ${twitterCategories.length} categories`);
    console.log(`💬 Reddit: ${totalReddit} communities across ${redditCategories.length} categories`);
    
    if (detailed) {
      console.log('\n   Twitter Categories:');
      twitterCategories.forEach(cat => {
        const count = influencersData[cat].length;
        console.log(`   • ${cat}: ${count} targets`);
      });
      
      console.log('\n   Reddit Categories:');
      redditCategories.forEach(cat => {
        const count = subredditsData[cat].length;
        console.log(`   • ${cat}: ${count} targets`);
      });
    }
    
  } catch (error) {
    status.system.healthy = false;
    status.system.issues.push(`Target manager error: ${error.message}`);
    console.log(`❌ Target manager error: ${error.message}`);
  }
}

// Get monitoring status
function getMonitoringStatus() {
  console.log('\n📊 Monitoring Status');
  console.log('--------------------');
  
  const monitoringFile = join(projectRoot, 'logs', 'scraping-monitor.json');
  
  if (existsSync(monitoringFile)) {
    try {
      const monitoringData = JSON.parse(readFileSync(monitoringFile, 'utf-8'));
      const lastUpdated = new Date(monitoringData.lastUpdated);
      const hoursAgo = Math.round((Date.now() - lastUpdated.getTime()) / (1000 * 60 * 60));
      
      status.monitoring = {
        active: true,
        lastUpdated: monitoringData.lastUpdated,
        hoursAgo,
        logs: monitoringData.logs?.length || 0,
        domains: Object.keys(monitoringData.domainStats || {}).length
      };
      
      console.log(`✅ Monitoring active`);
      console.log(`   Last updated: ${hoursAgo}h ago`);
      console.log(`   Tracked logs: ${status.monitoring.logs}`);
      console.log(`   Monitored domains: ${status.monitoring.domains}`);
      
      if (detailed && monitoringData.domainStats) {
        console.log('\n   Domain Statistics:');
        Object.entries(monitoringData.domainStats).forEach(([domain, stats]) => {
          console.log(`   • ${domain}: ${stats.requests || 0} requests, ${stats.successes || 0} successful`);
        });
      }
      
    } catch (error) {
      console.log(`⚠️  Monitoring file corrupted: ${error.message}`);
      status.monitoring.active = false;
      status.monitoring.error = error.message;
    }
  } else {
    console.log('⚠️  No monitoring data found');
    status.monitoring.active = false;
  }
}

// Get scraping system status
async function getScrapingStatus() {
  console.log('\n🛡️  Safe Scraping Status');
  console.log('------------------------');
  
  try {
    const orchestrator = new SafeScrapingOrchestrator('strict');
    const systemStatus = await orchestrator.getSystemStatus();
    
    status.scraping = {
      safetyMode: systemStatus.safetyMode,
      activeSessions: systemStatus.browserSessions?.activeSessions || 0,
      cacheEntries: systemStatus.cacheStats?.entries || 0,
      circuitBreakers: Object.keys(systemStatus.circuitBreakerStates || {}).length
    };
    
    console.log(`✅ Safety mode: ${systemStatus.safetyMode.toUpperCase()}`);
    console.log(`   Active browser sessions: ${status.scraping.activeSessions}`);
    console.log(`   Cache entries: ${status.scraping.cacheEntries}`);
    console.log(`   Circuit breakers: ${status.scraping.circuitBreakers}`);
    
    if (detailed && systemStatus.circuitBreakerStates) {
      console.log('\n   Circuit Breaker States:');
      Object.entries(systemStatus.circuitBreakerStates).forEach(([domain, state]) => {
        console.log(`   • ${domain}: ${state.state} (${state.stats?.successRate?.toFixed(1) || 'N/A'}% success)`);
      });
    }
    
    if (detailed && systemStatus.requestStats) {
      console.log('\n   Request Statistics:');
      console.log(`   • Success rate: ${systemStatus.requestStats.successRate?.toFixed(1) || 'N/A'}%`);
      console.log(`   • Blocked domains: ${systemStatus.requestStats.blockedDomains?.length || 0}`);
    }
    
    await orchestrator.cleanup();
    
  } catch (error) {
    console.log(`❌ Scraping system error: ${error.message}`);
    status.scraping.error = error.message;
    status.system.healthy = false;
    status.system.issues.push(`Scraping system error: ${error.message}`);
  }
}

// Get log file status
function getLogStatus() {
  console.log('\n📝 Log Status');
  console.log('-------------');
  
  const logsDir = join(projectRoot, 'logs');
  
  if (!existsSync(logsDir)) {
    console.log('❌ Logs directory not found');
    status.logs.directory = false;
    return;
  }
  
  const logFiles = ['scraping-monitor.json', 'live-monitoring.log', 'safe-scraping-test.log'];
  const presentLogs = [];
  const logSizes = {};
  
  logFiles.forEach(logFile => {
    const logPath = join(logsDir, logFile);
    if (existsSync(logPath)) {
      const stats = statSync(logPath);
      presentLogs.push(logFile);
      logSizes[logFile] = stats.size;
    }
  });
  
  status.logs = {
    directory: true,
    files: presentLogs.length,
    sizes: logSizes
  };
  
  console.log(`✅ Logs directory exists`);
  console.log(`   Log files found: ${presentLogs.length}/${logFiles.length}`);
  
  if (detailed) {
    presentLogs.forEach(logFile => {
      const size = (logSizes[logFile] / 1024).toFixed(1);
      console.log(`   • ${logFile}: ${size} KB`);
    });
  }
}

// Show overall health
function showOverallHealth() {
  console.log('\n🏥 Overall Health');
  console.log('-----------------');
  
  if (status.system.healthy) {
    console.log('✅ System is healthy');
  } else {
    console.log('❌ System has issues:');
    status.system.issues.forEach(issue => {
      console.log(`   • ${issue}`);
    });
  }
  
  // Calculate health score
  let healthScore = 100;
  healthScore -= status.system.issues.length * 20;
  
  if (!status.monitoring?.active) healthScore -= 10;
  if (!status.logs?.directory) healthScore -= 15;
  
  status.healthScore = Math.max(0, healthScore);
  
  console.log(`\n🎯 Health Score: ${status.healthScore}%`);
  
  if (status.healthScore === 100) {
    console.log('🎉 Perfect health!');
  } else if (status.healthScore >= 80) {
    console.log('👍 Good health');
  } else if (status.healthScore >= 60) {
    console.log('⚠️  Needs attention');
  } else {
    console.log('🚨 Requires immediate action');
  }
}

// Main execution
async function runStatus() {
  if (json) {
    // JSON output mode - just collect data silently
    try {
      const targetManager = new TargetManager();
      const influencersData = targetManager.getInfluencers();
      const subredditsData = targetManager.getSubreddits();
      
      status.targets.twitter = {
        categories: Object.keys(influencersData).length,
        total: Object.values(influencersData).flat().length
      };
      status.targets.reddit = {
        categories: Object.keys(subredditsData).length,
        total: Object.values(subredditsData).flat().length
      };
      
      // Add monitoring data
      const monitoringFile = join(projectRoot, 'logs', 'scraping-monitor.json');
      if (existsSync(monitoringFile)) {
        const monitoringData = JSON.parse(readFileSync(monitoringFile, 'utf-8'));
        status.monitoring = {
          active: true,
          lastUpdated: monitoringData.lastUpdated,
          logs: monitoringData.logs?.length || 0
        };
      }
      
      // Calculate health
      status.system.healthy = existsSync(join(projectRoot, 'package.json')) &&
                             existsSync(join(projectRoot, 'node_modules')) &&
                             existsSync(join(projectRoot, 'build'));
      
      console.log(JSON.stringify(status, null, 2));
    } catch (error) {
      console.error(JSON.stringify({ error: error.message }, null, 2));
      process.exit(1);
    }
  } else {
    // Standard output mode
    getSystemStatus();
    getTargetStatus();
    getMonitoringStatus();
    await getScrapingStatus();
    getLogStatus();
    showOverallHealth();
    
    console.log('\n✅ Status check completed');
  }
}

// Run the status check
runStatus().catch(error => {
  if (json) {
    console.error(JSON.stringify({ error: error.message }, null, 2));
  } else {
    console.error('❌ Status check failed:', error.message);
  }
  process.exit(1);
});