#!/usr/bin/env node

/**
 * GitHub Manager
 * Integrates with GitHub API and CLI for project management
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { Octokit } from '@octokit/rest';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🐙 GitHub Manager');
console.log('=================\n');

const args = process.argv.slice(2);
const command = args[0];

if (!command || command === 'help') {
  console.log('Usage: node github-manager.js <command> [options]\n');
  console.log('Commands:');
  console.log('  status        Show repository status and info');
  console.log('  issues        List open issues');
  console.log('  prs           List open pull requests');
  console.log('  releases      List recent releases');
  console.log('  create-issue  Create a new issue');
  console.log('  create-pr     Create a pull request');
  console.log('  workflow      Show GitHub Actions workflow status');
  console.log('  setup         Setup GitHub integration');
  console.log('\nExamples:');
  console.log('  node github-manager.js status');
  console.log('  node github-manager.js issues --limit 5');
  console.log('  node github-manager.js create-issue "Bug report"');
  process.exit(0);
}

// Get GitHub token from environment or gh CLI
function getGitHubToken() {
  // Try environment variable first
  if (process.env.GITHUB_TOKEN) {
    return process.env.GITHUB_TOKEN;
  }
  
  // Try gh CLI auth status
  try {
    const authStatus = execSync('gh auth status', { encoding: 'utf-8' });
    if (authStatus.includes('Logged in')) {
      // Get token from gh CLI
      const token = execSync('gh auth token', { encoding: 'utf-8' }).trim();
      return token;
    }
  } catch (error) {
    console.log('⚠️  GitHub CLI not authenticated. Run: gh auth login');
  }
  
  return null;
}

// Get repository information
function getRepoInfo() {
  try {
    const remoteUrl = execSync('git config --get remote.origin.url', { encoding: 'utf-8' }).trim();
    const match = remoteUrl.match(/github\.com[:/]([^/]+)\/([^/.]+)/);
    
    if (match) {
      return {
        owner: match[1],
        repo: match[2]
      };
    }
  } catch (error) {
    // Fallback to package.json
    const packageJson = JSON.parse(readFileSync(join(projectRoot, 'package.json'), 'utf-8'));
    if (packageJson.repository && packageJson.repository.url) {
      const match = packageJson.repository.url.match(/github\.com[:/]([^/]+)\/([^/.]+)/);
      if (match) {
        return {
          owner: match[1],
          repo: match[2]
        };
      }
    }
  }
  
  return null;
}

// Initialize Octokit
function getOctokit() {
  const token = getGitHubToken();
  if (!token) {
    console.log('❌ GitHub token not found. Please set GITHUB_TOKEN or run: gh auth login');
    process.exit(1);
  }
  
  return new Octokit({ auth: token });
}

// Show repository status
async function showStatus() {
  const repoInfo = getRepoInfo();
  if (!repoInfo) {
    console.log('❌ Could not determine GitHub repository');
    return;
  }
  
  const octokit = getOctokit();
  
  try {
    console.log('📊 Repository Status');
    console.log('-------------------');
    
    const { data: repo } = await octokit.rest.repos.get({
      owner: repoInfo.owner,
      repo: repoInfo.repo
    });
    
    console.log(`Repository: ${repo.full_name}`);
    console.log(`Description: ${repo.description || 'No description'}`);
    console.log(`Language: ${repo.language || 'Multiple'}`);
    console.log(`Stars: ⭐ ${repo.stargazers_count}`);
    console.log(`Forks: 🍴 ${repo.forks_count}`);
    console.log(`Open Issues: 🐛 ${repo.open_issues_count}`);
    console.log(`Default Branch: ${repo.default_branch}`);
    console.log(`Last Updated: ${new Date(repo.updated_at).toLocaleDateString()}`);
    
    if (repo.private) {
      console.log('🔒 Private repository');
    } else {
      console.log('🌍 Public repository');
    }
    
    // Check if Actions are enabled
    try {
      const { data: workflows } = await octokit.rest.actions.listRepoWorkflows({
        owner: repoInfo.owner,
        repo: repoInfo.repo
      });
      
      if (workflows.total_count > 0) {
        console.log(`\n🔄 GitHub Actions: ${workflows.total_count} workflows`);
      }
    } catch {
      // Actions might not be enabled
    }
    
  } catch (error) {
    console.log(`❌ Error fetching repository info: ${error.message}`);
  }
}

// List issues
async function listIssues() {
  const repoInfo = getRepoInfo();
  if (!repoInfo) return;
  
  const octokit = getOctokit();
  const limit = parseInt(args.find(arg => arg.startsWith('--limit='))?.split('=')[1] || '10');
  
  try {
    console.log('🐛 Open Issues');
    console.log('--------------');
    
    const { data: issues } = await octokit.rest.issues.listForRepo({
      owner: repoInfo.owner,
      repo: repoInfo.repo,
      state: 'open',
      per_page: limit
    });
    
    if (issues.length === 0) {
      console.log('✅ No open issues');
      return;
    }
    
    issues.forEach((issue, index) => {
      if (issue.pull_request) return; // Skip PRs
      
      console.log(`\n${index + 1}. #${issue.number} ${issue.title}`);
      console.log(`   Created: ${new Date(issue.created_at).toLocaleDateString()}`);
      console.log(`   Author: @${issue.user.login}`);
      
      if (issue.labels.length > 0) {
        const labels = issue.labels.map(label => label.name).join(', ');
        console.log(`   Labels: ${labels}`);
      }
    });
    
  } catch (error) {
    console.log(`❌ Error fetching issues: ${error.message}`);
  }
}

// List pull requests
async function listPRs() {
  const repoInfo = getRepoInfo();
  if (!repoInfo) return;
  
  const octokit = getOctokit();
  const limit = parseInt(args.find(arg => arg.startsWith('--limit='))?.split('=')[1] || '10');
  
  try {
    console.log('🔄 Open Pull Requests');
    console.log('---------------------');
    
    const { data: prs } = await octokit.rest.pulls.list({
      owner: repoInfo.owner,
      repo: repoInfo.repo,
      state: 'open',
      per_page: limit
    });
    
    if (prs.length === 0) {
      console.log('✅ No open pull requests');
      return;
    }
    
    prs.forEach((pr, index) => {
      console.log(`\n${index + 1}. #${pr.number} ${pr.title}`);
      console.log(`   Branch: ${pr.head.ref} → ${pr.base.ref}`);
      console.log(`   Author: @${pr.user.login}`);
      console.log(`   Created: ${new Date(pr.created_at).toLocaleDateString()}`);
      
      if (pr.draft) {
        console.log('   📝 Draft');
      }
      
      if (pr.mergeable_state) {
        console.log(`   Status: ${pr.mergeable_state}`);
      }
    });
    
  } catch (error) {
    console.log(`❌ Error fetching pull requests: ${error.message}`);
  }
}

// Show workflow status
async function showWorkflowStatus() {
  const repoInfo = getRepoInfo();
  if (!repoInfo) return;
  
  const octokit = getOctokit();
  
  try {
    console.log('🔄 GitHub Actions Workflows');
    console.log('----------------------------');
    
    const { data: workflows } = await octokit.rest.actions.listRepoWorkflows({
      owner: repoInfo.owner,
      repo: repoInfo.repo
    });
    
    if (workflows.total_count === 0) {
      console.log('No workflows found');
      return;
    }
    
    for (const workflow of workflows.workflows) {
      console.log(`\n📋 ${workflow.name}`);
      console.log(`   State: ${workflow.state}`);
      console.log(`   File: ${workflow.path}`);
      
      // Get recent runs
      const { data: runs } = await octokit.rest.actions.listWorkflowRuns({
        owner: repoInfo.owner,
        repo: repoInfo.repo,
        workflow_id: workflow.id,
        per_page: 3
      });
      
      if (runs.workflow_runs.length > 0) {
        console.log('   Recent runs:');
        runs.workflow_runs.forEach(run => {
          const status = run.status === 'completed' ? 
            (run.conclusion === 'success' ? '✅' : '❌') : '🔄';
          console.log(`     ${status} ${run.head_branch} - ${new Date(run.created_at).toLocaleDateString()}`);
        });
      }
    }
    
  } catch (error) {
    console.log(`❌ Error fetching workflows: ${error.message}`);
  }
}

// Create issue using gh CLI (easier than API for input)
function createIssue() {
  const title = args[1];
  if (!title) {
    console.log('❌ Please provide an issue title');
    console.log('Usage: node github-manager.js create-issue "Issue title"');
    return;
  }
  
  try {
    console.log('🐛 Creating GitHub issue...');
    const result = execSync(`gh issue create --title "${title}" --body "Created via GitHub Manager"`, 
      { encoding: 'utf-8' });
    console.log('✅ Issue created:', result.trim());
  } catch (error) {
    console.log('❌ Failed to create issue:', error.message);
  }
}

// Setup GitHub integration
function setupGitHubIntegration() {
  console.log('🔧 Setting up GitHub integration...');
  
  // Check if gh CLI is authenticated
  try {
    execSync('gh auth status', { stdio: 'pipe' });
    console.log('✅ GitHub CLI authenticated');
  } catch {
    console.log('⚠️  GitHub CLI not authenticated');
    console.log('Run: gh auth login');
  }
  
  // Check for GitHub token
  if (process.env.GITHUB_TOKEN) {
    console.log('✅ GITHUB_TOKEN environment variable set');
  } else {
    console.log('⚠️  GITHUB_TOKEN not set');
    console.log('Consider setting GITHUB_TOKEN in your .env file');
  }
  
  // Check repository configuration
  const repoInfo = getRepoInfo();
  if (repoInfo) {
    console.log(`✅ Repository detected: ${repoInfo.owner}/${repoInfo.repo}`);
  } else {
    console.log('⚠️  Could not detect GitHub repository');
  }
  
  console.log('\n💡 GitHub integration features available:');
  console.log('• Repository status and statistics');
  console.log('• Issue and PR management');
  console.log('• GitHub Actions workflow monitoring');
  console.log('• Automated issue creation');
}

// Execute commands
switch (command) {
  case 'status':
    await showStatus();
    break;
  case 'issues':
    await listIssues();
    break;
  case 'prs':
    await listPRs();
    break;
  case 'workflow':
    await showWorkflowStatus();
    break;
  case 'create-issue':
    createIssue();
    break;
  case 'setup':
    setupGitHubIntegration();
    break;
  default:
    console.log(`❌ Unknown command: ${command}`);
    console.log('Run "node github-manager.js help" for usage information');
    process.exit(1);
}