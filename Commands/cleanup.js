#!/usr/bin/env node

/**
 * Cleanup Command
 * Cleans up logs, temporary files, and old data
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readdirSync, statSync, unlinkSync, readFileSync, writeFileSync, existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🧹 MCP X/Reddit Scraper Cleanup');
console.log('================================\n');

// Parse command line arguments
const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run');
const force = args.includes('--force');
const days = parseInt(args.find(arg => arg.startsWith('--days='))?.split('=')[1] || '7');

if (args.includes('--help')) {
  console.log('Usage: node cleanup.js [options]');
  console.log('\nOptions:');
  console.log('  --dry-run     Show what would be deleted without actually deleting');
  console.log('  --force       Delete all files without age check');
  console.log('  --days=N      Delete files older than N days (default: 7)');
  console.log('  --help        Show this help message');
  process.exit(0);
}

console.log(`Mode: ${dryRun ? 'DRY RUN' : 'CLEANUP'}`);
console.log(`Age threshold: ${force ? 'ALL FILES' : `${days} days`}`);
console.log('');

let totalSize = 0;
let filesDeleted = 0;
let filesKept = 0;

// Helper to format file size
function formatSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Helper to check if file is old enough to delete
function shouldDelete(filePath) {
  if (force) return true;
  
  try {
    const stats = statSync(filePath);
    const ageInDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
    return ageInDays > days;
  } catch {
    return false;
  }
}

// Clean logs directory
function cleanLogs() {
  console.log('📝 Cleaning logs directory...');
  const logsDir = join(projectRoot, 'logs');
  
  if (!existsSync(logsDir)) {
    console.log('   ⚠️  Logs directory not found');
    return;
  }
  
  const files = readdirSync(logsDir);
  const logFiles = files.filter(file => 
    file.endsWith('.log') || 
    file.endsWith('.json') && file !== 'scraping-monitor.json'
  );
  
  logFiles.forEach(file => {
    const filePath = join(logsDir, file);
    const stats = statSync(filePath);
    
    if (shouldDelete(filePath)) {
      if (!dryRun) {
        unlinkSync(filePath);
      }
      totalSize += stats.size;
      filesDeleted++;
      console.log(`   ${dryRun ? 'Would delete' : 'Deleted'}: ${file} (${formatSize(stats.size)})`);
    } else {
      filesKept++;
    }
  });
  
  if (filesDeleted === 0 && filesKept > 0) {
    console.log(`   ✅ ${filesKept} log files kept (newer than ${days} days)`);
  }
}

// Clean test files
function cleanTestFiles() {
  console.log('\n🧪 Cleaning test files...');
  const testFiles = readdirSync(projectRoot).filter(file => 
    file.startsWith('test-') && file.endsWith('.js')
  );
  
  testFiles.forEach(file => {
    const filePath = join(projectRoot, file);
    const stats = statSync(filePath);
    
    if (shouldDelete(filePath)) {
      if (!dryRun) {
        unlinkSync(filePath);
      }
      totalSize += stats.size;
      filesDeleted++;
      console.log(`   ${dryRun ? 'Would delete' : 'Deleted'}: ${file} (${formatSize(stats.size)})`);
    } else {
      filesKept++;
    }
  });
  
  if (testFiles.length === 0) {
    console.log('   ✅ No test files to clean');
  }
}

// Clean monitoring data
function cleanMonitoring() {
  console.log('\n📊 Cleaning monitoring data...');
  const monitoringFile = join(projectRoot, 'scraping-monitor.json');
  
  if (existsSync(monitoringFile) && !dryRun && force) {
    const stats = statSync(monitoringFile);
    totalSize += stats.size;
    
    // Reset monitoring data instead of deleting
    const emptyMonitoring = {
      logs: [],
      domainStats: {},
      lastUpdated: new Date().toISOString()
    };
    
    writeFileSync(monitoringFile, JSON.stringify(emptyMonitoring, null, 2));
    console.log('   ✅ Reset monitoring data');
  } else if (existsSync(monitoringFile)) {
    console.log('   ℹ️  Monitoring data preserved (use --force to reset)');
  }
}

// Clean screenshots
function cleanScreenshots() {
  console.log('\n📸 Cleaning screenshots...');
  const screenshotsDir = join(projectRoot, 'screenshots');
  
  if (!existsSync(screenshotsDir)) {
    console.log('   ⚠️  Screenshots directory not found');
    return;
  }
  
  const files = readdirSync(screenshotsDir);
  const imageFiles = files.filter(file => 
    file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.jpeg')
  );
  
  imageFiles.forEach(file => {
    const filePath = join(screenshotsDir, file);
    const stats = statSync(filePath);
    
    if (shouldDelete(filePath)) {
      if (!dryRun) {
        unlinkSync(filePath);
      }
      totalSize += stats.size;
      filesDeleted++;
      console.log(`   ${dryRun ? 'Would delete' : 'Deleted'}: ${file} (${formatSize(stats.size)})`);
    } else {
      filesKept++;
    }
  });
  
  if (imageFiles.length === 0) {
    console.log('   ✅ No screenshots to clean');
  }
}

// Clean build artifacts
function cleanBuildArtifacts() {
  console.log('\n🔨 Cleaning build artifacts...');
  const buildDir = join(projectRoot, 'build');
  
  if (existsSync(buildDir) && force && !dryRun) {
    // Don't actually delete build directory, just notify
    console.log('   ℹ️  Build directory preserved (run npm run build to rebuild)');
  } else {
    console.log('   ✅ Build artifacts preserved');
  }
}

// Show summary
function showSummary() {
  console.log('\n📋 CLEANUP SUMMARY');
  console.log('==================');
  console.log(`Files ${dryRun ? 'would be deleted' : 'deleted'}: ${filesDeleted}`);
  console.log(`Files kept: ${filesKept}`);
  console.log(`Total space ${dryRun ? 'would be freed' : 'freed'}: ${formatSize(totalSize)}`);
  
  if (dryRun) {
    console.log('\n💡 This was a dry run. Use without --dry-run to actually delete files.');
  }
}

// Run cleanup
try {
  cleanLogs();
  cleanTestFiles();
  cleanMonitoring();
  cleanScreenshots();
  cleanBuildArtifacts();
  showSummary();
  
  console.log('\n✅ Cleanup completed');
} catch (error) {
  console.error('❌ Cleanup failed:', error.message);
  process.exit(1);
}