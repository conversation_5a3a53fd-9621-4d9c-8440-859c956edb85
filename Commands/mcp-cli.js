#!/usr/bin/env node

/**
 * MCP CLI - Master command interface for the MCP X/Reddit Scraper
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { spawn } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const commands = {
  'health': {
    file: 'health-check.js',
    description: 'Check system health and dependencies',
    usage: 'mcp-cli health'
  },
  'status': {
    file: 'status.js', 
    description: 'Show current system status and metrics',
    usage: 'mcp-cli status [--detailed] [--json]'
  },
  'scrape': {
    file: 'quick-scrape.js',
    description: 'Perform a quick test scrape',
    usage: 'mcp-cli scrape <twitter|reddit> <username|subreddit> [safety-mode]'
  },
  'analyze': {
    file: 'analyze-targets.js',
    description: 'Analyze current targets and provide insights',
    usage: 'mcp-cli analyze'
  },
  'cleanup': {
    file: 'cleanup.js',
    description: 'Clean up logs and temporary files',
    usage: 'mcp-cli cleanup [--dry-run] [--force] [--days=N]'
  },
  'github': {
    file: 'github-manager.js',
    description: 'GitHub repository management',
    usage: 'mcp-cli github <status|issues|prs|workflow|setup>'
  }
};

const args = process.argv.slice(2);
const command = args[0];

if (!command || command === 'help' || command === '--help') {
  console.log('🚀 MCP X/Reddit Scraper CLI');
  console.log('===========================\n');
  
  console.log('Usage: mcp-cli <command> [options]\n');
  
  console.log('Available Commands:');
  Object.entries(commands).forEach(([name, info]) => {
    console.log(`  ${name.padEnd(10)} ${info.description}`);
  });
  
  console.log('\nExamples:');
  console.log('  mcp-cli health                    # Check system health');
  console.log('  mcp-cli status --detailed         # Detailed status report');
  console.log('  mcp-cli scrape twitter elonmusk   # Test scrape Twitter');
  console.log('  mcp-cli analyze                   # Analyze targets');
  console.log('  mcp-cli cleanup --dry-run         # Preview cleanup');
  
  console.log('\nFor command-specific help:');
  console.log('  node Commands/<command>.js --help');
  
  process.exit(0);
}

if (!commands[command]) {
  console.error(`❌ Unknown command: ${command}`);
  console.error(`Available commands: ${Object.keys(commands).join(', ')}`);
  process.exit(1);
}

// Execute the command
const commandFile = join(__dirname, commands[command].file);
const commandArgs = args.slice(1);

const proc = spawn('node', [commandFile, ...commandArgs], {
  stdio: 'inherit',
  cwd: dirname(__dirname)
});

proc.on('close', (code) => {
  process.exit(code);
});

proc.on('error', (error) => {
  console.error(`❌ Failed to execute command: ${error.message}`);
  process.exit(1);
});