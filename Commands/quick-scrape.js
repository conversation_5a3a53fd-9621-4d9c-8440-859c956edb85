#!/usr/bin/env node

/**
 * Quick Scrape Command
 * Performs a quick test scrape of a single target with safe settings
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { SafeScrapingOrchestrator } from '../build/safe-scraping-orchestrator.js';
import { TargetManager } from '../build/target-manager.js';
import { createWriteStream } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// Parse command line arguments
const args = process.argv.slice(2);
const targetType = args[0] || 'twitter';
const targetName = args[1];

// Handle flags
const isTestLLM = args.includes('--test-llm');
const isRandom = args.includes('--random');

// Extract safety mode (exclude flags)
const nonFlagArgs = args.filter(arg => !arg.startsWith('--'));
const safetyMode = nonFlagArgs[2] || 'strict';

console.log('🚀 Quick Scrape Test');
console.log('===================\n');

if (!targetName && !isRandom) {
  console.log('Usage: node quick-scrape.js <twitter|reddit> <username|subreddit> [safety-mode]');
  console.log('       node quick-scrape.js --random');
  console.log('\nExamples:');
  console.log('  node quick-scrape.js twitter elonmusk');
  console.log('  node quick-scrape.js reddit MachineLearning moderate');
  console.log('  node quick-scrape.js --random');
  process.exit(1);
}

const logStream = createWriteStream(join(projectRoot, 'logs', 'quick-scrape.log'), { flags: 'a' });

function log(message) {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}\n`;
  logStream.write(logEntry);
  console.log(message);
}

async function quickScrape() {
  const orchestrator = new SafeScrapingOrchestrator(safetyMode);
  const targetManager = new TargetManager();
  
  if (isTestLLM) {
    log('🧠 LLM Test Mode: Testing OpenRouter integration...');
  }
  
  try {
    let target;
    let domain;
    
    if (isRandom) {
      // Pick a random target
      const allTwitter = Object.values(targetManager.getInfluencers()).flat();
      const allReddit = Object.values(targetManager.getSubreddits()).flat();
      const allTargets = [...allTwitter.map(t => ({...t, type: 'twitter'})), 
                         ...allReddit.map(t => ({...t, type: 'reddit'}))];
      
      target = allTargets[Math.floor(Math.random() * allTargets.length)];
      domain = target.type === 'twitter' ? 'x.com' : 'reddit.com';
      
      log(`🎲 Randomly selected: ${target.type === 'twitter' ? '@' + target.username : 'r/' + target.name}`);
    } else {
      // Use specified target
      if (targetType === 'twitter') {
        target = { username: targetName.replace('@', '') };
        domain = 'x.com';
      } else {
        target = { name: targetName.replace('r/', '') };
        domain = 'reddit.com';
      }
    }
    
    log(`🔍 Target: ${targetType === 'twitter' ? '@' + target.username : 'r/' + target.name}`);
    log(`🛡️  Safety Mode: ${safetyMode.toUpperCase()}`);
    log(`🌐 Domain: ${domain}`);
    
    // Show system status
    const status = await orchestrator.getSystemStatus();
    log(`\n📊 System Status:`);
    log(`   Safety Mode: ${status.safetyMode}`);
    log(`   Cache Entries: ${status.cacheStats.entries}`);
    log(`   Active Sessions: ${status.browserSessions.activeSessions}`);
    
    // Perform test connection
    log(`\n🔗 Testing connection...`);
    const startTime = Date.now();
    
    const result = await orchestrator.testConnection(
      domain,
      targetType === 'twitter' ? target.username : target.name
    );
    
    const elapsed = Date.now() - startTime;
    
    if (result.success) {
      log(`✅ SUCCESS: ${result.data}`);
      log(`⏱️  Response Time: ${result.stats.responseTime}ms`);
      log(`🔄 Attempts Used: ${result.stats.attemptsUsed}`);
      log(`🔌 Circuit Breaker: ${result.stats.circuitBreakerState}`);
    } else {
      log(`❌ FAILED: ${result.error}`);
      log(`⏱️  Total Time: ${elapsed}ms`);
      
      if (result.cached) {
        log(`💾 Using cached data`);
      }
      if (result.fromFallback) {
        log(`🔄 Using fallback mechanism`);
      }
    }
    
    // Show final status
    const finalStatus = await orchestrator.getSystemStatus();
    
    if (finalStatus.circuitBreakerStates) {
      log(`\n🔌 Circuit Breaker States:`);
      Object.entries(finalStatus.circuitBreakerStates).forEach(([domain, state]) => {
        log(`   ${domain}: ${state.state} (${state.stats.successRate.toFixed(1)}% success)`);
      });
    }
    
    // Show monitoring stats
    if (finalStatus.requestStats) {
      log(`\n📈 Request Statistics:`);
      log(`   Success Rate: ${finalStatus.requestStats.successRate?.toFixed(1) || 'N/A'}%`);
      log(`   Blocked Domains: ${finalStatus.requestStats.blockedDomains?.length || 0}`);
      
      if (finalStatus.requestStats.recentErrors?.length > 0) {
        log(`   Recent Errors: ${finalStatus.requestStats.recentErrors.slice(0, 3).join(', ')}`);
      }
    }
    
    await orchestrator.cleanup();
    log(`\n✅ Quick scrape test completed`);
    
  } catch (error) {
    log(`\n❌ Error: ${error.message}`);
    await orchestrator.cleanup();
    process.exit(1);
  } finally {
    logStream.end();
  }
}

// Run the quick scrape
quickScrape().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});