#!/usr/bin/env node

/**
 * Analyze Targets Command
 * Analyzes current targets and provides insights on quality and coverage
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { TargetManager } from '../build/target-manager.js';
import { readFileSync, writeFileSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🎯 Target Analysis Report');
console.log('========================\n');

const targetManager = new TargetManager();

// Analyze Twitter influencers
function analyzeTwitterInfluencers() {
  console.log('📱 Twitter Influencers Analysis');
  console.log('-------------------------------');
  
  const influencersData = targetManager.getInfluencers();
  const categories = Object.keys(influencersData);
  let totalInfluencers = 0;
  let totalRelevanceScore = 0;
  const categoryStats = {};
  
  categories.forEach(category => {
    const influencers = influencersData[category];
    const count = influencers.length;
    totalInfluencers += count;
    
    const avgRelevance = influencers.reduce((sum, inf) => sum + inf.relevance_score, 0) / count;
    totalRelevanceScore += influencers.reduce((sum, inf) => sum + inf.relevance_score, 0);
    
    categoryStats[category] = {
      count,
      avgRelevance,
      topInfluencer: influencers.sort((a, b) => b.relevance_score - a.relevance_score)[0]
    };
    
    console.log(`\n📂 ${category.toUpperCase()}`);
    console.log(`   Count: ${count}`);
    console.log(`   Avg Relevance: ${avgRelevance.toFixed(2)}`);
    console.log(`   Top: @${categoryStats[category].topInfluencer.username} (${categoryStats[category].topInfluencer.followers})`);
  });
  
  console.log(`\n📊 Twitter Summary:`);
  console.log(`   Total Categories: ${categories.length}`);
  console.log(`   Total Influencers: ${totalInfluencers}`);
  console.log(`   Overall Avg Relevance: ${(totalRelevanceScore / totalInfluencers).toFixed(2)}`);
  
  return { totalInfluencers, categoryStats };
}

// Analyze Reddit communities
function analyzeRedditCommunities() {
  console.log('\n\n💬 Reddit Communities Analysis');
  console.log('------------------------------');
  
  const subredditsData = targetManager.getSubreddits();
  const categories = Object.keys(subredditsData);
  let totalSubreddits = 0;
  let totalActivityScore = 0;
  const categoryStats = {};
  
  categories.forEach(category => {
    const subreddits = subredditsData[category];
    const count = subreddits.length;
    totalSubreddits += count;
    
    const avgActivity = subreddits.reduce((sum, sub) => sum + sub.activity_score, 0) / count;
    totalActivityScore += subreddits.reduce((sum, sub) => sum + sub.activity_score, 0);
    
    categoryStats[category] = {
      count,
      avgActivity,
      topSubreddit: subreddits.sort((a, b) => b.activity_score - a.activity_score)[0]
    };
    
    console.log(`\n📂 ${category.toUpperCase()}`);
    console.log(`   Count: ${count}`);
    console.log(`   Avg Activity: ${avgActivity.toFixed(2)}`);
    console.log(`   Top: r/${categoryStats[category].topSubreddit.name} (${categoryStats[category].topSubreddit.subscribers})`);
  });
  
  console.log(`\n📊 Reddit Summary:`);
  console.log(`   Total Categories: ${categories.length}`);
  console.log(`   Total Subreddits: ${totalSubreddits}`);
  console.log(`   Overall Avg Activity: ${(totalActivityScore / totalSubreddits).toFixed(2)}`);
  
  return { totalSubreddits, categoryStats };
}

// Find targets needing update
function findOutdatedTargets() {
  console.log('\n\n⏰ Targets Needing Update');
  console.log('-------------------------');
  
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const outdatedInfluencers = [];
  const outdatedSubreddits = [];
  
  // Check Twitter influencers
  const influencersData = targetManager.getInfluencers();
  Object.entries(influencersData).forEach(([category, influencers]) => {
    influencers.forEach(inf => {
      const lastVerified = new Date(inf.last_verified);
      if (lastVerified < thirtyDaysAgo) {
        outdatedInfluencers.push({ ...inf, category });
      }
    });
  });
  
  // Check Reddit communities
  const subredditsData = targetManager.getSubreddits();
  Object.entries(subredditsData).forEach(([category, subreddits]) => {
    subreddits.forEach(sub => {
      const lastVerified = new Date(sub.last_verified);
      if (lastVerified < thirtyDaysAgo) {
        outdatedSubreddits.push({ ...sub, category });
      }
    });
  });
  
  console.log(`\n🐦 Outdated Twitter Influencers: ${outdatedInfluencers.length}`);
  if (outdatedInfluencers.length > 0) {
    outdatedInfluencers.slice(0, 5).forEach(inf => {
      console.log(`   @${inf.username} (${inf.category}) - Last verified: ${inf.last_verified}`);
    });
    if (outdatedInfluencers.length > 5) {
      console.log(`   ... and ${outdatedInfluencers.length - 5} more`);
    }
  }
  
  console.log(`\n🗨️  Outdated Reddit Communities: ${outdatedSubreddits.length}`);
  if (outdatedSubreddits.length > 0) {
    outdatedSubreddits.slice(0, 5).forEach(sub => {
      console.log(`   r/${sub.name} (${sub.category}) - Last verified: ${sub.last_verified}`);
    });
    if (outdatedSubreddits.length > 5) {
      console.log(`   ... and ${outdatedSubreddits.length - 5} more`);
    }
  }
  
  return { outdatedInfluencers, outdatedSubreddits };
}

// Generate recommendations
function generateRecommendations(twitterStats, redditStats, outdatedData) {
  console.log('\n\n💡 Recommendations');
  console.log('------------------');
  
  const recommendations = [];
  
  // Check for low coverage categories
  Object.entries(twitterStats.categoryStats).forEach(([category, stats]) => {
    if (stats.count < 5) {
      recommendations.push(`Add more Twitter influencers to '${category}' (currently ${stats.count})`);
    }
  });
  
  Object.entries(redditStats.categoryStats).forEach(([category, stats]) => {
    if (stats.count < 3) {
      recommendations.push(`Add more Reddit communities to '${category}' (currently ${stats.count})`);
    }
  });
  
  // Check for outdated targets
  if (outdatedData.outdatedInfluencers.length > 10) {
    recommendations.push(`Update ${outdatedData.outdatedInfluencers.length} outdated Twitter influencers`);
  }
  
  if (outdatedData.outdatedSubreddits.length > 5) {
    recommendations.push(`Update ${outdatedData.outdatedSubreddits.length} outdated Reddit communities`);
  }
  
  // Check for low quality scores
  Object.entries(twitterStats.categoryStats).forEach(([category, stats]) => {
    if (stats.avgRelevance < 0.7) {
      recommendations.push(`Review quality of '${category}' Twitter targets (avg relevance: ${stats.avgRelevance.toFixed(2)})`);
    }
  });
  
  if (recommendations.length === 0) {
    console.log('✅ Target configuration looks healthy!');
  } else {
    recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
  }
  
  return recommendations;
}

// Export analysis report
function exportAnalysisReport(twitterStats, redditStats, outdatedData, recommendations) {
  const report = {
    generated: new Date().toISOString(),
    summary: {
      totalTwitterInfluencers: twitterStats.totalInfluencers,
      totalRedditCommunities: redditStats.totalSubreddits,
      outdatedTargets: outdatedData.outdatedInfluencers.length + outdatedData.outdatedSubreddits.length,
      recommendationCount: recommendations.length
    },
    twitterStats,
    redditStats,
    outdatedTargets: {
      twitter: outdatedData.outdatedInfluencers.length,
      reddit: outdatedData.outdatedSubreddits.length
    },
    recommendations
  };
  
  const reportPath = join(projectRoot, 'logs', 'target-analysis-report.json');
  writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`\n\n📄 Full report saved to: logs/target-analysis-report.json`);
}

// Run the analysis
try {
  const twitterStats = analyzeTwitterInfluencers();
  const redditStats = analyzeRedditCommunities();
  const outdatedData = findOutdatedTargets();
  const recommendations = generateRecommendations(twitterStats, redditStats, outdatedData);
  
  exportAnalysisReport(twitterStats, redditStats, outdatedData, recommendations);
  
  console.log('\n✅ Target analysis completed');
} catch (error) {
  console.error('❌ Analysis failed:', error.message);
  process.exit(1);
}