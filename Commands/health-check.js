#!/usr/bin/env node

/**
 * Health Check Command
 * Verifies all components of the MCP X/Reddit Scraper are working correctly
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync, readFileSync } from 'fs';
import { spawn } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🏥 MCP X/Reddit Scraper Health Check');
console.log('=====================================\n');

const checks = {
  typescript: false,
  python: false,
  fabric: false,
  dependencies: false,
  env: false,
  logs: false,
  build: false,
  monitoring: false
};

// Check TypeScript compilation
async function checkTypeScript() {
  console.log('📦 Checking TypeScript...');
  try {
    await runCommand('npm', ['run', 'build']);
    checks.typescript = true;
    console.log('✅ TypeScript compilation successful');
  } catch (error) {
    console.log('❌ TypeScript compilation failed');
  }
}

// Check Python environment
async function checkPython() {
  console.log('\n🐍 Checking Python environment...');
  const venvPython = join(projectRoot, 'venv', 'bin', 'python');
  
  if (existsSync(venvPython)) {
    try {
      await runCommand(venvPython, ['--version']);
      checks.python = true;
      console.log('✅ Python virtual environment found');
    } catch {
      console.log('❌ Python virtual environment not working');
    }
  } else {
    console.log('❌ Python virtual environment not found');
  }
}

// Check Fabric installation
async function checkFabric() {
  console.log('\n🧵 Checking Fabric...');
  const fabricPath = join(process.env.HOME || '', 'go', 'bin', 'fabric');
  
  if (existsSync(fabricPath)) {
    try {
      await runCommand(fabricPath, ['--version']);
      checks.fabric = true;
      console.log('✅ Fabric is installed');
    } catch {
      console.log('⚠️  Fabric found but not configured');
    }
  } else {
    console.log('❌ Fabric not installed');
  }
}

// Check dependencies
function checkDependencies() {
  console.log('\n📚 Checking dependencies...');
  const packageJson = JSON.parse(readFileSync(join(projectRoot, 'package.json'), 'utf-8'));
  const nodeModulesExist = existsSync(join(projectRoot, 'node_modules'));
  
  if (nodeModulesExist) {
    checks.dependencies = true;
    console.log('✅ Node dependencies installed');
    console.log(`   Dependencies: ${Object.keys(packageJson.dependencies).length}`);
    console.log(`   Dev dependencies: ${Object.keys(packageJson.devDependencies).length}`);
  } else {
    console.log('❌ Node dependencies not installed');
  }
}

// Check environment variables
function checkEnvironment() {
  console.log('\n🔐 Checking environment...');
  const envExists = existsSync(join(projectRoot, '.env'));
  
  if (envExists) {
    const envContent = readFileSync(join(projectRoot, '.env'), 'utf-8');
    const hasApiKey = envContent.includes('OPENAI_API_KEY');
    
    if (hasApiKey) {
      checks.env = true;
      console.log('✅ Environment configured with API key');
    } else {
      console.log('⚠️  .env file exists but missing OPENAI_API_KEY');
    }
  } else {
    console.log('❌ .env file not found');
  }
}

// Check logging setup
function checkLogging() {
  console.log('\n📝 Checking logging setup...');
  const logsDir = join(projectRoot, 'logs');
  
  if (existsSync(logsDir)) {
    checks.logs = true;
    console.log('✅ Logs directory exists');
  } else {
    console.log('❌ Logs directory not found');
  }
}

// Check monitoring
function checkMonitoring() {
  console.log('\n📊 Checking monitoring...');
  const monitoringFile = join(projectRoot, 'logs', 'scraping-monitor.json');
  
  if (existsSync(monitoringFile)) {
    try {
      const monitoring = JSON.parse(readFileSync(monitoringFile, 'utf-8'));
      checks.monitoring = true;
      console.log('✅ Monitoring system active');
      console.log(`   Last updated: ${monitoring.lastUpdated || 'Unknown'}`);
    } catch {
      console.log('⚠️  Monitoring file exists but corrupted');
    }
  } else {
    console.log('⚠️  No monitoring data yet');
  }
}

// Helper function to run commands
function runCommand(command, args) {
  return new Promise((resolve, reject) => {
    const proc = spawn(command, args, { cwd: projectRoot });
    let output = '';
    
    proc.stdout.on('data', (data) => { output += data; });
    proc.stderr.on('data', (data) => { output += data; });
    
    proc.on('close', (code) => {
      if (code === 0) resolve(output);
      else reject(new Error(`Command failed with code ${code}`));
    });
  });
}

// Run all checks
async function runHealthCheck() {
  await checkTypeScript();
  await checkPython();
  await checkFabric();
  checkDependencies();
  checkEnvironment();
  checkLogging();
  checkMonitoring();
  
  // Summary
  console.log('\n📋 HEALTH CHECK SUMMARY');
  console.log('=======================');
  
  const totalChecks = Object.keys(checks).length;
  const passedChecks = Object.values(checks).filter(v => v).length;
  const healthScore = Math.round((passedChecks / totalChecks) * 100);
  
  Object.entries(checks).forEach(([check, passed]) => {
    const status = passed ? '✅' : '❌';
    const checkName = check.charAt(0).toUpperCase() + check.slice(1);
    console.log(`${status} ${checkName}`);
  });
  
  console.log(`\n🏆 Health Score: ${healthScore}%`);
  
  if (healthScore === 100) {
    console.log('🎉 All systems operational!');
  } else if (healthScore >= 75) {
    console.log('👍 System is mostly healthy');
  } else if (healthScore >= 50) {
    console.log('⚠️  System needs attention');
  } else {
    console.log('🚨 System requires immediate setup');
  }
  
  process.exit(healthScore === 100 ? 0 : 1);
}

// Run the health check
runHealthCheck().catch(error => {
  console.error('❌ Health check failed:', error.message);
  process.exit(1);
});