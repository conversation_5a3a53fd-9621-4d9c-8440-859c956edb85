#!/usr/bin/env node

/**
 * MCP Tool Testing Suite
 * Tests individual MCP tools and their responses
 */

const { spawn } = require('child_process');

console.log('🔧 MCP Tool Testing Suite\n');

function testMCPTool(toolName, args = {}, timeout = 15000) {
  return new Promise((resolve, reject) => {
    console.log(`Testing tool: ${toolName}`);
    
    const request = {
      jsonrpc: "2.0",
      id: 1,
      method: "tools/call",
      params: {
        name: toolName,
        arguments: args
      }
    };

    const child = spawn('node', ['build/index.js'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';
    let hasResponded = false;

    child.stdout.on('data', (data) => {
      output += data.toString();
      
      // Look for JSON response
      const lines = output.split('\n');
      for (const line of lines) {
        if (line.trim().startsWith('{') && line.includes('"jsonrpc"')) {
          if (!hasResponded) {
            hasResponded = true;
            try {
              const response = JSON.parse(line.trim());
              child.kill();
              resolve(response);
              return;
            } catch (e) {
              // Continue looking for valid JSON
            }
          }
        }
      }
    });

    child.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    child.on('close', (code) => {
      if (!hasResponded) {
        resolve({ 
          error: 'No valid JSON response', 
          output, 
          errorOutput, 
          exitCode: code 
        });
      }
    });

    child.on('error', (err) => {
      if (!hasResponded) {
        hasResponded = true;
        reject(err);
      }
    });

    // Send the request
    child.stdin.write(JSON.stringify(request) + '\n');
    child.stdin.end();

    // Timeout
    setTimeout(() => {
      if (!hasResponded) {
        hasResponded = true;
        child.kill();
        resolve({ error: 'Timeout', output, errorOutput });
      }
    }, timeout);
  });
}

async function runToolTests() {
  const tests = [
    {
      name: 'System Health',
      tool: 'get_system_health',
      args: {},
      expectSuccess: true
    },
    {
      name: 'List Targets',
      tool: 'list_targets',
      args: {},
      expectSuccess: true
    },
    {
      name: 'AI Chat',
      tool: '@chat',
      args: { message: 'Hello, test message' },
      expectSuccess: true
    },
    {
      name: 'Fabric Patterns',
      tool: 'list_fabric_patterns',
      args: {},
      expectSuccess: true
    },
    {
      name: 'Target Categories',
      tool: 'get_target_categories',
      args: {},
      expectSuccess: true
    }
  ];

  let passed = 0;
  let total = tests.length;

  console.log(`Running ${total} tool tests...\n`);

  for (const test of tests) {
    try {
      console.log(`🔍 ${test.name}...`);
      const response = await testMCPTool(test.tool, test.args);
      
      if (response.result) {
        console.log(`✅ ${test.name} - SUCCESS`);
        if (typeof response.result === 'object') {
          const keys = Object.keys(response.result);
          console.log(`   Response keys: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`);
        }
        passed++;
      } else if (response.error) {
        console.log(`❌ ${test.name} - ERROR: ${response.error}`);
        if (response.output) {
          console.log(`   Output: ${response.output.substring(0, 100)}...`);
        }
      } else {
        console.log(`⚠️  ${test.name} - UNEXPECTED RESPONSE`);
        console.log(`   Response: ${JSON.stringify(response).substring(0, 100)}...`);
      }
    } catch (error) {
      console.log(`❌ ${test.name} - CRASHED: ${error.message}`);
    }
    
    console.log(''); // Empty line for readability
  }

  console.log('📊 Test Results:');
  console.log(`   Passed: ${passed}/${total}`);
  console.log(`   Success Rate: ${Math.round((passed/total) * 100)}%`);
  
  if (passed === total) {
    console.log('\n🎉 All MCP tools are working correctly!');
  } else {
    console.log('\n⚠️  Some tools need attention. Check the output above.');
  }

  return passed === total;
}

// Run if called directly
if (require.main === module) {
  runToolTests().catch(console.error);
}

module.exports = { testMCPTool, runToolTests };
