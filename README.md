# X (Twitter) & Reddit Scraper MCP Server with AI Agent + Fabric Integration

An advanced **modular MCP server** that provides tools for safe scraping of X (Twitter) and Reddit content with comprehensive IP protection, tracking key contributors, analyzing trends, and performing sentiment analysis. Features an integrated Pydantic-based AI agent for intelligent report generation, multi-source analysis, and conversational interactions. Enhanced with **<PERSON>'s Fabric framework** for proven AI pattern analysis and **enterprise-grade safe scraping infrastructure** to prevent IP blacklisting.

## 🏗️ **NEW: Modular Architecture (v0.3.0)**

This version features a **completely modularized architecture** with:

- **70% reduction** in main file complexity (549 → 107 lines)
- **6 specialized handler modules** for clean separation of concerns
- **Centralized tool registry** for easy maintenance and testing
- **Enhanced configuration validation** with automatic environment detection
- **49 tools** organized across Twitter, Reddit, Browser, AI, Target Management, and Enhanced categories

### 🏛️ **Architecture Overview**

```
src/
├── index.ts                 # Main MCP server (107 lines)
├── tool-registry.ts         # Central tool management
├── config-validator.ts      # System validation
├── handlers/                # Modular tool handlers
│   ├── twitter-handlers.ts  # Twitter/X tools (6 tools)
│   ├── reddit-handlers.ts   # Reddit tools (6 tools)
│   ├── browser-handlers.ts  # Browser automation (6 tools)
│   ├── ai-handlers.ts       # AI & Fabric tools (11 tools)
│   ├── target-handlers.ts   # Target management (11 tools)
│   └── enhanced-handlers.ts # System & analytics (9 tools)
├── schemas/                 # Zod validation schemas
└── components/              # Core functionality modules
```

## Features

### 🛡️ Safe Scraping Infrastructure (ENHANCED!)

- **IP Protection**: Advanced rate limiting (10-30s delays) to prevent blacklisting
- **Circuit Breakers**: Automatic error recovery and graceful degradation
- **User Agent Rotation**: Randomized browser signatures for detection avoidance
- **Real-time Monitoring**: Track success rates and blocking indicators
- **Fallback Systems**: Intelligent failover to cached data or safe alternatives
- **Human-like Behavior**: Realistic timing, mouse movements, and scrolling
- **Multi-mode Operation**: STRICT (maximum safety), MODERATE (balanced), AGGRESSIVE (higher risk)
- **Puppeteer-Extra Stealth**: Advanced anti-detection with 20+ evasion techniques
- **Proxy Support**: Built-in proxy rotation for IP diversity

### X/Twitter Specific Tools

- **get_comments** - Scrape comments from specific users
- **get_key_contributors** - Find influential users for topics/hashtags
- **get_trending_topics** - Retrieve trending topics with category filtering
- **analyze_sentiment** - Perform sentiment analysis on posts

### Reddit Specific Tools

- **get_subreddit_posts** - Get posts from a specific subreddit with sorting options
- **get_reddit_comments** - Extract comments from Reddit posts
- **get_reddit_trending** - Find trending subreddits by category
- **search_reddit** - Search posts across Reddit or within subreddits
- **analyze_reddit_trends** - Analyze posting patterns and engagement metrics
- **analyze_reddit_comments** - Sentiment analysis on Reddit comment threads

### AI Agent Tools

- **chat** - Simple @chat interface - just type your message (Recommended)
- **create_chat_session** - Create a new AI chat session for analysis
- **chat_with_agent** - Send messages to AI agent for analysis, questions, and reports
- **get_session_info** - Get information about active chat sessions
- **export_report** - Export AI-generated reports in JSON, Markdown, or HTML
- **get_trending_keywords** - AI-powered trending keyword discovery
- **analyze_keywords_with_agent** - Advanced keyword analysis with AI insights

### Fabric Pattern Tools

- **list_fabric_patterns** - List all available Fabric analysis patterns
- **apply_fabric_pattern** - Apply specific Fabric patterns to content
- **chain_fabric_patterns** - Chain multiple patterns for complex analysis
- **create_custom_pattern** - Create custom Fabric patterns for specific needs
- **analyze_with_fabric** - Smart pattern selection and analysis
- **batch_apply_pattern** - Apply patterns to multiple content items

### Browser Automation Tools

- **navigate** - Navigate to any URL
- **screenshot** - Capture screenshots of pages or elements
- **click** - Click elements using CSS selectors
- **fill** - Fill input fields
- **scroll** - Scroll pages up or down

### Target Management Tools (NEW!)

- **list_targets** - List current influencers and subreddits by category
- **get_target_categories** - Get all available categories with descriptions
- **add_influencer** - Add new Twitter influencers to categories
- **add_subreddit** - Add new Reddit subreddits to categories
- **remove_target** - Remove influencers or subreddits from tracking
- **get_top_targets** - Get top-ranked targets by relevance/activity
- **batch_analyze_targets** - Analyze multiple categories in batch
- **discover_new_targets** - AI-powered discovery of new targets
- **get_recent_discoveries** - View recently discovered targets awaiting approval
- **promote_discovered_target** - Promote discoveries to main target lists
- **update_target_relevance** - Update target scores with current metrics

## Installation

### Node.js Dependencies

```bash
cd ~/mcp-x-scraper
npm install
npm run build
```

### Python Dependencies

```bash
pip install -r requirements.txt
```

### Environment Variables

Create a `.env` file with:

```
OPENAI_API_KEY=your_openai_api_key_here
DEFAULT_MODEL=gpt-4.1
DEFAULT_VENDOR=OpenAI

# Safe Scraping Configuration (Optional)
SCRAPING_MODE=strict           # Options: strict, moderate, aggressive
MAX_CONCURRENT_REQUESTS=1      # Number of concurrent requests allowed
ENABLE_MONITORING=true         # Enable real-time monitoring

# Proxy Configuration (Optional)
PROXY_ENABLED=false                           # Set to true to enable proxy support
PROXY_URL=http://proxy.example.com:8080      # Proxy server URL
PROXY_USERNAME=your_proxy_username           # Proxy authentication username
PROXY_PASSWORD=your_proxy_password           # Proxy authentication password
PROXY_ROTATE_ENDPOINT=                       # Optional: Endpoint for rotating proxy services
```

### Install and Setup Fabric

```bash
# Install Go (if not already installed)
brew install go

# Install Fabric
go install github.com/danielmiessler/fabric@latest

# Setup Fabric (optional - for direct Fabric usage)
~/go/bin/fabric --setup
```

## Installation

### Option 1: MCP Installation (Recommended for Claude Code)

#### Quick Install

```bash
# Install via npm
npm install -g mcp-x-reddit-scraper

# Auto-register with Claude Code
npm run install-mcp
```

#### Manual MCP Registration

```bash
# Add server to Claude Code
claude mcp add x-reddit-scraper -s local -- mcp-x-reddit-scraper

# Or add manually to your MCP settings:
```

```json
{
  "mcpServers": {
    "x-reddit-scraper": {
      "command": "mcp-x-reddit-scraper",
      "env": {
        "OPENAI_API_KEY": "your-api-key-here"
      }
    }
  }
}
```

#### Project-Scope Installation

For team projects, copy `.mcp.json` to your project root and run:

```bash
claude mcp install
```

### Option 2: Docker Deployment (Recommended for Remote Use)

For a self-contained deployment that runs remotely without installing tools locally:

```bash
# Quick start with Docker
./deploy.sh
```

Or manually:

```bash
# Set environment variables
export OPENAI_API_KEY=your_api_key_here

# Build and run with Docker Compose
docker-compose up -d

# Check status
docker logs -f mcp-x-scraper
```

### Option 3: Local Development Setup

#### Installation Requirements

- Node.js 18+
- Python 3.9+
- Go 1.19+ (for Fabric)

```bash
# Clone and install
git clone https://github.com/username/mcp-x-reddit-scraper.git
cd mcp-x-reddit-scraper

# Install Node.js dependencies
npm install
npm run build

# Install Python dependencies
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt

# Install Fabric
go install github.com/danielmiessler/fabric@latest

# Register with Claude Code
npm run install-mcp
```

## Configuration

### Environment Variables

Set these environment variables before use:

```bash
export OPENAI_API_KEY=your_api_key_here
export DEFAULT_MODEL=gpt-4.1
export DEFAULT_VENDOR=OpenAI
```

### For Docker Deployment

The Docker setup is fully self-contained with the above environment variables.

### For MCP Integration

The server automatically registers with Claude Code. Configuration options:

#### User-Scope (Personal)

```bash
claude mcp add x-reddit-scraper -s user -- mcp-x-reddit-scraper
```

#### Local-Scope (Current Directory)

```bash
claude mcp add x-reddit-scraper -s local -- mcp-x-reddit-scraper
```

#### Project-Scope (Team/Shared)

Copy `.mcp.json` to project root and run:

```bash
claude mcp install
```

### Manual Configuration

Add to your MCP settings:

```json
{
  "mcpServers": {
    "x-scraper": {
      "command": "node",
      "args": ["/Users/<USER>/mcp-x-scraper/build/index.js"]
    }
  }
}
```

## Usage Examples

### X/Twitter Examples

#### Get Comments from a User

```
get_comments({
  "username": "elonmusk",
  "limit": 10,
  "includeReplies": true
})
```

#### Find Key Contributors

```
get_key_contributors({
  "topic": "#AI",
  "limit": 5
})
```

#### Get Trending Topics

```
get_trending_topics({
  "category": "tech",
  "location": "worldwide"
})
```

### Reddit Examples

#### Get Subreddit Posts

```
get_subreddit_posts({
  "subreddit": "technology",
  "sort": "hot",
  "limit": 25
})
```

#### Search Reddit

```
search_reddit({
  "query": "artificial intelligence",
  "subreddit": "programming",
  "sort": "top",
  "timeframe": "week",
  "limit": 20
})
```

#### Get Reddit Comments

```
get_reddit_comments({
  "postUrl": "https://www.reddit.com/r/technology/comments/...",
  "limit": 50,
  "sortBy": "best"
})
```

#### Analyze Reddit Trends

```
analyze_reddit_trends({
  "subreddit": "technology",
  "timeframe": "week"
})
```

#### Analyze Reddit Comment Sentiment

```
analyze_reddit_comments({
  "postUrl": "https://www.reddit.com/r/technology/comments/..."
})
```

### AI Agent Examples

#### Simple Chat Interface (Recommended)

The easiest way to interact with the AI agent is using the `@chat` command:

```
// Simple @chat interface - no session management needed
chat({
  "message": "Find the latest AI news and trending topics from Twitter"
})

// Continue the conversation naturally
chat({
  "message": "What are the key differences in AI discussions between Twitter and Reddit?"
})

// Ask for analysis and reports
chat({
  "message": "Create a summary report of today's AI developments"
})
```

#### Advanced Chat Session Management

For more control, you can manage sessions explicitly:

```
// Create a new chat session
create_chat_session({})

// Chat with the agent
chat_with_agent({
  "sessionId": "chat_20240101_120000",
  "message": "Analyze trending topics around AI and machine learning on both Twitter and Reddit from the past week"
})

// Export the generated report
export_report({
  "sessionId": "chat_20240101_120000",
  "format": "markdown"
})
```

#### Ask Questions About Data

```
chat_with_agent({
  "sessionId": "chat_20240101_120000",
  "message": "What are the key differences in how AI is discussed on Twitter vs Reddit?"
})
```

#### Get AI-Powered Trending Keywords

```
get_trending_keywords({
  "sources": ["both"],
  "limit": 15
})
```

#### Advanced Keyword Analysis

```
analyze_keywords_with_agent({
  "keywords": ["artificial intelligence", "machine learning", "deep learning"],
  "sources": ["twitter", "reddit"]
})
```

### Fabric Pattern Examples

#### List Available Patterns

```
list_fabric_patterns({
  "category": "analysis"
})
```

#### Apply Social Media Sentiment Analysis

```
apply_fabric_pattern({
  "patternName": "analyze_social_sentiment",
  "content": "Amazing new AI breakthrough! This could change everything. #AI #innovation",
  "model": "gpt-4.1"
})
```

#### Extract Trending Insights from Multiple Posts

```
batch_apply_pattern({
  "patternName": "extract_trending_insights",
  "contentArray": [
    "Post 1 content...",
    "Post 2 content...",
    "Post 3 content..."
  ]
})
```

#### Chain Multiple Patterns for Deep Analysis

```
chain_fabric_patterns({
  "patterns": ["analyze_social_sentiment", "extract_trending_insights", "generate_social_strategy"],
  "content": "Social media discussion content..."
})
```

#### Smart Fabric Analysis

```
analyze_with_fabric({
  "content": "Complex social media discussion thread...",
  "analysisType": "insights"
})
```

#### Create Custom Pattern for Specific Analysis

```
create_custom_pattern({
  "name": "analyze_crypto_sentiment",
  "description": "Analyze cryptocurrency discussions and market sentiment",
  "systemPrompt": "You are a crypto market analyst. Analyze the sentiment and market implications of cryptocurrency discussions..."
})
```

#### Multi-Step Analysis Workflow

```
// 1. Create session
create_chat_session({})

// 2. Start with trending discovery
chat_with_agent({"message": "Find trending tech topics from the last 24 hours"})

// 3. Deep dive analysis
chat_with_agent({"message": "Create a detailed sentiment analysis report on the top 3 trending topics"})

// 4. Ask follow-up questions
chat_with_agent({"message": "Which platform shows more positive sentiment for AI discussions?"})

// 5. Generate recommendations
chat_with_agent({"message": "Based on this analysis, what content strategy would you recommend?"})

// 6. Export comprehensive report
export_report({"format": "html"})
```

### General Tools

#### Analyze Sentiment

```
analyze_sentiment({
  "posts": ["Great product!", "Terrible experience"],
  "granularity": "aggregate"
})
```

#### Browser Automation

```
navigate({ "url": "https://x.com" })
screenshot({ "name": "twitter_home" })
click({ "selector": "[data-testid='tweet']" })
```

### Target Management Examples (NEW!)

#### View Current Influencers and Subreddits

```javascript
// List all targets
list_targets({
  type: 'both',
});

// List only AI/ML Twitter influencers
list_targets({
  type: 'twitter',
  category: 'ai_ml',
});

// Get available categories with descriptions
get_target_categories({});
```

#### Add New Targets Manually

```javascript
// Add a new Twitter influencer
add_influencer({
  category: 'ai_ml',
  username: 'newaiexpert',
  followers: '500K+',
  relevance_score: 0.85,
  topics: ['machine learning', 'neural networks'],
});

// Add a new Reddit subreddit
add_subreddit({
  category: 'ai_ml_tech',
  name: 'LocalLLaMA',
  subscribers: '200K',
  activity_score: 0.9,
});
```

#### AI-Powered Target Discovery

```javascript
// Discover new AI/ML influencers and communities
discover_new_targets({
  category: 'ai_ml',
  keywords: ['artificial intelligence', 'machine learning', 'LLM'],
  limit: 10,
});

// View recently discovered targets
get_recent_discoveries({});

// Promote a discovered target to main list
promote_discovered_target({
  type: 'twitter',
  category: 'ai_ml',
  identifier: 'newdiscoveredexpert',
});
```

#### Batch Analysis and Management

```javascript
// Analyze multiple target categories
batch_analyze_targets({
  twitter_categories: ['ai_ml', 'tech_ceos'],
  reddit_categories: ['ai_ml_tech', 'emerging_trends'],
  analysis_type: 'full',
});

// Get top-performing targets across all categories
get_top_targets({
  limit: 20,
});

// Update target with current metrics
update_target_relevance({
  type: 'twitter',
  category: 'ai_ml',
  identifier: 'karpathy',
  check_current_metrics: true,
});
```

#### Target Management Workflow

```javascript
// 1. View current landscape
get_target_categories({});

// 2. Discover new targets in a category
discover_new_targets({ category: 'crypto_web3', limit: 5 });

// 3. Review discoveries
get_recent_discoveries({});

// 4. Promote approved targets
promote_discovered_target({
  type: 'reddit',
  category: 'crypto_web3',
  identifier: 'discovered_crypto_community',
});

// 5. Run batch analysis on updated targets
batch_analyze_targets({
  twitter_categories: ['crypto_web3'],
  reddit_categories: ['crypto_web3'],
  analysis_type: 'sentiment',
});

// 6. Update relevance scores with current metrics
update_target_relevance({
  type: 'twitter',
  category: 'crypto_web3',
  identifier: 'VitalikButerin',
  check_current_metrics: true,
});
```

## Docker Features

### Self-Contained Deployment

- **No Local Installation**: All dependencies (Node.js, Python, Go, Fabric) included in Docker image
- **Remote Ready**: Can be deployed on any Docker-compatible server
- **One-Command Deploy**: Use `./deploy.sh` for automated setup
- **Volume Mounts**: Screenshots and custom patterns accessible from host
- **Health Checks**: Built-in health monitoring
- **Auto-Restart**: Container restarts automatically on failure

### Docker Management Commands

```bash
# Start services
docker-compose up -d

# View logs
docker logs -f mcp-x-scraper

# Stop services
docker-compose down

# Rebuild after code changes
docker-compose build --no-cache

# Shell access
docker exec -it mcp-x-scraper /bin/sh
```

## Enhanced Stealth & Proxy Features

### Puppeteer-Extra Stealth Plugin

The project now includes puppeteer-extra with the stealth plugin, providing:

- **WebGL Vendor Spoofing**: Masks graphics card information
- **Navigator Property Masking**: Hides automation indicators
- **Console Debugging Evasion**: Prevents detection via console
- **Codec Detection Bypass**: Masks missing codecs in headless mode
- **Window Dimension Adjustments**: Realistic window sizes
- **Plugin & Language Spoofing**: Mimics real browser configurations
- **20+ Evasion Techniques**: Comprehensive anti-detection coverage

### Proxy Integration

Built-in support for proxy services including:

- **HTTP/HTTPS Proxies**: Standard proxy protocol support
- **SOCKS5 Proxies**: Advanced proxy protocol support
- **Rotating Proxies**: Automatic IP rotation for services like:
  - Bright Data (formerly Luminati)
  - Oxylabs
  - Smartproxy
  - ProxyMesh
- **Authentication**: Username/password authentication support
- **Rotation Endpoints**: Integration with proxy rotation APIs

### Testing Stealth Capabilities

```bash
# Test stealth effectiveness
node test-stealth-scraping.js

# The test will:
# 1. Check against bot detection sites
# 2. Verify proxy configuration
# 3. Display performance metrics
```

## Safe Scraping Details

### Safety Modes

#### STRICT Mode (Default - Recommended)

- **Request Delays**: 10-30 seconds between requests
- **Daily Limit**: 50 requests per target
- **Hourly Limit**: 5 requests per target
- **Max Concurrent**: 1 request at a time
- **Error Threshold**: 10% before circuit breaker activation
- **Use Case**: Maximum protection for production use

#### MODERATE Mode

- **Request Delays**: 5-15 seconds between requests
- **Daily Limit**: 200 requests per target
- **Hourly Limit**: 20 requests per target
- **Max Concurrent**: 2 simultaneous requests
- **Error Threshold**: 20% before circuit breaker activation
- **Use Case**: Balanced protection with reasonable throughput

#### AGGRESSIVE Mode (Use with Caution)

- **Request Delays**: 2-5 seconds between requests
- **Daily Limit**: 1000 requests per target
- **Hourly Limit**: 100 requests per target
- **Max Concurrent**: 5 simultaneous requests
- **Error Threshold**: 30% before circuit breaker activation
- **Use Case**: Higher risk, use only with established IPs

### Monitoring & Protection Features

- **Real-time Monitoring**: Track all requests in `scraping-monitor.json`
- **Circuit Breakers**: Auto-pause on high error rates with recovery testing
- **User Agent Rotation**: 20+ different browser signatures
- **Request Jitter**: Random delays added to avoid patterns (±20%)
- **Cache First**: Check cache before making new requests
- **Graceful Degradation**: Fallback to cached data on failures
- **Emergency Stop**: Manual kill switch for immediate halt

### Testing Safe Scraping

```bash
# Test IP protection systems
node safe-scraping-test.js

# Monitor live data collection
node live-monitoring-test.js

# Check monitoring status
cat scraping-monitor.json
```

## Developer Experience

### Claude Code Boost Integration

This project includes **claude-code-boost** for enhanced development workflow:

- **Auto-approval** of safe operations (file reads, builds, tests)
- **Intelligent decision making** for complex operations
- **Smart caching** to reduce redundant prompts
- Improves development speed by reducing interruptions

To use globally:

```bash
npm install -g claude-code-boost
ccb install
```

### GitHub Integration

Comprehensive GitHub integration with CLI and API access:

- **GitHub CLI** - Repository management from command line
- **Octokit REST API** - Programmatic GitHub API access
- **Husky + lint-staged** - Modern git hooks with automatic code formatting
- **Custom GitHub Manager** - Issue tracking, PR management, workflow monitoring

```bash
# GitHub CLI commands (requires gh auth login)
node Commands/github-manager.js status    # Repository statistics
node Commands/github-manager.js issues    # List open issues
node Commands/github-manager.js workflow  # GitHub Actions status
```

## Notes

- Uses Puppeteer for web scraping (headless browser)
- **Safe Scraping**: Enterprise-grade IP protection with rate limiting and monitoring
- Implements caching to reduce load (10-minute TTL)
- Screenshots saved to `screenshots/` directory (mounted in Docker)
- **Fabric Integration**: Leverages Daniel Miessler's proven AI pattern library
- **Triple Analysis**: Keyword-based + AI-powered + Fabric pattern analysis
- AI agent uses OpenAI GPT-4.1 for advanced analysis
- Supports multiple export formats (JSON, Markdown, HTML)
- Python bridge enables seamless integration between TypeScript and Python components
- Pydantic models ensure data validation and type safety
- Custom social media patterns for specialized analysis
- Pattern chaining for multi-step complex analysis workflows
- **Docker Ready**: Full containerization for easy deployment and scaling
- **Production Tested**: Zero IP blocking detected during live testing

## AI Agent + Fabric Capabilities

The integrated AI agent enhanced with Fabric can:

### Core AI Agent Features:

- **Intelligent Analysis**: Automatically interpret user requests and coordinate multi-source scraping
- **Report Generation**: Create comprehensive reports with executive summaries, insights, and recommendations
- **Conversational Interface**: Answer questions about scraped data and provide contextual insights
- **Trend Discovery**: Identify emerging trends and patterns across platforms
- **Cross-Platform Comparison**: Compare discussions and trends between Twitter and Reddit
- **Actionable Recommendations**: Generate strategic recommendations based on data analysis

### Enhanced Fabric Pattern Features:

- **Proven Prompts**: Access 50+ battle-tested AI patterns from the Fabric community
- **Specialized Analysis**: Deep content analysis using human-crafted prompts
- **Pattern Chaining**: Combine multiple patterns for complex multi-step analysis
- **Custom Patterns**: Create domain-specific patterns for unique analysis needs
- **Consistent Quality**: Standardized, high-quality outputs from proven patterns

### Custom Social Media Patterns:

- **analyze_social_sentiment** - Advanced social media sentiment analysis
- **extract_trending_insights** - Identify emerging trends and viral content
- **compare_platform_sentiment** - Compare Twitter vs Reddit discussions
- **generate_social_strategy** - Create data-driven social media strategies

## Target Management System

### Overview

The MCP X & Reddit Scraper now includes a comprehensive target management system that combines **configuration-based lists** with **AI-driven discovery** to keep your influencer and subreddit tracking current and relevant.

### Key Features

#### 🎯 **Pre-configured Expert Lists**

- **70+ high-quality targets** across 8 categories
- **Relevance scoring** (0-1 scale) based on follower count, engagement, and topic alignment
- **Regular verification** with last-verified timestamps
- **Organized categories**: AI/ML, Tech CEOs, Crypto/Web3, Startup/VC, Business/Finance, etc.

#### 🤖 **AI-Powered Discovery**

- **Intelligent keyword analysis** using GPT-4.1
- **Automated relevance scoring** based on engagement metrics
- **Spam filtering** and quality assessment
- **Discovery queue** for manual approval before adding to main lists

#### 📊 **Dynamic Updates**

- **Real-time metrics** fetching from Twitter and Reddit
- **Relevance score updates** based on current follower/activity data
- **Automatic verification** timestamps
- **Trending analysis** integration

### Current Target Categories

#### Twitter Influencers

- **ai_ml**: Yann LeCun, Andrew Ng, Andrej Karpathy, Jeremy Howard, François Chollet
- **tech_ceos**: Elon Musk, Sundar Pichai, Satya Nadella, Tim Cook, Jeff Weiner
- **crypto_web3**: Vitalik Buterin, Michael Saylor, CZ, Naval, Balaji
- **startup_vc**: Paul Graham, Marc Benioff, Chamath, Jason Calacanis, Gary Vee

#### Reddit Communities

- **ai_ml_tech**: MachineLearning, artificial, OpenAI, singularity, technology, programming
- **business_finance**: investing, business, entrepreneur, startups, SecurityAnalysis
- **crypto_web3**: CryptoCurrency, Bitcoin, ethereum, defi, NFT
- **emerging_trends**: Futurology, innovation, tech, gadgets

### How It Works

1. **Start with curated lists** of verified high-quality targets
2. **Use AI discovery** to find new emerging influencers and communities
3. **Review discoveries** in the approval queue with relevance scores
4. **Promote approved targets** to main tracking lists
5. **Update metrics regularly** to maintain current relevance scores
6. **Analyze trends** across your customized target portfolio

### Why This Approach?

- **Quality + Discovery**: Combines human curation with AI-powered discovery
- **Always Current**: Regular metric updates keep relevance scores accurate
- **Scalable**: Easy to add new categories and targets as landscapes change
- **Intelligent**: AI filters out spam and low-quality targets automatically
- **Manageable**: Approval workflow prevents noise from cluttering your lists

## MCP Features

### Model Context Protocol Integration

- **Automatic Discovery**: Server tools are automatically available in Claude Code
- **Schema Validation**: All tools use Zod schemas for type safety
- **Error Handling**: Comprehensive error responses with helpful messages
- **Resource Management**: Efficient connection and resource handling
- **Multi-Scope Support**: Install at user, local, or project scope

### Installation Methods

- **NPM Global**: `npm install -g mcp-x-reddit-scraper`
- **Direct Command**: `claude mcp add x-reddit-scraper`
- **Project Config**: Use `.mcp.json` for team installations
- **Docker Deployment**: Self-contained with all dependencies

## Troubleshooting

### MCP Installation Issues

```bash
# Check if Claude Code CLI is installed
claude --version

# List installed MCP servers
claude mcp list

# Remove and reinstall server
claude mcp remove x-reddit-scraper
claude mcp add x-reddit-scraper -s local -- mcp-x-reddit-scraper

# Check server status
claude mcp status x-reddit-scraper
```

### Common Issues

- **"Server not found"**: Run `npm run build` then `npm run install-mcp`
- **"OPENAI_API_KEY not set"**: Set environment variable or add to MCP config
- **"Python module not found"**: Check virtual environment activation
- **"Fabric pattern failed"**: Ensure Go and Fabric are installed correctly

### Environment Setup

```bash
# Verify prerequisites
node --version  # Should be 18+
python3 --version  # Should be 3.9+
go version  # Should be 1.19+

# Check API key
echo $OPENAI_API_KEY

# Test server build
npm run build
node build/index.js --version
```

## Limitations

- X.com and Reddit may have rate limits or anti-scraping measures
- AI agent requires OpenAI API key and may incur costs
- Sentiment analysis combines keyword-based and AI-powered methods
- Requires stable internet connection
- May need updates if websites change their HTML structure
- Reddit scraping works best with public subreddits
- Some Reddit features may require authentication for full access
- Python dependencies required for AI agent functionality
