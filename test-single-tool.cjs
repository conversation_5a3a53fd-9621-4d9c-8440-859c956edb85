#!/usr/bin/env node

/**
 * Test a single MCP tool
 */

const { spawn } = require('child_process');

function testTool(toolName, args = {}) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 Testing tool: ${toolName}`);
    
    const request = {
      jsonrpc: "2.0",
      id: 1,
      method: "tools/call",
      params: {
        name: toolName,
        arguments: args
      }
    };

    const child = spawn('node', ['build/index.js'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let hasResponse = false;

    child.stdout.on('data', (data) => {
      output += data.toString();
      
      // Look for JSON response
      const lines = output.split('\n');
      for (const line of lines) {
        if (line.trim().startsWith('{') && line.includes('"jsonrpc"') && !hasResponse) {
          hasResponse = true;
          try {
            const response = JSON.parse(line.trim());
            child.kill();
            resolve(response);
            return;
          } catch (e) {
            // Continue looking
          }
        }
      }
    });

    child.stderr.on('data', (data) => {
      // Ignore stderr for now
    });

    child.on('close', (code) => {
      if (!hasResponse) {
        resolve({ error: 'No response', output });
      }
    });

    // Send request
    child.stdin.write(JSON.stringify(request) + '\n');
    child.stdin.end();

    // Timeout after 10 seconds
    setTimeout(() => {
      if (!hasResponse) {
        hasResponse = true;
        child.kill();
        resolve({ error: 'Timeout' });
      }
    }, 10000);
  });
}

async function main() {
  console.log('🧪 Testing Individual MCP Tool\n');
  
  try {
    const response = await testTool('list_targets');
    
    if (response.result) {
      console.log('✅ Tool call successful!');
      console.log('📊 Response summary:');
      
      const result = response.result;
      if (result.influencers) {
        console.log(`   Twitter Influencers: ${result.influencers.length}`);
      }
      if (result.subreddits) {
        console.log(`   Subreddits: ${result.subreddits.length}`);
      }
      if (result.categories) {
        console.log(`   Categories: ${Object.keys(result.categories).length}`);
      }
      
      console.log('\n🎉 MCP tool is working correctly!');
    } else if (response.error) {
      console.log('❌ Tool call failed:', response.error);
      if (response.output) {
        console.log('Output:', response.output.substring(0, 200) + '...');
      }
    } else {
      console.log('⚠️  Unexpected response format');
      console.log('Response:', JSON.stringify(response, null, 2));
    }
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

main();
