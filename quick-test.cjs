#!/usr/bin/env node

/**
 * Quick Test - Fast verification of core functionality
 */

console.log('⚡ Quick Test - MCP X/Reddit Scraper\n');

async function quickTest() {
  try {
    // Test 1: Tool Registry
    console.log('1️⃣ Testing Tool Registry...');
    const { ToolRegistry } = require('./build/tool-registry.js');
    const registry = new ToolRegistry();
    const tools = registry.getTools();
    console.log(`   ✅ ${tools.length} tools registered`);
    
    // Test 2: Handler Modules
    console.log('2️⃣ Testing Handler Modules...');
    const handlers = [
      './build/handlers/twitter-handlers.js',
      './build/handlers/reddit-handlers.js', 
      './build/handlers/browser-handlers.js',
      './build/handlers/ai-handlers.js',
      './build/handlers/target-handlers.js',
      './build/handlers/enhanced-handlers.js'
    ];
    
    let loadedHandlers = 0;
    for (const handler of handlers) {
      try {
        require(handler);
        loadedHandlers++;
      } catch (e) {
        console.log(`   ❌ Failed to load ${handler}`);
      }
    }
    console.log(`   ✅ ${loadedHandlers}/${handlers.length} handlers loaded`);
    
    // Test 3: Component Manager
    console.log('3️⃣ Testing Component Manager...');
    const { ComponentManager } = require('./build/component-manager.js');
    const componentManager = new ComponentManager();
    console.log('   ✅ Component Manager initialized');
    
    // Test 4: Build Status
    console.log('4️⃣ Testing Build Status...');
    const fs = require('fs');
    const buildExists = fs.existsSync('./build/index.js');
    console.log(`   ${buildExists ? '✅' : '❌'} Build directory exists`);
    
    console.log('\n🎉 Quick Test Summary:');
    console.log(`   ✅ Tool Registry: ${tools.length} tools`);
    console.log(`   ✅ Handler Modules: ${loadedHandlers}/6 loaded`);
    console.log(`   ✅ Component Manager: Working`);
    console.log(`   ✅ Build Status: ${buildExists ? 'Ready' : 'Missing'}`);
    
    if (tools.length === 49 && loadedHandlers === 6 && buildExists) {
      console.log('\n🚀 System is ready for use!');
      return true;
    } else {
      console.log('\n⚠️  System has issues that need attention.');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Quick test failed:', error.message);
    return false;
  }
}

// Run test
quickTest().catch(console.error);
