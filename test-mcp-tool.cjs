#!/usr/bin/env node

/**
 * Simple MCP Tool Tester
 * Usage: node test-mcp-tool.cjs <tool_name> [args_json]
 */

const { spawn } = require('child_process');

function testTool(toolName, args = {}) {
  return new Promise((resolve, reject) => {
    const request = {
      jsonrpc: "2.0",
      id: 1,
      method: "tools/call",
      params: {
        name: toolName,
        arguments: args
      }
    };

    const child = spawn('node', ['build/index.js'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let hasResponse = false;

    child.stdout.on('data', (data) => {
      output += data.toString();
      
      // Look for JSON response
      const lines = output.split('\n');
      for (const line of lines) {
        if (line.trim().startsWith('{') && line.includes('"jsonrpc"') && !hasResponse) {
          hasResponse = true;
          try {
            const response = JSON.parse(line.trim());
            child.kill();
            resolve(response);
            return;
          } catch (e) {
            // Continue looking
          }
        }
      }
    });

    child.stderr.on('data', (data) => {
      // Ignore stderr for now
    });

    child.on('close', (code) => {
      if (!hasResponse) {
        resolve({ error: 'No response', output });
      }
    });

    // Send request
    child.stdin.write(JSON.stringify(request) + '\n');
    child.stdin.end();

    // Timeout after 15 seconds
    setTimeout(() => {
      if (!hasResponse) {
        hasResponse = true;
        child.kill();
        resolve({ error: 'Timeout' });
      }
    }, 15000);
  });
}

async function main() {
  const toolName = process.argv[2];
  const argsJson = process.argv[3];
  
  if (!toolName) {
    console.log('Usage: node test-mcp-tool.cjs <tool_name> [args_json]');
    console.log('\nExample tools to try:');
    console.log('  node test-mcp-tool.cjs list_targets');
    console.log('  node test-mcp-tool.cjs get_system_health');
    console.log('  node test-mcp-tool.cjs "@chat" \'{"message": "Hello!"}\'');
    console.log('  node test-mcp-tool.cjs list_fabric_patterns');
    return;
  }
  
  let args = {};
  if (argsJson) {
    try {
      args = JSON.parse(argsJson);
    } catch (e) {
      console.error('Invalid JSON arguments:', e.message);
      return;
    }
  }
  
  console.log(`🔧 Testing tool: ${toolName}`);
  if (Object.keys(args).length > 0) {
    console.log(`📝 Arguments: ${JSON.stringify(args)}`);
  }
  console.log('⏳ Please wait...\n');
  
  try {
    const response = await testTool(toolName, args);
    
    if (response.result) {
      console.log('✅ SUCCESS!');
      console.log('📊 Response:');
      if (response.result.content && response.result.content[0] && response.result.content[0].text) {
        // Pretty print the text content
        try {
          const parsed = JSON.parse(response.result.content[0].text);
          console.log(JSON.stringify(parsed, null, 2));
        } catch (e) {
          console.log(response.result.content[0].text);
        }
      } else {
        console.log(JSON.stringify(response.result, null, 2));
      }
    } else if (response.error) {
      console.log('❌ ERROR:', response.error);
    } else {
      console.log('⚠️  Unexpected response format');
      console.log(JSON.stringify(response, null, 2));
    }
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

main();
