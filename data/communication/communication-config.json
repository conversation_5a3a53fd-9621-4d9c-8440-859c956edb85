{"channels": [{"type": "webhook", "name": "default-webhook", "config": {"webhookUrl": "https://hooks.slack.com/services/YOUR/WEBHOOK/URL"}, "enabled": false, "rules": []}], "rules": [{"id": "critical_alerts", "name": "Critical System Alerts", "conditions": {"eventTypes": ["health_alert", "performance_degradation", "blocking_detected"], "severityLevels": ["critical", "error"], "frequency": "immediate"}, "message": {"template": "🚨 **CRITICAL ALERT**: {{title}}\n{{message}}\n\n📊 Current Metrics:\n- Success Rate: {{metrics.successRate}}%\n- Block Rate: {{metrics.blockRate}}%\n- Response Time: {{metrics.avgResponseTime}}ms", "includeMetrics": true}, "escalation": {"after": 300000, "channels": ["critical-alerts"]}}, {"id": "daily_summary", "name": "Daily Health Summary", "conditions": {"eventTypes": ["system_status"], "severityLevels": ["info"], "frequency": "daily"}, "message": {"template": "📊 **Daily Health Report**\n\n{{summary}}\n\n📈 Key Metrics:\n- Overall Health: {{health.overall}}/100\n- Uptime: {{metrics.uptime}}%\n- Data Quality: {{metrics.dataQuality}}/100", "includeMetrics": true, "includeTrends": true}}], "dashboard": {"enabled": true, "port": 3001, "cors": true, "endpoints": [{"path": "/api/health", "method": "GET", "handler": "getSystemHealth", "description": "Get current system health status"}, {"path": "/api/metrics", "method": "GET", "handler": "getLiveMetrics", "description": "Get real-time performance metrics"}, {"path": "/api/alerts", "method": "GET", "handler": "getActiveAlerts", "description": "Get current active alerts"}]}, "reporting": {"dailyReports": {"enabled": true, "time": "08:00", "recipients": [], "channels": ["default-webhook"]}, "weeklyReports": {"enabled": true, "day": 1, "time": "09:00", "recipients": [], "channels": ["default-webhook"]}, "alertBatching": {"enabled": true, "windowMinutes": 5, "maxBatchSize": 10}}}