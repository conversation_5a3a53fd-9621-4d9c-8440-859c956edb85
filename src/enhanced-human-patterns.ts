// Enhanced Human Browsing Pattern Simulation
// Ultra-realistic behavior patterns to prevent blacklisting

export interface HumanBehaviorProfile {
  // Timing Patterns
  readingSpeed: number;           // Words per minute (150-300)
  scrollSpeed: number;            // Pixels per scroll (50-200) 
  clickDelay: [number, number];   // Min/max click delay
  typingSpeed: number;            // Characters per minute
  
  // Interaction Patterns
  scrollFrequency: number;        // Scroll events per page visit
  mouseMovements: number;         // Random movements per page
  pauseProbability: number;       // Chance of pausing (0-1)
  backButtonUsage: number;        // Probability of going back
  
  // Session Patterns
  sessionDuration: [number, number]; // Min/max session length
  pagesPerSession: [number, number]; // Pages visited per session
  breakFrequency: number;         // Hours between sessions
  weekendBehavior: boolean;       // Different weekend patterns
}

export const HUMAN_PROFILES: Record<string, HumanBehaviorProfile> = {
  casual: {
    readingSpeed: 180,
    scrollSpeed: 120,
    clickDelay: [800, 2500],
    typingSpeed: 200,
    scrollFrequency: 8,
    mouseMovements: 15,
    pauseProbability: 0.3,
    backButtonUsage: 0.15,
    sessionDuration: [300000, 1800000], // 5-30 minutes
    pagesPerSession: [3, 12],
    breakFrequency: 4,
    weekendBehavior: true
  },
  
  researcher: {
    readingSpeed: 250,
    scrollSpeed: 80,
    clickDelay: [400, 1200],
    typingSpeed: 300,
    scrollFrequency: 12,
    mouseMovements: 25,
    pauseProbability: 0.5,
    backButtonUsage: 0.25,
    sessionDuration: [1200000, 3600000], // 20-60 minutes
    pagesPerSession: [8, 25],
    breakFrequency: 6,
    weekendBehavior: false
  },
  
  speed_reader: {
    readingSpeed: 400,
    scrollSpeed: 200,
    clickDelay: [200, 800],
    typingSpeed: 450,
    scrollFrequency: 20,
    mouseMovements: 35,
    pauseProbability: 0.1,
    backButtonUsage: 0.05,
    sessionDuration: [600000, 2400000], // 10-40 minutes
    pagesPerSession: [15, 40],
    breakFrequency: 3,
    weekendBehavior: true
  }
};

export class HumanBehaviorSimulator {
  private profile: HumanBehaviorProfile;
  private sessionStartTime: number;
  private pagesInSession: number = 0;
  private lastInteractionTime: number = 0;

  constructor(profileType: keyof typeof HUMAN_PROFILES = 'casual') {
    this.profile = HUMAN_PROFILES[profileType];
    this.sessionStartTime = Date.now();
  }

  // Calculate reading time based on content length
  calculateReadingTime(contentLength: number): number {
    const words = contentLength / 5; // Average 5 chars per word
    const baseTime = (words / this.profile.readingSpeed) * 60 * 1000; // ms
    return this.addRealisticJitter(baseTime, 0.4);
  }

  // Generate scroll pattern based on page height
  generateScrollPattern(pageHeight: number): Array<{delay: number, amount: number}> {
    const scrolls: Array<{delay: number, amount: number}> = [];
    let currentPosition = 0;
    
    while (currentPosition < pageHeight * 0.8) { // Don't scroll to bottom always
      const scrollAmount = this.addRealisticJitter(this.profile.scrollSpeed, 0.5);
      const scrollDelay = this.addRealisticJitter(1500, 0.6); // Pause between scrolls
      
      scrolls.push({ delay: scrollDelay, amount: scrollAmount });
      currentPosition += scrollAmount;
      
      // Random pause for "reading"
      if (Math.random() < this.profile.pauseProbability) {
        const readingPause = this.addRealisticJitter(3000, 0.8);
        scrolls.push({ delay: readingPause, amount: 0 });
      }
    }
    
    return scrolls;
  }

  // Generate realistic mouse movement patterns
  generateMouseMovements(viewportWidth: number, viewportHeight: number): Array<{x: number, y: number, delay: number}> {
    const movements: Array<{x: number, y: number, delay: number}> = [];
    let lastX = viewportWidth / 2;
    let lastY = viewportHeight / 2;
    
    for (let i = 0; i < this.profile.mouseMovements; i++) {
      // Simulate natural mouse drift
      const deltaX = this.addRealisticJitter(0, 100);
      const deltaY = this.addRealisticJitter(0, 50);
      
      lastX = Math.max(0, Math.min(viewportWidth, lastX + deltaX));
      lastY = Math.max(0, Math.min(viewportHeight, lastY + deltaY));
      
      movements.push({
        x: lastX,
        y: lastY,
        delay: this.addRealisticJitter(2000, 0.8)
      });
    }
    
    return movements;
  }

  // Check if should take a break based on realistic patterns
  shouldTakeBreak(): boolean {
    const sessionTime = Date.now() - this.sessionStartTime;
    const [minSession, maxSession] = this.profile.sessionDuration;
    
    // Probability increases as session gets longer
    if (sessionTime > maxSession) {
      return true;
    }
    if (sessionTime < minSession) {
      return false;
    }
    
    const breakProbability = (sessionTime - minSession) / (maxSession - minSession);
    return Math.random() < breakProbability;
  }

  // Generate break duration (realistic offline time)
  getBreakDuration(): number {
    const baseBreak = this.profile.breakFrequency * 60 * 60 * 1000; // Hours to ms
    return this.addRealisticJitter(baseBreak, 0.6);
  }

  // Weekend vs weekday behavior adjustments
  adjustForTimeOfWeek(): void {
    const now = new Date();
    const isWeekend = now.getDay() === 0 || now.getDay() === 6;
    const isEvening = now.getHours() > 18 || now.getHours() < 9;
    
    if (isWeekend && this.profile.weekendBehavior) {
      // More relaxed on weekends
      this.profile.readingSpeed *= 0.8;
      this.profile.pauseProbability *= 1.3;
      this.profile.sessionDuration[0] *= 1.5;
      this.profile.sessionDuration[1] *= 1.8;
    }
    
    if (isEvening) {
      // Slower in evening
      this.profile.readingSpeed *= 0.9;
      this.profile.clickDelay[0] *= 1.2;
      this.profile.clickDelay[1] *= 1.4;
    }
  }

  // Add realistic jitter that follows human patterns
  private addRealisticJitter(baseValue: number, jitterFactor: number): number {
    // Human behavior follows log-normal distribution, not uniform
    const jitter = this.logNormalRandom(0, jitterFactor);
    return Math.max(baseValue * 0.1, baseValue * (1 + jitter - 0.5));
  }

  // Log-normal distribution for more realistic randomness
  private logNormalRandom(mu: number, sigma: number): number {
    const normal = this.boxMullerRandom();
    return Math.exp(mu + sigma * normal);
  }

  // Box-Muller transformation for normal distribution
  private boxMullerRandom(): number {
    let u = 0;
    let v = 0;
    while (u === 0) {
      u = Math.random();
    } // Converting [0,1) to (0,1)
    while (v === 0) {
      v = Math.random();
    }
    return Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
  }
}

// Enhanced detection evasion patterns
export interface BrowserFingerprint {
  userAgent: string;
  viewport: { width: number; height: number };
  timezone: string;
  language: string[];
  platform: string;
  cookieEnabled: boolean;
  doNotTrack: boolean;
  hardwareConcurrency: number;
  deviceMemory?: number;
  colorDepth: number;
  pixelRatio: number;
}

export function generateRealisticFingerprint(): BrowserFingerprint {
  const fingerprints = [
    // Most common configurations based on real data
    {
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1920, height: 1080 },
      timezone: 'America/New_York',
      language: ['en-US', 'en'],
      platform: 'MacIntel',
      cookieEnabled: true,
      doNotTrack: false,
      hardwareConcurrency: 8,
      deviceMemory: 8,
      colorDepth: 24,
      pixelRatio: 2
    },
    {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      viewport: { width: 1366, height: 768 },
      timezone: 'America/Chicago',
      language: ['en-US', 'en'],
      platform: 'Win32',
      cookieEnabled: true,
      doNotTrack: true,
      hardwareConcurrency: 4,
      deviceMemory: 4,
      colorDepth: 24,
      pixelRatio: 1
    }
    // Add more realistic combinations...
  ];
  
  return fingerprints[Math.floor(Math.random() * fingerprints.length)];
}