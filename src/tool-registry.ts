/**
 * Tool Registry - Central Registration and Management
 * Factory pattern for registering all MCP tools from modularized handlers
 */

import { XScraper } from './scraper.js';
import { RedditScraper } from './reddit-scraper.js';
import { TrendingAnalyzer } from './analyzer.js';
import { TargetManager } from './target-manager.js';
import { AITargetDiscovery } from './ai-discovery.js';
import { ComponentManager } from './component-manager.js';

// Handler imports
import { TwitterHandlers } from './handlers/twitter-handlers.js';
import { RedditHandlers } from './handlers/reddit-handlers.js';
import { BrowserHandlers } from './handlers/browser-handlers.js';
import { AIHandlers } from './handlers/ai-handlers.js';
import { TargetHandlers } from './handlers/target-handlers.js';
import { EnhancedHandlers } from './handlers/enhanced-handlers.js';
import { NotionHandlers } from './handlers/notion-handlers.js';

// Schema imports
import {
  GetCommentsSchema,
  GetKeyContributorsSchema,
  GetTrendingTopicsSchema,
  AnalyzeSentimentSchema,
  GetTrendingKeywordsSchema,
  AnalyzeKeywordsSchema
} from './schemas/twitter-schemas.js';

import {
  GetSubredditPostsSchema,
  GetRedditCommentsSchema,
  GetRedditTrendingSchema,
  SearchRedditSchema,
  AnalyzeRedditTrendsSchema,
  AnalyzeRedditCommentsSchema
} from './schemas/reddit-schemas.js';

import {
  NavigateSchema,
  ScreenshotSchema,
  ClickSchema,
  FillSchema,
  ScrollSchema,
  AnalyzePageVisuallySchema
} from './schemas/browser-schemas.js';

import {
  CreateChatSessionSchema,
  ChatWithAgentSchema,
  ChatShorthandSchema,
  GetSessionInfoSchema,
  ExportReportSchema,
  ListFabricPatternsSchema,
  ApplyFabricPatternSchema,
  ChainFabricPatternsSchema,
  CreateCustomPatternSchema,
  AnalyzeWithFabricSchema,
  BatchApplyPatternSchema
} from './schemas/ai-schemas.js';

import {
  ListTargetsSchema,
  GetTargetCategoriesSchema,
  AddInfluencerSchema,
  AddSubredditSchema,
  RemoveTargetSchema,
  GetTopTargetsSchema,
  BatchAnalyzeTargetsSchema,
  DiscoverNewTargetsSchema,
  GetRecentDiscoveriesSchema,
  PromoteDiscoveredTargetSchema,
  UpdateTargetRelevanceSchema
} from './schemas/target-schemas.js';

import {
  GetSystemHealthSchema,
  GetKPISnapshotSchema,
  GetBlockingStatsSchema,
  GetEnhancementStatsSchema,
  GenerateEnhancementProposalSchema,
  GetResearchSummarySchema,
  SendNotificationSchema,
  GetDashboardDataSchema,
  TestNotificationChannelSchema
} from './schemas/enhanced-schemas.js';

import {
  CreateNotionPageSchema,
  SearchNotionPagesSchema,
  GetNotionPageSchema,
  ListNotionDatabasesSchema,
  AddNotionDatabaseEntrySchema
} from './schemas/notion-schemas.js';

export interface ToolDefinition {
  name: string;
  description: string;
  inputSchema: any;
}

export interface ToolHandler {
  [key: string]: any;
}

export class ToolRegistry {
  private handlers: Map<string, ToolHandler> = new Map();
  private tools: ToolDefinition[] = [];

  constructor() {
    this.initializeComponents();
    this.registerAllTools();
  }

  private initializeComponents(): void {
    const scraper = new XScraper();
    const redditScraper = new RedditScraper();
    const analyzer = new TrendingAnalyzer();
    const targetManager = new TargetManager();
    const aiDiscovery = new AITargetDiscovery();
    const componentManager = ComponentManager.getInstance();

    // Initialize handler classes
    const twitterHandlers = new TwitterHandlers(scraper, analyzer);
    const redditHandlers = new RedditHandlers(redditScraper, analyzer);
    const browserHandlers = new BrowserHandlers(scraper, componentManager);
    const aiHandlers = new AIHandlers();
    const targetHandlers = new TargetHandlers(targetManager, aiDiscovery);
    const enhancedHandlers = new EnhancedHandlers(componentManager);
    const notionHandlers = new NotionHandlers();

    // Store handlers
    this.handlers.set('twitter', twitterHandlers);
    this.handlers.set('reddit', redditHandlers);
    this.handlers.set('browser', browserHandlers);
    this.handlers.set('ai', aiHandlers);
    this.handlers.set('target', targetHandlers);
    this.handlers.set('enhanced', enhancedHandlers);
    this.handlers.set('notion', notionHandlers);
  }

  private registerAllTools(): void {
    // Twitter/X Tools
    this.registerTool('get_comments', 'Scrape comments from a specific X/Twitter user', GetCommentsSchema);
    this.registerTool('get_key_contributors', 'Find key contributors for a specific topic or hashtag', GetKeyContributorsSchema);
    this.registerTool('get_trending_topics', 'Get current trending topics with optional category filter', GetTrendingTopicsSchema);
    this.registerTool('analyze_sentiment', 'Analyze sentiment of posts or comments', AnalyzeSentimentSchema);
    this.registerTool('get_trending_keywords', 'Get trending keywords from social media platforms', GetTrendingKeywordsSchema);
    this.registerTool('analyze_keywords', 'Analyze keyword performance across platforms', AnalyzeKeywordsSchema);

    // Browser Tools
    this.registerTool('navigate', 'Navigate browser to a specific URL', NavigateSchema);
    this.registerTool('screenshot', 'Take a screenshot of the current page or specific element', ScreenshotSchema);
    this.registerTool('click', 'Click on an element using CSS selector', ClickSchema);
    this.registerTool('fill', 'Fill an input field with text', FillSchema);
    this.registerTool('scroll', 'Scroll the page up or down', ScrollSchema);
    this.registerTool('analyze_page_visually', 'Analyze a webpage using visual scraping with OCR and blocking detection', AnalyzePageVisuallySchema);

    // Reddit Tools
    this.registerTool('get_subreddit_posts', 'Get posts from a specific subreddit', GetSubredditPostsSchema);
    this.registerTool('get_reddit_comments', 'Get comments from a specific Reddit post', GetRedditCommentsSchema);
    this.registerTool('get_reddit_trending', 'Get trending subreddits by category', GetRedditTrendingSchema);
    this.registerTool('search_reddit', 'Search Reddit posts and comments', SearchRedditSchema);
    this.registerTool('analyze_reddit_trends', 'Analyze trending topics on Reddit', AnalyzeRedditTrendsSchema);
    this.registerTool('analyze_reddit_comments', 'Analyze comments from a Reddit post', AnalyzeRedditCommentsSchema);

    // AI Agent Tools
    this.registerTool('create_chat_session', 'Create a new AI agent chat session', CreateChatSessionSchema);
    this.registerTool('chat_with_agent', 'Chat with the AI agent using a session ID', ChatWithAgentSchema);
    this.registerTool('@chat', 'Simple chat interface - your message to AI agent', ChatShorthandSchema);
    this.registerTool('get_session_info', 'Get information about a chat session', GetSessionInfoSchema);
    this.registerTool('export_report', 'Export chat session as a formatted report', ExportReportSchema);

    // Fabric Tools  
    this.registerTool('list_fabric_patterns', 'List available Fabric analysis patterns', ListFabricPatternsSchema);
    this.registerTool('apply_fabric_pattern', 'Apply a Fabric pattern to analyze content', ApplyFabricPatternSchema);
    this.registerTool('chain_fabric_patterns', 'Chain multiple Fabric patterns for complex analysis', ChainFabricPatternsSchema);
    this.registerTool('create_custom_pattern', 'Create a custom Fabric analysis pattern', CreateCustomPatternSchema);
    this.registerTool('analyze_with_fabric', 'Quick analysis using Fabric framework', AnalyzeWithFabricSchema);
    this.registerTool('batch_apply_pattern', 'Apply Fabric pattern to multiple content items', BatchApplyPatternSchema);

    // Target Management Tools
    this.registerTool('list_targets', 'List configured influencer and subreddit targets', ListTargetsSchema);
    this.registerTool('get_target_categories', 'Get available target categories', GetTargetCategoriesSchema);
    this.registerTool('add_influencer', 'Add a new Twitter influencer to targets', AddInfluencerSchema);
    this.registerTool('add_subreddit', 'Add a new subreddit to targets', AddSubredditSchema);
    this.registerTool('remove_target', 'Remove a target from the configuration', RemoveTargetSchema);
    this.registerTool('get_top_targets', 'Get top performing targets by metrics', GetTopTargetsSchema);
    this.registerTool('batch_analyze_targets', 'Analyze multiple target categories simultaneously', BatchAnalyzeTargetsSchema);
    this.registerTool('discover_new_targets', 'AI-powered discovery of new influential targets', DiscoverNewTargetsSchema);
    this.registerTool('get_recent_discoveries', 'Get recently discovered targets awaiting approval', GetRecentDiscoveriesSchema);
    this.registerTool('promote_discovered_target', 'Promote a discovered target from recent discoveries to main target list', PromoteDiscoveredTargetSchema);
    this.registerTool('update_target_relevance', 'Update relevance scores of existing targets with current metrics', UpdateTargetRelevanceSchema);

    // Enhanced Scraping Engine Tools
    this.registerTool('get_system_health', 'Get comprehensive system health metrics and status', GetSystemHealthSchema);
    this.registerTool('get_kpi_snapshot', 'Get current KPI metrics and performance data', GetKPISnapshotSchema);
    this.registerTool('get_blocking_stats', 'Get statistics on blocking events and mitigation effectiveness', GetBlockingStatsSchema);
    this.registerTool('get_enhancement_stats', 'Get statistics on automated enhancements and success rates', GetEnhancementStatsSchema);
    this.registerTool('generate_enhancement_proposal', 'Generate AI-powered enhancement proposal for system improvements', GenerateEnhancementProposalSchema);
    this.registerTool('get_research_summary', 'Get research agent findings and technology trends summary', GetResearchSummarySchema);
    this.registerTool('send_notification', 'Send notification through configured communication channels', SendNotificationSchema);
    this.registerTool('get_dashboard_data', 'Get real-time dashboard data for system monitoring', GetDashboardDataSchema);
    this.registerTool('test_notification_channel', 'Test a notification channel configuration', TestNotificationChannelSchema);

    // Notion Tools
    this.registerTool('create_notion_page', 'Create a new page in Notion workspace', CreateNotionPageSchema);
    this.registerTool('search_notion_pages', 'Search for pages in Notion workspace', SearchNotionPagesSchema);
    this.registerTool('get_notion_page', 'Get content from a specific Notion page', GetNotionPageSchema);
    this.registerTool('list_notion_databases', 'List all databases in Notion workspace', ListNotionDatabasesSchema);
    this.registerTool('add_notion_database_entry', 'Add a new entry to a Notion database', AddNotionDatabaseEntrySchema);
  }

  private registerTool(name: string, description: string, inputSchema: any): void {
    this.tools.push({
      name,
      description,
      inputSchema
    });
  }

  getTools(): ToolDefinition[] {
    return this.tools;
  }

  async executeHandler(toolName: string, args: unknown): Promise<any> {
    // Map tool names to handler categories and methods
    const toolMappings: Record<string, { category: string; method: string }> = {
      // Twitter tools
      'get_comments': { category: 'twitter', method: 'getComments' },
      'get_key_contributors': { category: 'twitter', method: 'getKeyContributors' },
      'get_trending_topics': { category: 'twitter', method: 'getTrendingTopics' },
      'analyze_sentiment': { category: 'twitter', method: 'analyzeSentiment' },
      'get_trending_keywords': { category: 'twitter', method: 'getTrendingKeywords' },
      'analyze_keywords': { category: 'twitter', method: 'analyzeKeywords' },

      // Browser tools
      'navigate': { category: 'browser', method: 'navigate' },
      'screenshot': { category: 'browser', method: 'screenshot' },
      'click': { category: 'browser', method: 'click' },
      'fill': { category: 'browser', method: 'fill' },
      'scroll': { category: 'browser', method: 'scroll' },
      'analyze_page_visually': { category: 'browser', method: 'analyzePageVisually' },

      // Reddit tools
      'get_subreddit_posts': { category: 'reddit', method: 'getSubredditPosts' },
      'get_reddit_comments': { category: 'reddit', method: 'getRedditComments' },
      'get_reddit_trending': { category: 'reddit', method: 'getRedditTrending' },
      'search_reddit': { category: 'reddit', method: 'searchReddit' },
      'analyze_reddit_trends': { category: 'reddit', method: 'analyzeRedditTrends' },
      'analyze_reddit_comments': { category: 'reddit', method: 'analyzeRedditComments' },

      // AI tools
      'create_chat_session': { category: 'ai', method: 'createChatSession' },
      'chat_with_agent': { category: 'ai', method: 'chatWithAgent' },
      '@chat': { category: 'ai', method: 'chatShorthand' },
      'get_session_info': { category: 'ai', method: 'getSessionInfo' },
      'export_report': { category: 'ai', method: 'exportReport' },
      'list_fabric_patterns': { category: 'ai', method: 'listFabricPatterns' },
      'apply_fabric_pattern': { category: 'ai', method: 'applyFabricPattern' },
      'chain_fabric_patterns': { category: 'ai', method: 'chainFabricPatterns' },
      'create_custom_pattern': { category: 'ai', method: 'createCustomPattern' },
      'analyze_with_fabric': { category: 'ai', method: 'analyzeWithFabric' },
      'batch_apply_pattern': { category: 'ai', method: 'batchApplyPattern' },

      // Target tools
      'list_targets': { category: 'target', method: 'listTargets' },
      'get_target_categories': { category: 'target', method: 'getTargetCategories' },
      'add_influencer': { category: 'target', method: 'addInfluencer' },
      'add_subreddit': { category: 'target', method: 'addSubreddit' },
      'remove_target': { category: 'target', method: 'removeTarget' },
      'get_top_targets': { category: 'target', method: 'getTopTargets' },
      'batch_analyze_targets': { category: 'target', method: 'batchAnalyzeTargets' },
      'discover_new_targets': { category: 'target', method: 'discoverNewTargets' },
      'get_recent_discoveries': { category: 'target', method: 'getRecentDiscoveries' },
      'promote_discovered_target': { category: 'target', method: 'promoteDiscoveredTarget' },
      'update_target_relevance': { category: 'target', method: 'updateTargetRelevance' },

      // Enhanced tools
      'get_system_health': { category: 'enhanced', method: 'getSystemHealth' },
      'get_kpi_snapshot': { category: 'enhanced', method: 'getKPISnapshot' },
      'get_blocking_stats': { category: 'enhanced', method: 'getBlockingStats' },
      'get_enhancement_stats': { category: 'enhanced', method: 'getEnhancementStats' },
      'generate_enhancement_proposal': { category: 'enhanced', method: 'generateEnhancementProposal' },
      'get_research_summary': { category: 'enhanced', method: 'getResearchSummary' },
      'send_notification': { category: 'enhanced', method: 'sendNotification' },
      'get_dashboard_data': { category: 'enhanced', method: 'getDashboardData' },
      'test_notification_channel': { category: 'enhanced', method: 'testNotificationChannel' },

      // Notion tools
      'create_notion_page': { category: 'notion', method: 'createPage' },
      'search_notion_pages': { category: 'notion', method: 'searchPages' },
      'get_notion_page': { category: 'notion', method: 'getPage' },
      'list_notion_databases': { category: 'notion', method: 'listDatabases' },
      'add_notion_database_entry': { category: 'notion', method: 'addDatabaseEntry' }
    };

    const mapping = toolMappings[toolName];
    if (!mapping) {
      throw new Error(`Unknown tool: ${toolName}`);
    }

    const handler = this.handlers.get(mapping.category);
    if (!handler) {
      throw new Error(`Handler not found for category: ${mapping.category}`);
    }

    const method = handler[mapping.method];
    if (typeof method !== 'function') {
      throw new Error(`Method ${mapping.method} not found in ${mapping.category} handler`);
    }

    return await method.call(handler, args);
  }
}