// Prometheus Metrics Integration for Real-Time Scraping Dashboard
// Provides comprehensive monitoring of scraping operations with visual dashboards

import { register, Counter, Gauge, Histogram, collectDefaultMetrics } from 'prom-client';
import { createWriteStream, WriteStream } from 'fs';

// Enable default Node.js metrics
collectDefaultMetrics({ register });

export class PrometheusScrapingMetrics {
  // Request Metrics
  private requestsTotal = new Counter({
    name: 'scraper_requests_total',
    help: 'Total number of scraping requests made',
    labelNames: ['domain', 'target', 'status', 'safety_mode']
  });

  private requestDuration = new Histogram({
    name: 'scraper_request_duration_seconds',
    help: 'Duration of scraping requests in seconds',
    labelNames: ['domain', 'target', 'safety_mode'],
    buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60]
  });

  // Safety Metrics
  private rateLimitHits = new Counter({
    name: 'scraper_rate_limit_hits_total',
    help: 'Number of times rate limiting was triggered',
    labelNames: ['domain', 'limit_type']
  });

  private circuitBreakerState = new Gauge({
    name: 'scraper_circuit_breaker_state',
    help: 'Circuit breaker state (0=closed, 1=open, 2=half-open)',
    labelNames: ['domain']
  });

  private blockedRequests = new Counter({
    name: 'scraper_blocked_requests_total',
    help: 'Number of requests blocked by target sites',
    labelNames: ['domain', 'block_reason']
  });

  // Browser Metrics
  private activeBrowserSessions = new Gauge({
    name: 'scraper_browser_sessions_active',
    help: 'Number of active browser sessions'
  });

  private browserSessionDuration = new Histogram({
    name: 'scraper_browser_session_duration_seconds',
    help: 'Duration of browser sessions in seconds',
    buckets: [60, 300, 600, 1800, 3600, 7200]
  });

  // Cache Metrics
  private cacheHits = new Counter({
    name: 'scraper_cache_hits_total',
    help: 'Number of cache hits'
  });

  private cacheMisses = new Counter({
    name: 'scraper_cache_misses_total',
    help: 'Number of cache misses'
  });

  private cacheSize = new Gauge({
    name: 'scraper_cache_entries',
    help: 'Number of entries in cache'
  });

  // Data Quality Metrics
  private dataExtractionSuccess = new Counter({
    name: 'scraper_data_extraction_success_total',
    help: 'Successful data extractions',
    labelNames: ['domain', 'data_type']
  });

  private dataExtractionErrors = new Counter({
    name: 'scraper_data_extraction_errors_total',
    help: 'Failed data extractions',
    labelNames: ['domain', 'data_type', 'error_type']
  });

  // AI Analysis Metrics
  private aiAnalysisRequests = new Counter({
    name: 'scraper_ai_analysis_requests_total',
    help: 'AI analysis requests made',
    labelNames: ['analysis_type', 'model']
  });

  private aiAnalysisDuration = new Histogram({
    name: 'scraper_ai_analysis_duration_seconds',
    help: 'Duration of AI analysis operations',
    labelNames: ['analysis_type'],
    buckets: [1, 5, 10, 30, 60, 120]
  });

  private logStream: WriteStream;

  constructor(logPath: string = './logs/prometheus-metrics.log') {
    this.logStream = createWriteStream(logPath, { flags: 'a' });
    
    // Register all metrics
    register.registerMetric(this.requestsTotal);
    register.registerMetric(this.requestDuration);
    register.registerMetric(this.rateLimitHits);
    register.registerMetric(this.circuitBreakerState);
    register.registerMetric(this.blockedRequests);
    register.registerMetric(this.activeBrowserSessions);
    register.registerMetric(this.browserSessionDuration);
    register.registerMetric(this.cacheHits);
    register.registerMetric(this.cacheMisses);
    register.registerMetric(this.cacheSize);
    register.registerMetric(this.dataExtractionSuccess);
    register.registerMetric(this.dataExtractionErrors);
    register.registerMetric(this.aiAnalysisRequests);
    register.registerMetric(this.aiAnalysisDuration);
  }

  // Request Tracking
  recordRequest(domain: string, target: string, success: boolean, duration: number, safetyMode: string) {
    const status = success ? 'success' : 'failure';
    this.requestsTotal.inc({ domain, target, status, safety_mode: safetyMode });
    this.requestDuration.observe({ domain, target, safety_mode: safetyMode }, duration / 1000);
    
    this.logMetric('REQUEST', { domain, target, status, duration, safetyMode });
  }

  // Safety Tracking
  recordRateLimit(domain: string, limitType: 'hourly' | 'daily' | 'concurrent') {
    this.rateLimitHits.inc({ domain, limit_type: limitType });
    this.logMetric('RATE_LIMIT', { domain, limitType });
  }

  updateCircuitBreakerState(domain: string, state: 'closed' | 'open' | 'half-open') {
    const stateValue = state === 'closed' ? 0 : state === 'open' ? 1 : 2;
    this.circuitBreakerState.set({ domain }, stateValue);
    this.logMetric('CIRCUIT_BREAKER', { domain, state });
  }

  recordBlockedRequest(domain: string, reason: string) {
    this.blockedRequests.inc({ domain, block_reason: reason });
    this.logMetric('BLOCKED', { domain, reason });
  }

  // Browser Session Tracking
  updateActiveBrowserSessions(count: number) {
    this.activeBrowserSessions.set(count);
  }

  recordBrowserSessionDuration(duration: number) {
    this.browserSessionDuration.observe(duration / 1000);
  }

  // Cache Tracking
  recordCacheHit() {
    this.cacheHits.inc();
  }

  recordCacheMiss() {
    this.cacheMisses.inc();
  }

  updateCacheSize(size: number) {
    this.cacheSize.set(size);
  }

  // Data Quality Tracking
  recordDataExtraction(domain: string, dataType: string, success: boolean, errorType?: string) {
    if (success) {
      this.dataExtractionSuccess.inc({ domain, data_type: dataType });
    } else {
      this.dataExtractionErrors.inc({ 
        domain, 
        data_type: dataType, 
        error_type: errorType || 'unknown' 
      });
    }
    
    this.logMetric('DATA_EXTRACTION', { domain, dataType, success, errorType });
  }

  // AI Analysis Tracking
  recordAIAnalysis(analysisType: string, model: string, duration: number) {
    this.aiAnalysisRequests.inc({ analysis_type: analysisType, model });
    this.aiAnalysisDuration.observe({ analysis_type: analysisType }, duration / 1000);
    
    this.logMetric('AI_ANALYSIS', { analysisType, model, duration });
  }

  // Get current metrics snapshot
  async getMetricsSnapshot(): Promise<{
    requests: unknown;
    safety: unknown;
    browser: unknown;
    cache: unknown;
    dataQuality: unknown;
    ai: unknown;
  }> {
    const metrics = await register.getMetricsAsJSON();
    
    return {
      requests: {
        total: this.getMetricValue(metrics, 'scraper_requests_total'),
        duration: this.getMetricValue(metrics, 'scraper_request_duration_seconds'),
        rate: this.calculateRequestRate()
      },
      safety: {
        rateLimits: this.getMetricValue(metrics, 'scraper_rate_limit_hits_total'),
        circuitBreakers: this.getMetricValue(metrics, 'scraper_circuit_breaker_state'),
        blockedRequests: this.getMetricValue(metrics, 'scraper_blocked_requests_total')
      },
      browser: {
        activeSessions: this.getMetricValue(metrics, 'scraper_browser_sessions_active'),
        sessionDuration: this.getMetricValue(metrics, 'scraper_browser_session_duration_seconds')
      },
      cache: {
        hits: this.getMetricValue(metrics, 'scraper_cache_hits_total'),
        misses: this.getMetricValue(metrics, 'scraper_cache_misses_total'),
        size: this.getMetricValue(metrics, 'scraper_cache_entries'),
        hitRate: this.calculateCacheHitRate()
      },
      dataQuality: {
        successful: this.getMetricValue(metrics, 'scraper_data_extraction_success_total'),
        errors: this.getMetricValue(metrics, 'scraper_data_extraction_errors_total'),
        successRate: this.calculateDataSuccessRate()
      },
      ai: {
        requests: this.getMetricValue(metrics, 'scraper_ai_analysis_requests_total'),
        duration: this.getMetricValue(metrics, 'scraper_ai_analysis_duration_seconds')
      }
    };
  }

  // Generate Prometheus metrics endpoint data
  async getPrometheusMetrics(): Promise<string> {
    return register.metrics();
  }

  // Generate dashboard screenshot data
  async generateDashboardScreenshot(): Promise<{
    timestamp: number;
    metrics: any;
    alerts: string[];
    recommendations: string[];
  }> {
    const metrics = await this.getMetricsSnapshot();
    const alerts = this.generateAlerts(metrics);
    const recommendations = this.generateRecommendations(metrics);

    return {
      timestamp: Date.now(),
      metrics,
      alerts,
      recommendations
    };
  }

  // Helper methods
  private getMetricValue(metrics: unknown[], metricName: string): unknown {
    const metric = (metrics as Array<{ name: string; values: unknown }>).find(m => m.name === metricName);
    return metric ? metric.values : null;
  }

  private calculateRequestRate(): number {
    // Calculate requests per minute over last 5 minutes
    // Implementation would track request timestamps
    return 0; // Placeholder
  }

  private calculateCacheHitRate(): number {
    // This would need to be implemented with proper metric value extraction
    // For now, return a placeholder
    return 0;
  }

  private calculateDataSuccessRate(): number {
    // Calculate success rate across all data extraction attempts
    return 0; // Placeholder - implement based on metric values
  }

  private generateAlerts(metrics: Record<string, unknown>): string[] {
    const alerts: string[] = [];
    
    // High error rate alert
    const successRate = (metrics.dataQuality as { successRate?: number })?.successRate;
    if (typeof successRate === 'number' && successRate < 80) {
      alerts.push(`Low data extraction success rate: ${successRate}%`);
    }
    
    // Circuit breaker alert
    const circuitBreakers = (metrics.safety as { circuitBreakers?: Array<{ value: number }> })?.circuitBreakers;
    if (circuitBreakers?.some(cb => cb.value === 1)) {
      alerts.push('Circuit breakers are open - scraping paused');
    }
    
    // High blocking rate
    const blockedRequests = (metrics.safety as { blockedRequests?: Array<{ value: number }> })?.blockedRequests;
    const blockedCount = blockedRequests?.reduce((sum: number, br) => sum + br.value, 0) ?? 0;
    if (blockedCount > 10) {
      alerts.push(`High number of blocked requests: ${blockedCount}`);
    }
    
    return alerts;
  }

  private generateRecommendations(metrics: Record<string, unknown>): string[] {
    const recommendations: string[] = [];
    
    // Cache optimization
    const hitRate = (metrics.cache as { hitRate?: number })?.hitRate;
    if (typeof hitRate === 'number' && hitRate < 60) {
      recommendations.push('Consider increasing cache TTL to improve hit rate');
    }
    
    // Rate limiting
    const rateLimits = (metrics.safety as { rateLimits?: unknown[] })?.rateLimits;
    if (rateLimits && rateLimits.length > 0) {
      recommendations.push('Consider switching to STRICT safety mode to reduce rate limiting');
    }
    
    // Browser sessions
    const activeSessions = (metrics.browser as { activeSessions?: number })?.activeSessions;
    if (typeof activeSessions === 'number' && activeSessions > 5) {
      recommendations.push('High number of browser sessions - consider session pooling');
    }
    
    return recommendations;
  }

  private logMetric(type: string, data: unknown): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      type,
      data
    };
    
    this.logStream.write(JSON.stringify(logEntry) + '\n');
  }

  cleanup() {
    this.logStream.end();
  }
}

// Grafana Dashboard Configuration
export const GRAFANA_DASHBOARD_CONFIG = {
  dashboard: {
    title: "MCP X/Reddit Scraper Monitoring",
    panels: [
      {
        title: "Request Rate",
        type: "graph",
        targets: [
          {
            expr: "rate(scraper_requests_total[5m])",
            legendFormat: "{{domain}} - {{status}}"
          }
        ]
      },
      {
        title: "Response Times",
        type: "graph", 
        targets: [
          {
            expr: "histogram_quantile(0.95, scraper_request_duration_seconds)",
            legendFormat: "95th percentile"
          }
        ]
      },
      {
        title: "Circuit Breaker States",
        type: "stat",
        targets: [
          {
            expr: "scraper_circuit_breaker_state",
            legendFormat: "{{domain}}"
          }
        ]
      },
      {
        title: "Cache Hit Rate",
        type: "gauge",
        targets: [
          {
            expr: "rate(scraper_cache_hits_total[5m]) / (rate(scraper_cache_hits_total[5m]) + rate(scraper_cache_misses_total[5m])) * 100"
          }
        ]
      },
      {
        title: "AI Analysis Performance", 
        type: "graph",
        targets: [
          {
            expr: "rate(scraper_ai_analysis_requests_total[5m])",
            legendFormat: "{{analysis_type}}"
          }
        ]
      }
    ]
  }
};