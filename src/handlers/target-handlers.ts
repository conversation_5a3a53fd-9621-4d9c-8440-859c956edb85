/**
 * Target Management Tool Handlers
 * Handler implementations for target management operations
 */

import { TargetManager } from '../target-manager.js';
import { AITargetDiscovery } from '../ai-discovery.js';
import {
  ListTargetsSchema,
  GetTargetCategoriesSchema,
  AddInfluencerSchema,
  AddSubredditSchema,
  RemoveTargetSchema,
  GetTopTargetsSchema,
  BatchAnalyzeTargetsSchema,
  DiscoverNewTargetsSchema,
  GetRecentDiscoveriesSchema,
  PromoteDiscoveredTargetSchema,
  UpdateTargetRelevanceSchema
} from '../schemas/target-schemas.js';

export class TargetHandlers {
  constructor(
    private targetManager: TargetManager,
    private aiDiscovery: AITargetDiscovery
  ) {}

  async listTargets(args: unknown) {
    const params = ListTargetsSchema.parse(args);
    const targets = this.targetManager.getTopTargets(10);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(targets, null, 2),
        },
      ],
    };
  }

  async getTargetCategories(args: unknown) {
    GetTargetCategoriesSchema.parse(args);
    const categories = this.targetManager.getCategories();
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(categories, null, 2),
        },
      ],
    };
  }

  async addInfluencer(args: unknown) {
    const params = AddInfluencerSchema.parse(args);
    try {
      this.targetManager.addInfluencer(
        params.category,
        {
          username: params.username ?? '',
          followers: (params.followers ?? 0).toString(),
          relevance_score: params.relevance_score ?? 0,
          last_verified: new Date().toISOString().split('T')[0],
          topics: params.topics
        }
      );
      return {
        content: [
          {
            type: 'text',
            text: `Added influencer @${params.username ?? ''} to ${params.category}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Failed to add influencer: ${error}`,
          },
        ],
      };
    }
  }

  async addSubreddit(args: unknown) {
    const params = AddSubredditSchema.parse(args);
    try {
      this.targetManager.addSubreddit(
        params.category,
        {
          name: params.name ?? '',
          subscribers: (params.subscribers ?? 0).toString(),
          activity_score: params.activity_score ?? 0,
          last_verified: new Date().toISOString().split('T')[0]
        }
      );
      return {
        content: [
          {
            type: 'text',
            text: `Added subreddit r/${params.name ?? ''} to ${params.category}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Failed to add subreddit: ${error}`,
          },
        ],
      };
    }
  }

  async removeTarget(args: unknown) {
    const params = RemoveTargetSchema.parse(args);
    const success = this.targetManager.removeInfluencer(
      params.category,
      params.identifier
    );
    return {
      content: [
        {
          type: 'text',
          text: success ? 
            `Removed ${params.type} target ${params.identifier} from ${params.category}` :
            `Failed to remove ${params.type} target ${params.identifier}`,
        },
      ],
    };
  }

  async getTopTargets(args: unknown) {
    const params = GetTopTargetsSchema.parse(args);
    const topTargets = this.targetManager.getTopTargets(params.limit);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(topTargets, null, 2),
        },
      ],
    };
  }

  async batchAnalyzeTargets(args: unknown) {
    const params = BatchAnalyzeTargetsSchema.parse(args);
    const analysis = this.targetManager.getBatchTargets(
      params.twitter_categories,
      params.reddit_categories
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(analysis, null, 2),
        },
      ],
    };
  }

  async discoverNewTargets(args: unknown) {
    const params = DiscoverNewTargetsSchema.parse(args);
    const discoveries = await this.aiDiscovery.discoverTwitterInfluencers(
      params.category,
      params.keywords ?? [],
      params.limit
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(discoveries, null, 2),
        },
      ],
    };
  }

  async getRecentDiscoveries(args: unknown) {
    GetRecentDiscoveriesSchema.parse(args);
    const discoveries = this.targetManager.getRecentDiscoveries();
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(discoveries, null, 2),
        },
      ],
    };
  }

  async promoteDiscoveredTarget(args: unknown) {
    const params = PromoteDiscoveredTargetSchema.parse(args);
    const success = this.targetManager.promoteDiscoveredTarget(
      params.type,
      params.category,
      params.identifier
    );
    return {
      content: [
        {
          type: 'text',
          text: success ? 
            `Promoted discovered ${params.type} target ${params.identifier} to main targets` :
            `Failed to promote ${params.type} target ${params.identifier}`,
        },
      ],
    };
  }

  async updateTargetRelevance(args: unknown) {
    const params = UpdateTargetRelevanceSchema.parse(args);
    this.targetManager.updateDiscoveryKeywords(
      params.category,
      [params.identifier]
    );
    const newScore = 'Updated';
    return {
      content: [
        {
          type: 'text',
          text: `Updated relevance score for ${params.identifier}: ${newScore}`,
        },
      ],
    };
  }
}