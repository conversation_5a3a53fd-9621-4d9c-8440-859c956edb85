/**
 * Twitter/X Scraping Tool Handlers
 * Handler implementations for Twitter scraping operations
 */

import { XScraper } from '../scraper.js';
import { TrendingAnalyzer } from '../analyzer.js';
import {
  GetCommentsSchema,
  GetKeyContributorsSchema,
  GetTrendingTopicsSchema,
  AnalyzeSentimentSchema,
  GetTrendingKeywordsSchema,
  AnalyzeKeywordsSchema
} from '../schemas/twitter-schemas.js';

export class TwitterHandlers {
  constructor(
    private scraper: XScraper,
    private analyzer: TrendingAnalyzer
  ) {}

  async getComments(args: unknown) {
    const params = GetCommentsSchema.parse(args);
    const comments = await this.scraper.getComments(
      params.username,
      params.limit,
      params.includeReplies
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(comments, null, 2),
        },
      ],
    };
  }

  async getKeyContributors(args: unknown) {
    const params = GetKeyContributorsSchema.parse(args);
    const contributors = await this.scraper.getKeyContributors(
      params.topic,
      params.limit
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(contributors, null, 2),
        },
      ],
    };
  }

  async getTrendingTopics(args: unknown) {
    const params = GetTrendingTopicsSchema.parse(args);
    const topics = await this.scraper.getTrendingTopics(
      params.category,
      params.location
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(topics, null, 2),
        },
      ],
    };
  }

  async analyzeSentiment(args: unknown) {
    const params = AnalyzeSentimentSchema.parse(args);
    const results = await this.analyzer.analyzeSentiment(
      params.posts,
      params.granularity
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(results, null, 2),
        },
      ],
    };
  }

  async getTrendingKeywords(args: unknown) {
    const params = GetTrendingKeywordsSchema.parse(args);
    const keywords = await this.analyzer.getTrendingKeywords(
      params.sources,
      params.limit
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(keywords, null, 2),
        },
      ],
    };
  }

  async analyzeKeywords(args: unknown) {
    const params = AnalyzeKeywordsSchema.parse(args);
    const analysis = await this.analyzer.analyzeKeywords(
      params.keywords,
      params.sources
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(analysis, null, 2),
        },
      ],
    };
  }
}