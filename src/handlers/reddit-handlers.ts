/**
 * Reddit Scraping Tool Handlers  
 * Handler implementations for Reddit scraping operations
 */

import { RedditScraper } from '../reddit-scraper.js';
import { TrendingAnalyzer } from '../analyzer.js';
import {
  GetSubredditPostsSchema,
  GetRedditCommentsSchema,
  GetRedditTrendingSchema,
  SearchRedditSchema,
  AnalyzeRedditTrendsSchema,
  AnalyzeRedditCommentsSchema
} from '../schemas/reddit-schemas.js';

export class RedditHandlers {
  constructor(
    private redditScraper: RedditScraper,
    private analyzer: TrendingAnalyzer
  ) {}

  async getSubredditPosts(args: unknown) {
    const params = GetSubredditPostsSchema.parse(args);
    const posts = await this.redditScraper.getSubredditPosts(
      params.subreddit,
      params.sort,
      params.limit
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(posts, null, 2),
        },
      ],
    };
  }

  async getRedditComments(args: unknown) {
    const params = GetRedditCommentsSchema.parse(args);
    const comments = await this.redditScraper.getPostComments(
      params.postUrl,
      params.limit,
      params.sortBy
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(comments, null, 2),
        },
      ],
    };
  }

  async getRedditTrending(args: unknown) {
    const params = GetRedditTrendingSchema.parse(args);
    const trending = await this.redditScraper.getTrendingSubreddits(
      params.category,
      params.limit
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(trending, null, 2),
        },
      ],
    };
  }

  async searchReddit(args: unknown) {
    const params = SearchRedditSchema.parse(args);
    const results = await this.redditScraper.searchReddit(
      params.query,
      params.subreddit,
      params.sort,
      params.timeframe,
      params.limit
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(results, null, 2),
        },
      ],
    };
  }

  async analyzeRedditTrends(args: unknown) {
    const params = AnalyzeRedditTrendsSchema.parse(args);
    const analysis = await this.analyzer.analyzeSubredditTrends(
      params.subreddit,
      params.timeframe
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(analysis, null, 2),
        },
      ],
    };
  }

  async analyzeRedditComments(args: unknown) {
    const params = AnalyzeRedditCommentsSchema.parse(args);
    const analysis = await this.analyzer.analyzeRedditComments(
      params.postUrl
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(analysis, null, 2),
        },
      ],
    };
  }
}