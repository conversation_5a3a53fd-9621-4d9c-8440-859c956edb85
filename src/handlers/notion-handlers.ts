/**
 * Notion Handlers - MCP tools for Notion integration
 * Provides comprehensive Notion API capabilities
 */

import { Client } from '@notionhq/client';

export class NotionHandlers {
  private notion: Client;

  constructor() {
    const apiKey = process.env.NOTION_API_KEY;
    if (!apiKey) {
      throw new Error('NOTION_API_KEY environment variable is required');
    }
    
    this.notion = new Client({
      auth: apiKey,
    });
  }

  /**
   * Create a new Notion page
   */
  async createPage(args: {
    parent_id: string;
    title: string;
    content?: string;
    properties?: Record<string, any>;
  }) {
    try {
      const { parent_id, title, content, properties = {} } = args;

      // Prepare page properties
      const pageProperties: any = {
        title: {
          title: [
            {
              text: {
                content: title,
              },
            },
          ],
        },
        ...properties,
      };

      // Prepare page content blocks
      const children: any[] = [];
      
      if (content) {
        // Split content into paragraphs and create blocks
        const paragraphs = content.split('\n\n');
        for (const paragraph of paragraphs) {
          if (paragraph.trim()) {
            if (paragraph.startsWith('# ')) {
              // Heading 1
              children.push({
                object: 'block',
                type: 'heading_1',
                heading_1: {
                  rich_text: [
                    {
                      type: 'text',
                      text: {
                        content: paragraph.replace('# ', ''),
                      },
                    },
                  ],
                },
              });
            } else if (paragraph.startsWith('## ')) {
              // Heading 2
              children.push({
                object: 'block',
                type: 'heading_2',
                heading_2: {
                  rich_text: [
                    {
                      type: 'text',
                      text: {
                        content: paragraph.replace('## ', ''),
                      },
                    },
                  ],
                },
              });
            } else if (paragraph.startsWith('### ')) {
              // Heading 3
              children.push({
                object: 'block',
                type: 'heading_3',
                heading_3: {
                  rich_text: [
                    {
                      type: 'text',
                      text: {
                        content: paragraph.replace('### ', ''),
                      },
                    },
                  ],
                },
              });
            } else {
              // Regular paragraph
              children.push({
                object: 'block',
                type: 'paragraph',
                paragraph: {
                  rich_text: [
                    {
                      type: 'text',
                      text: {
                        content: paragraph,
                      },
                    },
                  ],
                },
              });
            }
          }
        }
      }

      const response = await this.notion.pages.create({
        parent: {
          type: 'page_id',
          page_id: parent_id,
        },
        properties: pageProperties,
        children: children.length > 0 ? children : undefined,
      });

      return {
        success: true,
        page_id: response.id,
        url: (response as any).url || `https://notion.so/${response.id.replace(/-/g, '')}`,
        title: title,
        message: `Successfully created Notion page: ${title}`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        message: `Failed to create Notion page: ${error.message}`,
      };
    }
  }

  /**
   * Search for pages in Notion
   */
  async searchPages(args: { query: string; max_results?: number }) {
    try {
      const { query, max_results = 10 } = args;

      const response = await this.notion.search({
        query,
        page_size: Math.min(max_results, 100),
        filter: {
          property: 'object',
          value: 'page',
        },
      });

      const pages = response.results.map((page: any) => ({
        id: page.id,
        title: this.extractTitle(page),
        url: page.url,
        created_time: page.created_time,
        last_edited_time: page.last_edited_time,
      }));

      return {
        success: true,
        pages,
        total_results: pages.length,
        message: `Found ${pages.length} pages matching "${query}"`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        message: `Failed to search pages: ${error.message}`,
      };
    }
  }

  /**
   * Get page content
   */
  async getPage(args: { page_id: string }) {
    try {
      const { page_id } = args;

      const page = await this.notion.pages.retrieve({ page_id });
      const blocks = await this.notion.blocks.children.list({ block_id: page_id });

      return {
        success: true,
        page: {
          id: page.id,
          title: this.extractTitle(page),
          url: (page as any).url || `https://notion.so/${page.id.replace(/-/g, '')}`,
          created_time: (page as any).created_time,
          last_edited_time: (page as any).last_edited_time,
          properties: (page as any).properties,
        },
        content: this.extractBlockContent(blocks.results),
        message: `Successfully retrieved page content`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        message: `Failed to get page: ${error.message}`,
      };
    }
  }

  /**
   * List databases
   */
  async listDatabases(args: { max_results?: number } = {}) {
    try {
      const { max_results = 10 } = args;

      const response = await this.notion.search({
        page_size: Math.min(max_results, 100),
        filter: {
          property: 'object',
          value: 'database',
        },
      });

      const databases = response.results.map((db: any) => ({
        id: db.id,
        title: this.extractTitle(db),
        url: db.url,
        created_time: db.created_time,
        last_edited_time: db.last_edited_time,
      }));

      return {
        success: true,
        databases,
        total_results: databases.length,
        message: `Found ${databases.length} databases`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        message: `Failed to list databases: ${error.message}`,
      };
    }
  }

  /**
   * Add entry to database
   */
  async addDatabaseEntry(args: {
    database_id: string;
    properties: Record<string, any>;
  }) {
    try {
      const { database_id, properties } = args;

      const response = await this.notion.pages.create({
        parent: {
          type: 'database_id',
          database_id,
        },
        properties,
      });

      return {
        success: true,
        page_id: response.id,
        url: (response as any).url || `https://notion.so/${response.id.replace(/-/g, '')}`,
        message: `Successfully added entry to database`,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        message: `Failed to add database entry: ${error.message}`,
      };
    }
  }

  // Helper methods
  private extractTitle(page: any): string {
    if (page.properties?.title?.title?.[0]?.text?.content) {
      return page.properties.title.title[0].text.content;
    }
    if (page.properties?.Name?.title?.[0]?.text?.content) {
      return page.properties.Name.title[0].text.content;
    }
    return 'Untitled';
  }

  private extractBlockContent(blocks: any[]): string {
    return blocks
      .map((block) => {
        switch (block.type) {
          case 'paragraph':
            return block.paragraph?.rich_text?.map((text: any) => text.plain_text).join('') || '';
          case 'heading_1':
            return `# ${block.heading_1?.rich_text?.map((text: any) => text.plain_text).join('') || ''}`;
          case 'heading_2':
            return `## ${block.heading_2?.rich_text?.map((text: any) => text.plain_text).join('') || ''}`;
          case 'heading_3':
            return `### ${block.heading_3?.rich_text?.map((text: any) => text.plain_text).join('') || ''}`;
          default:
            return '';
        }
      })
      .filter(Boolean)
      .join('\n\n');
  }
}
