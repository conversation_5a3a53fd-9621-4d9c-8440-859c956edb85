/**
 * AI Agent and Fabric Tool Handlers
 * Handler implementations for AI agent and Fabric framework operations
 */

import { getAgentBridge } from '../agent-bridge.js';
import { getFabricBridge } from '../fabric-bridge.js';
import {
  CreateChatSessionSchema,
  ChatWithAgentSchema,
  ChatShorthandSchema,
  GetSessionInfoSchema,
  ExportReportSchema,
  ListFabricPatternsSchema,
  ApplyFabricPatternSchema,
  ChainFabricPatternsSchema,
  CreateCustomPatternSchema,
  AnalyzeWithFabricSchema,
  BatchApplyPatternSchema
} from '../schemas/ai-schemas.js';

export class AIHandlers {
  private agentBridge = getAgentBridge();
  private fabricBridge = getFabricBridge();
  private defaultChatSession: string | null = null;

  // AI Agent handlers
  async createChatSession(args: unknown) {
    CreateChatSessionSchema.parse(args);
    const sessionId = await this.agentBridge.createChatSession();
    return {
      content: [
        {
          type: 'text',
          text: `Created chat session: ${sessionId}`,
        },
      ],
    };
  }

  async chatWithAgent(args: unknown) {
    const params = ChatWithAgentSchema.parse(args);
    
    let sessionId = params.sessionId;
    if (!sessionId) {
      sessionId = await this.agentBridge.createChatSession();
      console.log(`📋 Created new chat session: ${sessionId}`);
    }

    const response = await this.agentBridge.chat(sessionId, params.message);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2),
        },
      ],
    };
  }

  async chatShorthand(args: unknown) {
    const params = ChatShorthandSchema.parse(args);
    
    if (!this.defaultChatSession) {
      this.defaultChatSession = await this.agentBridge.createChatSession();
      console.log(`📋 Created default chat session: ${this.defaultChatSession}`);
    }

    const response = await this.agentBridge.chat(this.defaultChatSession, params.message);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(response, null, 2),
        },
      ],
    };
  }

  async getSessionInfo(args: unknown) {
    const params = GetSessionInfoSchema.parse(args);
    const info = await this.agentBridge.getSessionInfo(params.sessionId);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(info, null, 2),
        },
      ],
    };
  }

  async exportReport(args: unknown) {
    const params = ExportReportSchema.parse(args);
    const report = await this.agentBridge.exportReport(params.sessionId, params.format);
    return {
      content: [
        {
          type: 'text',
          text: report,
        },
      ],
    };
  }

  // Fabric handlers
  async listFabricPatterns(args: unknown) {
    const params = ListFabricPatternsSchema.parse(args);
    const patterns = await this.fabricBridge.listPatterns(params.category);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(patterns, null, 2),
        },
      ],
    };
  }

  async applyFabricPattern(args: unknown) {
    const params = ApplyFabricPatternSchema.parse(args);
    const result = await this.fabricBridge.applyPattern(
      params.patternName,
      params.content,
      params.model,
      params.temperature
    );
    return {
      content: [
        {
          type: 'text',
          text: result,
        },
      ],
    };
  }

  async chainFabricPatterns(args: unknown) {
    const params = ChainFabricPatternsSchema.parse(args);
    const result = await this.fabricBridge.chainPatterns(
      params.patterns,
      params.content,
      params.model
    );
    return {
      content: [
        {
          type: 'text',
          text: result,
        },
      ],
    };
  }

  async createCustomPattern(args: unknown) {
    const params = CreateCustomPatternSchema.parse(args);
    const success = await this.fabricBridge.createCustomPattern(
      params.name,
      params.systemPrompt,
      params.description
    );
    return {
      content: [
        {
          type: 'text',
          text: success ? 
            `Custom pattern '${params.name}' created successfully` :
            `Failed to create custom pattern '${params.name}'`,
        },
      ],
    };
  }

  async analyzeWithFabric(args: unknown) {
    const params = AnalyzeWithFabricSchema.parse(args);
    let analysis;
    
    switch (params.analysisType) {
      case 'insights':
        analysis = await this.fabricBridge.extractInsights(params.content);
        break;
      case 'summary':
        analysis = await this.fabricBridge.summarizeContent(params.content);
        break;
      case 'claims':
        analysis = await this.fabricBridge.analyzeContent(params.content);
        break;
      default:
        analysis = await this.fabricBridge.analyzeContent(params.content);
    }
    
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(analysis, null, 2),
        },
      ],
    };
  }

  async batchApplyPattern(args: unknown) {
    const params = BatchApplyPatternSchema.parse(args);
    const results = await this.fabricBridge.applyPatternToMultipleInputs(
      params.patternName,
      params.contentArray,
      params.model
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(results, null, 2),
        },
      ],
    };
  }
}