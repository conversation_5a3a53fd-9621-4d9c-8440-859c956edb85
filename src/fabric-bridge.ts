import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import os from 'os';

interface FabricPattern {
  name: string;
  description: string;
  category: string;
  filePath: string;
}

export interface FabricResult {
  success: boolean;
  output?: string;
  error?: string;
  pattern?: string;
  executionTime?: number;
}

export class FabricBridge {
  private fabricPath: string;
  private patternsPath: string;
  private patterns: Map<string, FabricPattern> = new Map();

  constructor() {
    // Find Fabric binary
    this.fabricPath = this.findFabricBinary();
    this.patternsPath = path.join(os.homedir(), '.config', 'fabric', 'patterns');

    // Validate Fabric installation
    this.validateFabricInstallation();

    // Load available patterns
    this.loadPatterns();
  }

  private findFabricBinary(): string {
    // Try common locations
    const possiblePaths = [
      path.join(os.homedir(), 'go', 'bin', 'fabric'),
      '/usr/local/bin/fabric',
      '/opt/homebrew/bin/fabric',
      'fabric' // In PATH
    ];

    for (const fabricPath of possiblePaths) {
      try {
        if (path.isAbsolute(fabricPath)) {
          if (fs.existsSync(fabricPath)) {
            console.log(`✅ Found Fabric binary: ${fabricPath}`);
            return fabricPath;
          }
        } else {
          // For PATH-based commands, return and let validation handle it
          console.log(`🔍 Trying Fabric in PATH: ${fabricPath}`);
          return fabricPath;
        }
      } catch (error) {
        // Continue to next path
      }
    }

    // Default to go bin location
    console.warn('⚠️  Fabric binary not found in common locations, using default path');
    return path.join(os.homedir(), 'go', 'bin', 'fabric');
  }

  private validateFabricInstallation(): void {
    try {
      // Test if Fabric binary is executable
      const testProcess = spawn(this.fabricPath, ['--version'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      testProcess.on('error', (_error) => {
        console.error(`❌ Fabric binary not found or not executable: ${this.fabricPath}`);
        console.error('💡 Install Fabric with: go install github.com/danielmiessler/fabric@latest');
        console.error('💡 Then run: fabric --setup');
      });

      testProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ Fabric binary is accessible');
        } else {
          console.warn(`⚠️  Fabric binary test failed with code: ${code}`);
          console.warn('💡 Run: fabric --setup to configure Fabric');
        }
      });

    } catch (error) {
      console.error('❌ Failed to validate Fabric installation:', error);
    }
  }

  private loadPatterns(): void {
    try {
      if (fs.existsSync(this.patternsPath)) {
        const patterns = fs.readdirSync(this.patternsPath, { withFileTypes: true });
        
        for (const pattern of patterns) {
          if (pattern.isDirectory()) {
            const systemPath = path.join(this.patternsPath, pattern.name, 'system.md');
            if (fs.existsSync(systemPath)) {
              try {
                const content = fs.readFileSync(systemPath, 'utf-8');
                const description = this.extractDescription(content);
                const category = this.categorizePattern(pattern.name);
                
                this.patterns.set(pattern.name, {
                  name: pattern.name,
                  description,
                  category,
                  filePath: systemPath
                });
              } catch (error) {
                console.warn(`Failed to load pattern ${pattern.name}:`, error);
              }
            }
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load patterns:', error);
    }
  }

  private extractDescription(content: string): string {
    // Extract description from pattern content
    const lines = content.split('\\n');
    const descriptionLine = lines.find(line => 
      line.toLowerCase().includes('extract') || 
      line.toLowerCase().includes('analyze') ||
      line.toLowerCase().includes('create') ||
      line.toLowerCase().includes('summarize')
    );
    
    return descriptionLine ? descriptionLine.trim() : 'No description available';
  }

  private categorizePattern(patternName: string): string {
    const name = patternName.toLowerCase();
    
    if (name.includes('analyze') || name.includes('analysis')) {return 'analysis';}
    if (name.includes('extract')) {return 'extraction';}
    if (name.includes('summarize') || name.includes('summary')) {return 'summarization';}
    if (name.includes('create') || name.includes('generate')) {return 'creation';}
    if (name.includes('improve') || name.includes('enhance')) {return 'improvement';}
    if (name.includes('translate')) {return 'translation';}
    if (name.includes('explain')) {return 'explanation';}
    
    return 'general';
  }

  async listPatterns(category?: string): Promise<FabricPattern[]> {
    const allPatterns = Array.from(this.patterns.values());
    
    if (category) {
      return allPatterns.filter(p => p.category === category);
    }
    
    return allPatterns;
  }

  async getPattern(patternName: string): Promise<FabricPattern | null> {
    return this.patterns.get(patternName) || null;
  }

  async applyPattern(
    patternName: string, 
    input: string, 
    model?: string,
    temperature?: number
  ): Promise<FabricResult> {
    const startTime = Date.now();
    
    return new Promise((resolve) => {
      try {
        const args = ['--pattern', patternName];
        
        if (model) {
          args.push('--model', model);
        }
        
        if (temperature !== undefined) {
          args.push('--temperature', temperature.toString());
        }

        const process = spawn(this.fabricPath, args, {
          stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let error = '';

        process.stdout?.on('data', (data: Buffer) => {
          output += data.toString();
        });

        process.stderr?.on('data', (data: Buffer) => {
          error += data.toString();
        });

        process.on('close', (code) => {
          const executionTime = Date.now() - startTime;
          
          if (code === 0) {
            resolve({
              success: true,
              output: output.trim(),
              pattern: patternName,
              executionTime
            });
          } else {
            resolve({
              success: false,
              error: error || `Process exited with code ${code}`,
              pattern: patternName,
              executionTime
            });
          }
        });

        process.on('error', (err) => {
          resolve({
            success: false,
            error: `Failed to execute Fabric: ${err.message}`,
            pattern: patternName,
            executionTime: Date.now() - startTime
          });
        });

        // Send input to the process
        if (process.stdin) {
          process.stdin.write(input);
          process.stdin.end();
        }

        // Timeout after 2 minutes
        setTimeout(() => {
          process.kill();
          resolve({
            success: false,
            error: 'Pattern execution timed out',
            pattern: patternName,
            executionTime: Date.now() - startTime
          });
        }, 120000);

      } catch (error) {
        resolve({
          success: false,
          error: `Error executing pattern: ${error}`,
          pattern: patternName,
          executionTime: Date.now() - startTime
        });
      }
    });
  }

  async applyPatternToMultipleInputs(
    patternName: string,
    inputs: string[],
    model?: string
  ): Promise<FabricResult[]> {
    const results: FabricResult[] = [];
    
    // Process in batches to avoid overwhelming the system
    const batchSize = 3;
    for (let i = 0; i < inputs.length; i += batchSize) {
      const batch = inputs.slice(i, i + batchSize);
      const batchPromises = batch.map(input => 
        this.applyPattern(patternName, input, model)
      );
      
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }
    
    return results;
  }

  async chainPatterns(
    patternChain: string[],
    input: string,
    model?: string
  ): Promise<FabricResult[]> {
    const results: FabricResult[] = [];
    let currentInput = input;
    
    for (const patternName of patternChain) {
      const result = await this.applyPattern(patternName, currentInput, model);
      results.push(result);
      
      if (result.success && result.output) {
        currentInput = result.output;
      } else {
        // Stop chain if a pattern fails
        break;
      }
    }
    
    return results;
  }

  getCategories(): string[] {
    const categories = new Set<string>();
    for (const pattern of this.patterns.values()) {
      categories.add(pattern.category);
    }
    return Array.from(categories).sort();
  }

  async createCustomPattern(
    name: string,
    systemPrompt: string,
    description: string
  ): Promise<boolean> {
    try {
      const patternDir = path.join(this.patternsPath, name);
      
      if (!fs.existsSync(patternDir)) {
        fs.mkdirSync(patternDir, { recursive: true });
      }
      
      const systemFile = path.join(patternDir, 'system.md');
      fs.writeFileSync(systemFile, systemPrompt);
      
      // Add to patterns map
      this.patterns.set(name, {
        name,
        description,
        category: 'custom',
        filePath: systemFile
      });
      
      return true;
    } catch (error) {
      console.error('Failed to create custom pattern:', error);
      return false;
    }
  }

  // Social media specific helper methods
  async analyzeContent(content: string): Promise<FabricResult> {
    // Try to find the best pattern for content analysis
    const analysisPatterns = ['analyze_claims', 'extract_wisdom', 'analyze_tech_impact'];
    
    for (const pattern of analysisPatterns) {
      if (this.patterns.has(pattern)) {
        return await this.applyPattern(pattern, content);
      }
    }
    
    // Fallback to a general analysis if no specific patterns found
    return {
      success: false,
      error: 'No suitable analysis patterns found'
    };
  }

  async summarizeContent(content: string): Promise<FabricResult> {
    const summaryPatterns = ['summarize', 'create_summary', 'extract_wisdom'];
    
    for (const pattern of summaryPatterns) {
      if (this.patterns.has(pattern)) {
        return await this.applyPattern(pattern, content);
      }
    }
    
    return {
      success: false,
      error: 'No suitable summary patterns found'
    };
  }

  async extractInsights(content: string): Promise<FabricResult> {
    const insightPatterns = ['extract_wisdom', 'find_logical_fallacies', 'analyze_claims'];
    
    for (const pattern of insightPatterns) {
      if (this.patterns.has(pattern)) {
        return await this.applyPattern(pattern, content);
      }
    }
    
    return {
      success: false,
      error: 'No suitable insight extraction patterns found'
    };
  }
}

// Singleton instance
let fabricBridge: FabricBridge | null = null;

export function getFabricBridge(): FabricBridge {
  if (!fabricBridge) {
    fabricBridge = new FabricBridge();
  }
  return fabricBridge;
}