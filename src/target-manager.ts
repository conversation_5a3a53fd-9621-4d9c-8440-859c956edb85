import { readFileSync, writeFileSync, existsSync } from 'fs';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface InfluencerTarget {
  username: string;
  followers: string;
  relevance_score: number;
  last_verified: string;
  topics?: string[];
  engagement_rate?: number;
  verified?: boolean;
}

interface SubredditTarget {
  name: string;
  subscribers: string;
  activity_score: number;
  last_verified: string;
  category?: string;
  growth_trend?: 'up' | 'down' | 'stable';
  mod_quality?: number;
}

interface TargetCategory {
  description: string;
  targets: (InfluencerTarget | SubredditTarget)[];
  discovered_recently: (InfluencerTarget | SubredditTarget)[];
}

interface TargetsConfig {
  version: string;
  last_updated: string;
  auto_discovery: {
    enabled: boolean;
    update_frequency: string;
    discovery_threshold: number;
    max_targets_per_category: number;
  };
  twitter_influencers: { [category: string]: TargetCategory };
  reddit_subreddits: { [category: string]: TargetCategory };
  discovery_keywords: { [category: string]: string[] };
  exclusion_patterns: { [category: string]: string[] };
}

export class TargetManager {
  private configPath: string;
  private config!: TargetsConfig; // Definite assignment assertion since we call loadConfig in constructor

  constructor() {
    this.configPath = resolve(__dirname, '..', 'config', 'targets.json');
    this.loadConfig();
  }

  private loadConfig(): void {
    if (!existsSync(this.configPath)) {
      throw new Error(`Targets configuration not found at ${this.configPath}`);
    }
    
    try {
      const configData = readFileSync(this.configPath, 'utf-8');
      this.config = JSON.parse(configData);
    } catch (error) {
      throw new Error(`Failed to load targets configuration: ${error}`);
    }
  }

  private saveConfig(): void {
    try {
      this.config.last_updated = new Date().toISOString().split('T')[0];
      writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      throw new Error(`Failed to save targets configuration: ${error}`);
    }
  }

  // Get current influencers for a category
  getInfluencers(category?: string): { [category: string]: InfluencerTarget[] } {
    if (category) {
      const categoryData = this.config.twitter_influencers[category];
      if (!categoryData) {
        throw new Error(`Influencer category '${category}' not found`);
      }
      return { [category]: categoryData.targets as InfluencerTarget[] };
    }
    
    const result: { [category: string]: InfluencerTarget[] } = {};
    for (const [cat, data] of Object.entries(this.config.twitter_influencers)) {
      result[cat] = data.targets as InfluencerTarget[];
    }
    return result;
  }

  // Get current subreddits for a category
  getSubreddits(category?: string): { [category: string]: SubredditTarget[] } {
    if (category) {
      const categoryData = this.config.reddit_subreddits[category];
      if (!categoryData) {
        throw new Error(`Subreddit category '${category}' not found`);
      }
      return { [category]: categoryData.targets as SubredditTarget[] };
    }
    
    const result: { [category: string]: SubredditTarget[] } = {};
    for (const [cat, data] of Object.entries(this.config.reddit_subreddits)) {
      result[cat] = data.targets as SubredditTarget[];
    }
    return result;
  }

  // Get all categories and their descriptions
  getCategories(): {
    twitter_influencers: { [category: string]: string };
    reddit_subreddits: { [category: string]: string };
  } {
    const twitterCategories: { [category: string]: string } = {};
    const redditCategories: { [category: string]: string } = {};
    
    for (const [category, data] of Object.entries(this.config.twitter_influencers)) {
      twitterCategories[category] = data.description;
    }
    
    for (const [category, data] of Object.entries(this.config.reddit_subreddits)) {
      redditCategories[category] = data.description;
    }
    
    return {
      twitter_influencers: twitterCategories,
      reddit_subreddits: redditCategories
    };
  }

  // Add a new influencer to a category
  addInfluencer(category: string, influencer: InfluencerTarget): void {
    if (!this.config.twitter_influencers[category]) {
      throw new Error(`Twitter influencer category '${category}' not found`);
    }

    const existingIndex = this.config.twitter_influencers[category].targets.findIndex(
      (target: any) => target.username === influencer.username
    );

    if (existingIndex !== -1) {
      // Update existing influencer
      this.config.twitter_influencers[category].targets[existingIndex] = influencer;
    } else {
      // Add new influencer
      this.config.twitter_influencers[category].targets.push(influencer);
    }

    this.saveConfig();
  }

  // Add a new subreddit to a category
  addSubreddit(category: string, subreddit: SubredditTarget): void {
    if (!this.config.reddit_subreddits[category]) {
      throw new Error(`Reddit subreddit category '${category}' not found`);
    }

    const existingIndex = this.config.reddit_subreddits[category].targets.findIndex(
      (target: any) => target.name === subreddit.name
    );

    if (existingIndex !== -1) {
      // Update existing subreddit
      this.config.reddit_subreddits[category].targets[existingIndex] = subreddit;
    } else {
      // Add new subreddit
      this.config.reddit_subreddits[category].targets.push(subreddit);
    }

    this.saveConfig();
  }

  // Remove an influencer
  removeInfluencer(category: string, username: string): boolean {
    if (!this.config.twitter_influencers[category]) {
      return false;
    }

    const initialLength = this.config.twitter_influencers[category].targets.length;
    this.config.twitter_influencers[category].targets = this.config.twitter_influencers[category].targets.filter(
      (target: any) => target.username !== username
    );

    if (this.config.twitter_influencers[category].targets.length < initialLength) {
      this.saveConfig();
      return true;
    }
    return false;
  }

  // Remove a subreddit
  removeSubreddit(category: string, subredditName: string): boolean {
    if (!this.config.reddit_subreddits[category]) {
      return false;
    }

    const initialLength = this.config.reddit_subreddits[category].targets.length;
    this.config.reddit_subreddits[category].targets = this.config.reddit_subreddits[category].targets.filter(
      (target: any) => target.name !== subredditName
    );

    if (this.config.reddit_subreddits[category].targets.length < initialLength) {
      this.saveConfig();
      return true;
    }
    return false;
  }

  // Get recently discovered targets
  getRecentDiscoveries(): {
    twitter_influencers: { [category: string]: InfluencerTarget[] };
    reddit_subreddits: { [category: string]: SubredditTarget[] };
  } {
    const twitterDiscoveries: { [category: string]: InfluencerTarget[] } = {};
    const redditDiscoveries: { [category: string]: SubredditTarget[] } = {};
    
    for (const [category, data] of Object.entries(this.config.twitter_influencers)) {
      if (data.discovered_recently && data.discovered_recently.length > 0) {
        twitterDiscoveries[category] = data.discovered_recently as InfluencerTarget[];
      }
    }
    
    for (const [category, data] of Object.entries(this.config.reddit_subreddits)) {
      if (data.discovered_recently && data.discovered_recently.length > 0) {
        redditDiscoveries[category] = data.discovered_recently as SubredditTarget[];
      }
    }
    
    return {
      twitter_influencers: twitterDiscoveries,
      reddit_subreddits: redditDiscoveries
    };
  }

  // Add to recently discovered
  addToRecentDiscoveries(type: 'twitter' | 'reddit', category: string, target: InfluencerTarget | SubredditTarget): void {
    if (type === 'twitter') {
      if (!this.config.twitter_influencers[category]) {
        throw new Error(`Twitter category '${category}' not found`);
      }
      this.config.twitter_influencers[category].discovered_recently.push(target);
    } else {
      if (!this.config.reddit_subreddits[category]) {
        throw new Error(`Reddit category '${category}' not found`);
      }
      this.config.reddit_subreddits[category].discovered_recently.push(target);
    }
    
    this.saveConfig();
  }

  // Move discovered target to main list
  promoteDiscoveredTarget(type: 'twitter' | 'reddit', category: string, identifier: string): boolean {
    if (type === 'twitter') {
      const categoryData = this.config.twitter_influencers[category];
      if (!categoryData) {return false;}

      const targetIndex = categoryData.discovered_recently.findIndex(
        (target: any) => target.username === identifier
      );
      
      if (targetIndex !== -1) {
        const target = categoryData.discovered_recently.splice(targetIndex, 1)[0];
        categoryData.targets.push(target);
        this.saveConfig();
        return true;
      }
    } else {
      const categoryData = this.config.reddit_subreddits[category];
      if (!categoryData) {return false;}

      const targetIndex = categoryData.discovered_recently.findIndex(
        (target: any) => target.name === identifier
      );
      
      if (targetIndex !== -1) {
        const target = categoryData.discovered_recently.splice(targetIndex, 1)[0];
        categoryData.targets.push(target);
        this.saveConfig();
        return true;
      }
    }
    
    return false;
  }

  // Get discovery keywords for AI-driven discovery
  getDiscoveryKeywords(): { [category: string]: string[] } {
    return this.config.discovery_keywords;
  }

  // Update discovery keywords
  updateDiscoveryKeywords(category: string, keywords: string[]): void {
    this.config.discovery_keywords[category] = keywords;
    this.saveConfig();
  }

  // Get configuration stats
  getStats(): {
    total_twitter_targets: number;
    total_reddit_targets: number;
    categories: number;
    last_updated: string;
    recent_discoveries: number;
  } {
    let twitterCount = 0;
    let redditCount = 0;
    let recentDiscoveries = 0;

    for (const category of Object.values(this.config.twitter_influencers)) {
      twitterCount += category.targets.length;
      recentDiscoveries += category.discovered_recently.length;
    }

    for (const category of Object.values(this.config.reddit_subreddits)) {
      redditCount += category.targets.length;
      recentDiscoveries += category.discovered_recently.length;
    }

    return {
      total_twitter_targets: twitterCount,
      total_reddit_targets: redditCount,
      categories: Object.keys(this.config.twitter_influencers).length + Object.keys(this.config.reddit_subreddits).length,
      last_updated: this.config.last_updated,
      recent_discoveries: recentDiscoveries
    };
  }

  // Batch get targets for multiple categories
  getBatchTargets(twitterCategories: string[] = [], redditCategories: string[] = []) {
    const results = {
      twitter: {} as { [category: string]: InfluencerTarget[] },
      reddit: {} as { [category: string]: SubredditTarget[] }
    };

    for (const category of twitterCategories) {
      const influencers = this.getInfluencers(category);
      results.twitter[category] = influencers[category];
    }

    for (const category of redditCategories) {
      const subreddits = this.getSubreddits(category);
      results.reddit[category] = subreddits[category];
    }

    return results;
  }

  // Get top targets by relevance/activity score
  getTopTargets(limit: number = 10) {
    const topInfluencers: (InfluencerTarget & { category: string })[] = [];
    const topSubreddits: (SubredditTarget & { category: string })[] = [];

    for (const [category, data] of Object.entries(this.config.twitter_influencers)) {
      for (const target of data.targets as InfluencerTarget[]) {
        topInfluencers.push({ ...target, category });
      }
    }

    for (const [category, data] of Object.entries(this.config.reddit_subreddits)) {
      for (const target of data.targets as SubredditTarget[]) {
        topSubreddits.push({ ...target, category });
      }
    }

    return {
      twitter: topInfluencers
        .sort((a, b) => b.relevance_score - a.relevance_score)
        .slice(0, limit),
      reddit: topSubreddits
        .sort((a, b) => b.activity_score - a.activity_score)
        .slice(0, limit)
    };
  }
}