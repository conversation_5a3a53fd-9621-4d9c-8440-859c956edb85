import { TargetManager } from './target-manager.js';
import { XScraper } from './scraper.js';
import { RedditScraper } from './reddit-scraper.js';
import { getAgentBridge } from './agent-bridge.js';

interface DiscoveryResult {
  type: 'twitter' | 'reddit';
  category: string;
  targets: Array<{
    identifier: string;
    metrics: any;
    relevance_score: number;
    discovery_reason: string;
  }>;
  discovery_metadata: {
    keywords_used: string[];
    timestamp: string;
    total_candidates: number;
    filtered_count: number;
  };
}

export class AITargetDiscovery {
  private targetManager: TargetManager;
  private xScraper: XScraper;
  private redditScraper: RedditScraper;
  private agentBridge: ReturnType<typeof getAgentBridge>;

  constructor() {
    this.targetManager = new TargetManager();
    this.xScraper = new XScraper();
    this.redditScraper = new RedditScraper();
    this.agentBridge = getAgentBridge();
  }

  async discoverTwitterInfluencers(category: string, keywords: string[], limit: number = 5): Promise<DiscoveryResult> {
    const discoveryMetadata = {
      keywords_used: keywords,
      timestamp: new Date().toISOString(),
      total_candidates: 0,
      filtered_count: 0
    };

    const candidates: Array<{
      identifier: string;
      metrics: any;
      relevance_score: number;
      discovery_reason: string;
    }> = [];

    try {
      // Use AI agent to analyze trending topics related to keywords
      const sessionId = await this.agentBridge.createChatSession();
      
      const searchPrompt = `Find influential Twitter accounts in the ${category} space related to these keywords: ${keywords.join(', ')}. 
      Look for accounts that:
      1. Have significant follower counts (10K+)
      2. Regular posting about relevant topics
      3. High engagement rates
      4. Verified or industry-recognized status
      5. Recent activity (posted in last 30 days)
      
      Focus on finding quality over quantity. Return potential usernames (without @) that would be worth tracking.`;

      const aiResponse = await this.agentBridge.chat(sessionId, searchPrompt);
      
      // Parse AI response for potential usernames
      const potentialUsernames = this.extractUsernamesFromAIResponse(aiResponse.response);
      discoveryMetadata.total_candidates = potentialUsernames.length;

      // For each potential username, get basic metrics using existing scraper
      for (const username of potentialUsernames.slice(0, limit * 2)) { // Get more than needed for filtering
        try {
          // Get key contributors data to find relevant influencers
          const contributors = await this.xScraper.getKeyContributors(keywords.join(' '), 10);
          
          const matchingContributor = contributors.find(c => 
            c.username.toLowerCase().includes(username.toLowerCase()) ||
            username.toLowerCase().includes(c.username.toLowerCase())
          );

          if (matchingContributor) {
            const relevanceScore = this.calculateTwitterRelevanceScore(matchingContributor, keywords, category);
            
            if (relevanceScore >= 0.7) { // Only include high-relevance targets
              candidates.push({
                identifier: matchingContributor.username,
                metrics: {
                  follower_count: matchingContributor.followers,
                  verified: matchingContributor.verifiedStatus,
                  bio: matchingContributor.bio,
                  recent_posts: 0 // Not available in current type
                },
                relevance_score: relevanceScore,
                discovery_reason: `AI-discovered through keyword analysis: ${keywords.join(', ')}`
              });
            }
          }
        } catch (error) {
          console.warn(`Failed to analyze candidate ${username}:`, error);
        }
      }

      // Sort by relevance and limit results
      candidates.sort((a, b) => b.relevance_score - a.relevance_score);
      discoveryMetadata.filtered_count = candidates.length;

      return {
        type: 'twitter',
        category,
        targets: candidates.slice(0, limit),
        discovery_metadata: discoveryMetadata
      };

    } catch (error) {
      console.error('Twitter discovery failed:', error);
      return {
        type: 'twitter',
        category,
        targets: [],
        discovery_metadata: discoveryMetadata
      };
    }
  }

  async discoverRedditCommunities(category: string, keywords: string[], limit: number = 5): Promise<DiscoveryResult> {
    const discoveryMetadata = {
      keywords_used: keywords,
      timestamp: new Date().toISOString(),
      total_candidates: 0,
      filtered_count: 0
    };

    const candidates: Array<{
      identifier: string;
      metrics: any;
      relevance_score: number;
      discovery_reason: string;
    }> = [];

    try {
      // Use AI to suggest relevant subreddit searches
      const sessionId = await this.agentBridge.createChatSession();
      
      const searchPrompt = `Suggest active subreddit communities related to ${category} and these keywords: ${keywords.join(', ')}.
      Focus on subreddits that:
      1. Have active daily discussions
      2. Subscriber count above 10K
      3. Good moderation quality
      4. Relevant ongoing conversations
      5. Growing or stable membership
      
      Return subreddit names (without r/) that would be valuable for tracking trends and discussions.`;

      const aiResponse = await this.agentBridge.chat(sessionId, searchPrompt);
      
      // Extract subreddit names from AI response
      const potentialSubreddits = this.extractSubredditsFromAIResponse(aiResponse.response);
      discoveryMetadata.total_candidates = potentialSubreddits.length;

      // For each potential subreddit, get metrics using Reddit scraper
      for (const subredditName of potentialSubreddits.slice(0, limit * 2)) {
        try {
          // Get subreddit posts to analyze activity
          const posts = await this.redditScraper.getSubredditPosts(subredditName, 'hot', 25);
          
          if (posts.length > 0) {
            const subredditMetrics = this.calculateSubredditMetrics(posts);
            const relevanceScore = this.calculateRedditRelevanceScore(subredditMetrics, keywords, category);
            
            if (relevanceScore >= 0.7) {
              candidates.push({
                identifier: subredditName,
                metrics: subredditMetrics,
                relevance_score: relevanceScore,
                discovery_reason: `AI-discovered through keyword analysis: ${keywords.join(', ')}`
              });
            }
          }
        } catch (error) {
          console.warn(`Failed to analyze subreddit ${subredditName}:`, error);
        }
      }

      candidates.sort((a, b) => b.relevance_score - a.relevance_score);
      discoveryMetadata.filtered_count = candidates.length;

      return {
        type: 'reddit',
        category,
        targets: candidates.slice(0, limit),
        discovery_metadata: discoveryMetadata
      };

    } catch (error) {
      console.error('Reddit discovery failed:', error);
      return {
        type: 'reddit',
        category,
        targets: [],
        discovery_metadata: discoveryMetadata
      };
    }
  }

  private extractUsernamesFromAIResponse(response: string): string[] {
    // Extract potential Twitter usernames from AI response
    const usernamePattern = /@?([a-zA-Z0-9_]{1,15})/g;
    const matches = response.match(usernamePattern) || [];
    
    return matches
      .map(match => match.replace('@', ''))
      .filter(username => 
        username.length >= 3 && 
        username.length <= 15 &&
        !['twitter', 'reddit', 'example', 'user', 'account'].includes(username.toLowerCase())
      );
  }

  private extractSubredditsFromAIResponse(response: string): string[] {
    // Extract potential subreddit names from AI response
    const subredditPattern = /r\/([a-zA-Z0-9_]{2,25})|\/r\/([a-zA-Z0-9_]{2,25})|([a-zA-Z0-9_]{2,25})/g;
    const matches = response.match(subredditPattern) || [];
    
    return matches
      .map(match => match.replace(/^(r\/|\/r\/)/, ''))
      .filter(name => 
        name.length >= 2 && 
        name.length <= 25 &&
        !['reddit', 'example', 'test', 'sample'].includes(name.toLowerCase())
      );
  }

  private calculateTwitterRelevanceScore(contributor: any, keywords: string[], category: string): number {
    let score = 0.5; // Base score

    // Follower count influence (up to 0.2)
    if (contributor.followers > 1000000) {score += 0.2;}
    else if (contributor.followers > 100000) {score += 0.15;}
    else if (contributor.followers > 10000) {score += 0.1;}

    // Verified status (0.1)
    if (contributor.verifiedStatus) {score += 0.1;}

    // Bio keyword relevance (up to 0.15)
    const bioLower = (contributor.bio || '').toLowerCase();
    const keywordMatches = keywords.filter(keyword => bioLower.includes(keyword.toLowerCase())).length;
    score += Math.min(keywordMatches * 0.05, 0.15);

    // Recent activity (up to 0.05) - using relevanceScore as proxy
    if (contributor.relevanceScore > 0.8) {score += 0.05;}
    else if (contributor.relevanceScore > 0.6) {score += 0.03;}

    return Math.min(score, 1.0);
  }

  private calculateSubredditMetrics(posts: any[]): any {
    const totalUpvotes = posts.reduce((sum, post) => sum + (post.score || 0), 0);
    const totalComments = posts.reduce((sum, post) => sum + (post.commentCount || 0), 0);
    const avgUpvotes = totalUpvotes / posts.length;
    const avgComments = totalComments / posts.length;

    return {
      total_posts: posts.length,
      avg_upvotes: Math.round(avgUpvotes),
      avg_comments: Math.round(avgComments),
      total_engagement: totalUpvotes + totalComments,
      activity_indicators: {
        high_engagement_posts: posts.filter(p => (p.score || 0) > avgUpvotes * 2).length,
        discussion_posts: posts.filter(p => (p.commentCount || 0) > 10).length
      }
    };
  }

  private calculateRedditRelevanceScore(metrics: any, keywords: string[], category: string): number {
    let score = 0.5; // Base score

    // Engagement level (up to 0.3)
    if (metrics.avg_upvotes > 500) {score += 0.15;}
    else if (metrics.avg_upvotes > 100) {score += 0.1;}
    else if (metrics.avg_upvotes > 20) {score += 0.05;}

    if (metrics.avg_comments > 50) {score += 0.15;}
    else if (metrics.avg_comments > 20) {score += 0.1;}
    else if (metrics.avg_comments > 5) {score += 0.05;}

    // Activity indicators (up to 0.2)
    const engagementRatio = metrics.activity_indicators.high_engagement_posts / metrics.total_posts;
    if (engagementRatio > 0.3) {score += 0.1;}
    else if (engagementRatio > 0.1) {score += 0.05;}

    const discussionRatio = metrics.activity_indicators.discussion_posts / metrics.total_posts;
    if (discussionRatio > 0.5) {score += 0.1;}
    else if (discussionRatio > 0.2) {score += 0.05;}

    return Math.min(score, 1.0);
  }

  async addDiscoveredTargetsToConfig(discoveryResult: DiscoveryResult): Promise<string> {
    let addedCount = 0;
    
    for (const target of discoveryResult.targets) {
      try {
        if (discoveryResult.type === 'twitter') {
          this.targetManager.addToRecentDiscoveries('twitter', discoveryResult.category, {
            username: target.identifier,
            followers: target.metrics.follower_count ? `${target.metrics.follower_count}+` : 'Unknown',
            relevance_score: target.relevance_score,
            last_verified: new Date().toISOString().split('T')[0],
            topics: discoveryResult.discovery_metadata.keywords_used
          });
        } else {
          this.targetManager.addToRecentDiscoveries('reddit', discoveryResult.category, {
            name: target.identifier,
            subscribers: target.metrics.total_posts ? `${target.metrics.total_posts} posts` : 'Unknown',
            activity_score: target.relevance_score,
            last_verified: new Date().toISOString().split('T')[0]
          });
        }
        addedCount++;
      } catch (error) {
        console.warn(`Failed to add discovered target ${target.identifier}:`, error);
      }
    }

    return `Added ${addedCount} discovered ${discoveryResult.type} targets to recent discoveries for category '${discoveryResult.category}'.`;
  }
}