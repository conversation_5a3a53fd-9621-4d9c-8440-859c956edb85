/**
 * Data Quality Analyzer
 * 
 * Comprehensive data quality assessment system that evaluates:
 * - Content completeness and accuracy
 * - Data freshness and currency
 * - Schema compliance and structure validation
 * - Anomaly detection in scraped content
 * - Consistency checks across similar data points
 * - Quality scoring and improvement recommendations
 */

import { writeFileSync, existsSync, readFileSync, mkdirSync } from 'fs';
import { join } from 'path';

interface DataPoint {
  id: string;
  domain: string;
  target: string;
  timestamp: number;
  content: Record<string, unknown>;
  metadata: {
    responseTime: number;
    source: string;
    scrapeMethod: string;
    contentLength: number;
  };
}

interface QualityRule {
  name: string;
  description: string;
  category: 'completeness' | 'accuracy' | 'consistency' | 'freshness' | 'structure';
  severity: 'low' | 'medium' | 'high' | 'critical';
  check: (dataPoint: DataPoint) => QualityResult;
}

interface QualityResult {
  passed: boolean;
  score: number; // 0-100
  issues: string[];
  suggestions: string[];
  metadata?: Record<string, unknown>;
}

interface DataQualityAssessment {
  dataPointId: string;
  timestamp: number;
  overallScore: number;
  categoryScores: {
    completeness: number;
    accuracy: number;
    consistency: number;
    freshness: number;
    structure: number;
  };
  ruleResults: Record<string, QualityResult>;
  issues: QualityIssue[];
  recommendations: string[];
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
}

interface QualityIssue {
  rule: string;
  category: QualityRule['category'];
  severity: QualityRule['severity'];
  message: string;
  affectedFields: string[];
  suggestion: string;
}

interface DataSchema {
  name: string;
  domain: string;
  required: string[];
  optional: string[];
  types: Record<string, 'string' | 'number' | 'boolean' | 'array' | 'object' | 'date' | 'url'>;
  patterns: Record<string, RegExp>;
  ranges: Record<string, { min?: number; max?: number }>;
}

interface AnomalyDetection {
  field: string;
  type: 'outlier' | 'pattern_break' | 'type_mismatch' | 'missing_expected' | 'unexpected_value';
  severity: 'low' | 'medium' | 'high';
  confidence: number;
  description: string;
  value?: unknown;
  expected?: unknown;
}

export class DataQualityAnalyzer {
  private qualityRules: Map<string, QualityRule> = new Map();
  private schemas: Map<string, DataSchema> = new Map();
  private assessmentHistory: DataQualityAssessment[] = [];
  private historicalData: Map<string, DataPoint[]> = new Map(); // Domain-based historical data
  private dataDir: string;
  
  constructor(dataDir: string = './data/quality') {
    this.dataDir = dataDir;
    this.ensureDataDir();
    this.initializeDefaultRules();
    this.loadSchemas();
    this.loadHistoricalAssessments();
  }

  private ensureDataDir(): void {
    if (!existsSync(this.dataDir)) {
      mkdirSync(this.dataDir, { recursive: true });
    }
  }

  private initializeDefaultRules(): void {
    // Completeness Rules
    this.addRule({
      name: 'required_fields_present',
      description: 'All required fields should be present',
      category: 'completeness',
      severity: 'critical',
      check: (dataPoint: DataPoint) => {
        const schema = this.schemas.get(dataPoint.domain);
        if (!schema) {return { passed: true, score: 100, issues: [], suggestions: [] };}

        const missing = schema.required.filter(field => !(field in dataPoint.content));
        const score = Math.max(0, 100 - (missing.length * 20));

        return {
          passed: missing.length === 0,
          score,
          issues: missing.map(field => `Missing required field: ${field}`),
          suggestions: missing.length > 0 ? ['Review scraping selectors for required fields'] : []
        };
      }
    });

    this.addRule({
      name: 'field_completeness',
      description: 'Fields should not be empty or contain placeholder values',
      category: 'completeness',
      severity: 'medium',
      check: (dataPoint: DataPoint) => {
        const issues: string[] = [];
        const emptyFields: string[] = [];
        const placeholderValues = ['n/a', 'null', 'undefined', '', 'loading...', 'error', '---'];

        Object.entries(dataPoint.content).forEach(([field, value]) => {
          if (value === null || value === undefined || value === '') {
            emptyFields.push(field);
          } else if (typeof value === 'string' && placeholderValues.includes(value.toLowerCase())) {
            issues.push(`Field '${field}' contains placeholder value: ${value}`);
          }
        });

        const totalFields = Object.keys(dataPoint.content).length;
        const score = totalFields > 0 ? Math.max(0, 100 - ((emptyFields.length + issues.length) * 10)) : 0;

        return {
          passed: emptyFields.length === 0 && issues.length === 0,
          score,
          issues: [
            ...emptyFields.map(field => `Empty field: ${field}`),
            ...issues
          ],
          suggestions: emptyFields.length > 0 ? ['Check selectors for empty fields', 'Add fallback extraction methods'] : []
        };
      }
    });

    // Accuracy Rules
    this.addRule({
      name: 'data_type_validation',
      description: 'Data should match expected types',
      category: 'accuracy',
      severity: 'medium',
      check: (dataPoint: DataPoint) => {
        const schema = this.schemas.get(dataPoint.domain);
        if (!schema) {return { passed: true, score: 100, issues: [], suggestions: [] };}

        const issues: string[] = [];
        let checkedFields = 0;

        Object.entries(dataPoint.content).forEach(([field, value]) => {
          const expectedType = schema.types[field];
          if (!expectedType) {return;}

          checkedFields++;
          const actualType = this.getValueType(value);
          
          if (expectedType !== actualType) {
            issues.push(`Field '${field}' expected ${expectedType}, got ${actualType}: ${value}`);
          }
        });

        const score = checkedFields > 0 ? Math.max(0, 100 - (issues.length * 15)) : 100;

        return {
          passed: issues.length === 0,
          score,
          issues,
          suggestions: issues.length > 0 ? ['Review data extraction and type conversion logic'] : []
        };
      }
    });

    this.addRule({
      name: 'url_validation',
      description: 'URLs should be valid and accessible',
      category: 'accuracy',
      severity: 'medium',
      check: (dataPoint: DataPoint) => {
        const issues: string[] = [];
        const urlFields = ['url', 'link', 'href', 'image_url', 'avatar', 'profile_url'];
        
        urlFields.forEach(field => {
          const value = dataPoint.content[field];
          if (typeof value === 'string' && value.length > 0) {
            if (!this.isValidUrl(value)) {
              issues.push(`Invalid URL in field '${field}': ${value}`);
            }
          }
        });

        const score = Math.max(0, 100 - (issues.length * 20));

        return {
          passed: issues.length === 0,
          score,
          issues,
          suggestions: issues.length > 0 ? ['Review URL extraction patterns', 'Add URL validation'] : []
        };
      }
    });

    // Freshness Rules
    this.addRule({
      name: 'content_freshness',
      description: 'Content should be recently updated',
      category: 'freshness',
      severity: 'low',
      check: (dataPoint: DataPoint) => {
        const now = Date.now();
        const ageMinutes = (now - dataPoint.timestamp) / (1000 * 60);
        const maxAgeMinutes = 60; // 1 hour threshold
        
        const score = Math.max(0, 100 - Math.max(0, ageMinutes - maxAgeMinutes));
        
        return {
          passed: ageMinutes <= maxAgeMinutes,
          score,
          issues: ageMinutes > maxAgeMinutes ? [`Data is ${Math.round(ageMinutes)} minutes old`] : [],
          suggestions: ageMinutes > maxAgeMinutes ? ['Consider increasing scraping frequency'] : []
        };
      }
    });

    // Structure Rules
    this.addRule({
      name: 'content_structure',
      description: 'Content should follow expected structure patterns',
      category: 'structure',
      severity: 'medium',
      check: (dataPoint: DataPoint) => {
        const schema = this.schemas.get(dataPoint.domain);
        if (!schema) {return { passed: true, score: 100, issues: [], suggestions: [] };}

        const issues: string[] = [];
        let checkedFields = 0;

        Object.entries(schema.patterns).forEach(([field, pattern]) => {
          const value = dataPoint.content[field];
          if (typeof value === 'string' && value.length > 0) {
            checkedFields++;
            if (!pattern.test(value)) {
              issues.push(`Field '${field}' doesn't match expected pattern: ${value}`);
            }
          }
        });

        const score = checkedFields > 0 ? Math.max(0, 100 - (issues.length * 15)) : 100;

        return {
          passed: issues.length === 0,
          score,
          issues,
          suggestions: issues.length > 0 ? ['Review data extraction patterns', 'Update field validation rules'] : []
        };
      }
    });

    console.log(`📋 Data Quality Analyzer initialized with ${this.qualityRules.size} rules`);
  }

  addRule(rule: QualityRule): void {
    this.qualityRules.set(rule.name, rule);
  }

  addSchema(schema: DataSchema): void {
    this.schemas.set(schema.domain, schema);
    this.saveSchema(schema);
  }

  private saveSchema(schema: DataSchema): void {
    try {
      const schemaFile = join(this.dataDir, 'schemas', `${schema.domain}.json`);
      const schemaDir = join(this.dataDir, 'schemas');
      
      if (!existsSync(schemaDir)) {
        mkdirSync(schemaDir, { recursive: true });
      }
      
      writeFileSync(schemaFile, JSON.stringify(schema, null, 2));
    } catch (error) {
      console.error('Failed to save schema:', error);
    }
  }

  private loadSchemas(): void {
    try {
      const schemaDir = join(this.dataDir, 'schemas');
      if (!existsSync(schemaDir)) {return;}

      const schemaFiles = require('fs').readdirSync(schemaDir);
      
      schemaFiles.forEach((file: string) => {
        if (file.endsWith('.json')) {
          const schema = JSON.parse(readFileSync(join(schemaDir, file), 'utf-8'));
          this.schemas.set(schema.domain, schema);
        }
      });

      console.log(`📄 Loaded ${this.schemas.size} data schemas`);
    } catch (error) {
      console.warn('Failed to load schemas:', error);
    }
  }

  private loadHistoricalAssessments(): void {
    try {
      const assessmentFile = join(this.dataDir, 'assessments.json');
      if (existsSync(assessmentFile)) {
        const data = JSON.parse(readFileSync(assessmentFile, 'utf-8'));
        this.assessmentHistory = data.assessments || [];
        console.log(`📊 Loaded ${this.assessmentHistory.length} historical quality assessments`);
      }
    } catch (error) {
      console.warn('Failed to load historical assessments:', error);
    }
  }

  async assessDataQuality(dataPoint: DataPoint): Promise<DataQualityAssessment> {
    const startTime = Date.now();
    const ruleResults: Record<string, QualityResult> = {};
    const issues: QualityIssue[] = [];
    const categoryScores = {
      completeness: 0,
      accuracy: 0,
      consistency: 0,
      freshness: 0,
      structure: 0
    };
    const categoryCounts = {
      completeness: 0,
      accuracy: 0,
      consistency: 0,
      freshness: 0,
      structure: 0
    };

    // Store historical data for consistency checks
    if (!this.historicalData.has(dataPoint.domain)) {
      this.historicalData.set(dataPoint.domain, []);
    }
    this.historicalData.get(dataPoint.domain)!.push(dataPoint);

    // Run all quality rules
    for (const [ruleName, rule] of this.qualityRules.entries()) {
      try {
        const result = rule.check(dataPoint);
        ruleResults[ruleName] = result;

        // Aggregate scores by category
        categoryScores[rule.category] += result.score;
        categoryCounts[rule.category]++;

        // Collect issues
        if (!result.passed) {
          result.issues.forEach(issue => {
            issues.push({
              rule: ruleName,
              category: rule.category,
              severity: rule.severity,
              message: issue,
              affectedFields: this.extractFieldNamesFromMessage(issue),
              suggestion: result.suggestions[0] || 'Review and fix this issue'
            });
          });
        }
      } catch (error) {
        console.error(`Quality rule '${ruleName}' failed:`, error);
        ruleResults[ruleName] = {
          passed: false,
          score: 0,
          issues: [`Rule execution failed: ${error}`],
          suggestions: ['Fix the quality rule implementation']
        };
      }
    }

    // Calculate category averages
    Object.keys(categoryScores).forEach(category => {
      const cat = category as keyof typeof categoryScores;
      if (categoryCounts[cat] > 0) {
        categoryScores[cat] = categoryScores[cat] / categoryCounts[cat];
      } else {
        categoryScores[cat] = 100; // No rules in category means perfect score
      }
    });

    // Run anomaly detection
    const anomalies = this.detectAnomalies(dataPoint);
    
    // Add anomalies to issues
    anomalies.forEach(anomaly => {
      issues.push({
        rule: 'anomaly_detection',
        category: 'consistency',
        severity: anomaly.severity,
        message: anomaly.description,
        affectedFields: [anomaly.field],
        suggestion: 'Investigate unusual data pattern'
      });
    });

    // Calculate overall score
    const overallScore = Object.values(categoryScores).reduce((sum, score) => sum + score, 0) / 5;
    
    // Determine grade
    const grade = this.calculateGrade(overallScore);
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(issues, categoryScores);

    const assessment: DataQualityAssessment = {
      dataPointId: dataPoint.id,
      timestamp: Date.now(),
      overallScore: Math.round(overallScore),
      categoryScores: {
        completeness: Math.round(categoryScores.completeness),
        accuracy: Math.round(categoryScores.accuracy),
        consistency: Math.round(categoryScores.consistency),
        freshness: Math.round(categoryScores.freshness),
        structure: Math.round(categoryScores.structure)
      },
      ruleResults,
      issues,
      recommendations,
      grade
    };

    this.assessmentHistory.push(assessment);
    
    // Keep only last 1000 assessments
    if (this.assessmentHistory.length > 1000) {
      this.assessmentHistory = this.assessmentHistory.slice(-1000);
    }

    // Persist assessments periodically
    if (this.assessmentHistory.length % 50 === 0) {
      this.persistAssessments();
    }

    const processingTime = Date.now() - startTime;
    console.log(`🔍 Data quality assessment completed in ${processingTime}ms - Score: ${assessment.overallScore}/100 (${assessment.grade})`);

    return assessment;
  }

  private detectAnomalies(dataPoint: DataPoint): AnomalyDetection[] {
    const anomalies: AnomalyDetection[] = [];
    const historicalData = this.historicalData.get(dataPoint.domain) || [];
    
    if (historicalData.length < 10) {
      return anomalies; // Need more data for anomaly detection
    }

    const recentData = historicalData.slice(-50); // Last 50 data points
    
    Object.entries(dataPoint.content).forEach(([field, value]) => {
      // Get historical values for this field
      const historicalValues = recentData
        .map(dp => dp.content[field])
        .filter(v => v !== null && v !== undefined);

      if (historicalValues.length < 5) {return;}

      // Check for type mismatches
      const currentType = this.getValueType(value);
      const expectedType = this.getMostCommonType(historicalValues);
      
      if (currentType !== expectedType) {
        anomalies.push({
          field,
          type: 'type_mismatch',
          severity: 'medium',
          confidence: 85,
          description: `Field '${field}' type changed from ${expectedType} to ${currentType}`,
          value,
          expected: expectedType
        });
      }

      // Check for numerical outliers
      if (typeof value === 'number') {
        const numbers = historicalValues.filter(v => typeof v === 'number');
        if (numbers.length >= 5) {
          const stats = this.calculateStats(numbers);
          const zScore = Math.abs((value - stats.mean) / stats.stdDev);
          
          if (zScore > 2.5) { // 2.5 standard deviations
            anomalies.push({
              field,
              type: 'outlier',
              severity: zScore > 3 ? 'high' : 'medium',
              confidence: Math.min(95, zScore * 30),
              description: `Field '${field}' value ${value} is ${zScore.toFixed(1)} standard deviations from mean (${stats.mean.toFixed(1)})`,
              value,
              expected: `${stats.mean.toFixed(1)} ± ${stats.stdDev.toFixed(1)}`
            });
          }
        }
      }

      // Check for unexpected string patterns
      if (typeof value === 'string') {
        const strings = historicalValues.filter(v => typeof v === 'string');
        const commonPatterns = this.findCommonPatterns(strings);
        
        if (commonPatterns.length > 0 && !commonPatterns.some(pattern => pattern.test(value))) {
          anomalies.push({
            field,
            type: 'pattern_break',
            severity: 'low',
            confidence: 70,
            description: `Field '${field}' doesn't match common patterns: ${value}`,
            value
          });
        }
      }
    });

    return anomalies;
  }

  private getValueType(value: unknown): string {
    if (value === null || value === undefined) {return 'null';}
    if (Array.isArray(value)) {return 'array';}
    if (typeof value === 'string') {
      if (this.isValidUrl(value)) {return 'url';}
      if (this.isValidDate(value)) {return 'date';}
    }
    return typeof value;
  }

  private getMostCommonType(values: unknown[]): string {
    const typeCounts: Record<string, number> = {};
    
    values.forEach(value => {
      const type = this.getValueType(value);
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });

    return Object.entries(typeCounts)
      .sort(([,a], [,b]) => b - a)[0][0];
  }

  private calculateStats(numbers: number[]): { mean: number; stdDev: number; min: number; max: number } {
    const mean = numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
    const variance = numbers.reduce((sum, n) => sum + Math.pow(n - mean, 2), 0) / numbers.length;
    const stdDev = Math.sqrt(variance);
    
    return {
      mean,
      stdDev,
      min: Math.min(...numbers),
      max: Math.max(...numbers)
    };
  }

  private findCommonPatterns(strings: string[]): RegExp[] {
    // Simplified pattern detection - in production, you'd use more sophisticated analysis
    const patterns: RegExp[] = [];
    
    // Check if most strings are URLs
    if (strings.filter(s => this.isValidUrl(s)).length / strings.length > 0.8) {
      patterns.push(/^https?:\/\/.+/);
    }
    
    // Check if most strings are dates
    if (strings.filter(s => this.isValidDate(s)).length / strings.length > 0.8) {
      patterns.push(/^\d{4}-\d{2}-\d{2}/);
    }
    
    return patterns;
  }

  private isValidUrl(str: string): boolean {
    try {
      new URL(str);
      return true;
    } catch {
      return false;
    }
  }

  private isValidDate(str: string): boolean {
    const date = new Date(str);
    return !isNaN(date.getTime()) && str.length > 8;
  }

  private extractFieldNamesFromMessage(message: string): string[] {
    // Extract field names from error messages
    const matches = message.match(/'([^']+)'/g);
    return matches ? matches.map(m => m.replace(/'/g, '')) : [];
  }

  private calculateGrade(score: number): DataQualityAssessment['grade'] {
    if (score >= 90) {return 'A';}
    if (score >= 80) {return 'B';}
    if (score >= 70) {return 'C';}
    if (score >= 60) {return 'D';}
    return 'F';
  }

  private generateRecommendations(issues: QualityIssue[], categoryScores: DataQualityAssessment['categoryScores']): string[] {
    const recommendations: string[] = [];
    
    // Category-based recommendations
    if (categoryScores.completeness < 80) {
      recommendations.push('Improve field extraction completeness by reviewing selectors and adding fallbacks');
    }
    
    if (categoryScores.accuracy < 80) {
      recommendations.push('Enhance data accuracy by adding validation and type checking');
    }
    
    if (categoryScores.structure < 80) {
      recommendations.push('Review data structure patterns and update extraction rules');
    }

    // Issue severity recommendations
    const criticalIssues = issues.filter(i => i.severity === 'critical').length;
    if (criticalIssues > 0) {
      recommendations.push(`Address ${criticalIssues} critical data quality issues immediately`);
    }

    const highIssues = issues.filter(i => i.severity === 'high').length;
    if (highIssues > 2) {
      recommendations.push(`Review and fix ${highIssues} high-priority issues`);
    }

    return recommendations.slice(0, 5); // Limit to 5 recommendations
  }

  private persistAssessments(): void {
    try {
      writeFileSync(
        join(this.dataDir, 'assessments.json'),
        JSON.stringify({ assessments: this.assessmentHistory }, null, 2)
      );
    } catch (error) {
      console.error('Failed to persist assessments:', error);
    }
  }

  getQualityTrend(domain: string, timeframe: number = 24 * 60 * 60 * 1000): {
    trend: 'improving' | 'stable' | 'declining';
    currentScore: number;
    change: number;
    assessments: number;
  } {
    const cutoff = Date.now() - timeframe;
    const recentAssessments = this.assessmentHistory
      .filter(a => a.timestamp > cutoff)
      .filter(a => {
        const dataPoint = this.historicalData.get(domain)?.find(dp => dp.id === a.dataPointId);
        return dataPoint?.domain === domain;
      })
      .sort((a, b) => a.timestamp - b.timestamp);

    if (recentAssessments.length < 2) {
      return { trend: 'stable', currentScore: 0, change: 0, assessments: recentAssessments.length };
    }

    const firstHalf = recentAssessments.slice(0, Math.floor(recentAssessments.length / 2));
    const secondHalf = recentAssessments.slice(Math.floor(recentAssessments.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, a) => sum + a.overallScore, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, a) => sum + a.overallScore, 0) / secondHalf.length;
    
    const change = secondAvg - firstAvg;
    const trend = Math.abs(change) < 2 ? 'stable' : (change > 0 ? 'improving' : 'declining');

    return {
      trend,
      currentScore: Math.round(secondAvg),
      change: Math.round(change),
      assessments: recentAssessments.length
    };
  }

  getTopIssues(limit: number = 10): Array<{
    issue: string;
    frequency: number;
    severity: QualityIssue['severity'];
    category: QualityIssue['category'];
    affectedDomains: string[];
  }> {
    const issueMap: Map<string, {
      frequency: number;
      severity: QualityIssue['severity'];
      category: QualityIssue['category'];
      domains: Set<string>;
    }> = new Map();

    this.assessmentHistory.slice(-200).forEach(assessment => {
      const dataPoint = Array.from(this.historicalData.values())
        .flat()
        .find(dp => dp.id === assessment.dataPointId);
      
      if (!dataPoint) {return;}

      assessment.issues.forEach(issue => {
        const key = issue.message;
        if (!issueMap.has(key)) {
          issueMap.set(key, {
            frequency: 0,
            severity: issue.severity,
            category: issue.category,
            domains: new Set()
          });
        }
        
        const entry = issueMap.get(key)!;
        entry.frequency++;
        entry.domains.add(dataPoint.domain);
      });
    });

    return Array.from(issueMap.entries())
      .map(([issue, data]) => ({
        issue,
        frequency: data.frequency,
        severity: data.severity,
        category: data.category,
        affectedDomains: Array.from(data.domains)
      }))
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, limit);
  }

  cleanup(): void {
    console.log('🧹 Cleaning up Data Quality Analyzer...');
    this.persistAssessments();
    this.historicalData.clear();
    console.log('✅ Data Quality Analyzer cleanup complete');
  }
}