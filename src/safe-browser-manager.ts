// Safe Browser Manager with User Agent Rotation & Detection Avoidance

import puppeteer from 'puppeteer-extra';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import ProxyPlugin from 'puppeteer-extra-plugin-proxy';
import {
  ScrapingConfig,
  getRandomUserAgent,
  getRandomViewport,
  getRandomDelay,
} from './safe-scraper-config.js';
import { RequestMonitor } from './request-monitor.js';

// Configure puppeteer-extra plugins
puppeteer.use(StealthPlugin());

interface BrowserSession {
  browser: Browser;
  page: Page;
  userAgent: string;
  viewport: { width: number; height: number };
  createdAt: number;
  requestCount: number;
}

interface ProxyConfig {
  enabled: boolean;
  url?: string;
  username?: string;
  password?: string;
  rotateEndpoint?: string; // For services that provide rotating proxy endpoints
}

export class SafeBrowserManager {
  private config: ScrapingConfig;
  private monitor: RequestMonitor;
  private sessions: Map<string, BrowserSession> = new Map();
  private maxSessionRequests = 50; // Rotate browser after N requests
  private maxSessionAge = 3600000; // 1 hour max session age
  private proxyConfig: ProxyConfig;

  constructor(config: ScrapingConfig, monitor: RequestMonitor) {
    this.config = config;
    this.monitor = monitor;
    this.proxyConfig = this.loadProxyConfig();

    // Configure proxy plugin if enabled
    if (this.proxyConfig.enabled && this.proxyConfig.url) {
      puppeteer.use(
        ProxyPlugin({
          proxy: {
            url: this.proxyConfig.url,
            username: this.proxyConfig.username,
            password: this.proxyConfig.password,
          },
        })
      );
    }
  }

  private loadProxyConfig(): ProxyConfig {
    return {
      enabled: process.env.PROXY_ENABLED === 'true',
      url: process.env.PROXY_URL,
      username: process.env.PROXY_USERNAME,
      password: process.env.PROXY_PASSWORD,
      rotateEndpoint: process.env.PROXY_ROTATE_ENDPOINT,
    };
  }

  private async createSession(_domain: string): Promise<BrowserSession> {
    const userAgent = getRandomUserAgent();
    const viewport = getRandomViewport();

    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-features=VizDisplayCompositor',
        '--disable-images', // Faster loading
        `--window-size=${viewport.width},${viewport.height}`,
      ],
    });

    const page = await browser.newPage();

    // Set viewport
    await page.setViewport({
      width: viewport.width,
      height: viewport.height,
      deviceScaleFactor: 1,
      hasTouch: false,
      isLandscape: viewport.width > viewport.height,
      isMobile: false,
    });

    // Set user agent
    await page.setUserAgent(userAgent);

    // Set additional headers to look more human
    await page.setExtraHTTPHeaders({
      Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate, br',
      DNT: '1',
      Connection: 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Cache-Control': 'max-age=0',
    });

    // The stealth plugin handles anti-detection automatically
    // No need for manual navigator property modifications

    const session: BrowserSession = {
      browser,
      page,
      userAgent,
      viewport,
      createdAt: Date.now(),
      requestCount: 0,
    };

    return session;
  }

  private async getSession(domain: string): Promise<BrowserSession> {
    const existingSession = this.sessions.get(domain);
    const now = Date.now();

    // Check if existing session is still valid
    if (existingSession) {
      const isExpired = now - existingSession.createdAt > this.maxSessionAge;
      const tooManyRequests = existingSession.requestCount >= this.maxSessionRequests;

      if (!isExpired && !tooManyRequests) {
        return existingSession;
      } else {
        // Close expired session
        await this.closeSession(domain);
      }
    }

    // Create new session
    const session = await this.createSession(domain);
    this.sessions.set(domain, session);
    return session;
  }

  private async closeSession(domain: string): Promise<void> {
    const session = this.sessions.get(domain);
    if (session) {
      try {
        await session.browser.close();
      } catch (error) {
        console.warn(`Error closing browser session for ${domain}:`, error);
      }
      this.sessions.delete(domain);
    }
  }

  async safePage(domain: string): Promise<{ page: Page; cleanup: () => Promise<void> }> {
    const session = await this.getSession(domain);
    session.requestCount++;

    const cleanup = async (): Promise<void> => {
      // Optional: Close session after each request for maximum safety
      if (this.config.safetyMode === 'strict') {
        await this.closeSession(domain);
      }
    };

    return { page: session.page, cleanup };
  }

  async safeNavigate(
    page: Page,
    url: string,
    domain: string,
    target: string
  ): Promise<{ success: boolean; error?: string }> {
    const startTime = Date.now();

    try {
      // Add random delay before navigation
      if (this.config.humanLikeTiming) {
        const delay = getRandomDelay(1000, 3000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // Navigate with timeout
      const response = await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: 30000,
      });

      const responseTime = Date.now() - startTime;
      const statusCode = response?.status();

      // Check for blocking indicators
      const pageContent = await page.content().catch(() => '');
      const isBlocked =
        statusCode === 429 ||
        statusCode === 403 ||
        pageContent.toLowerCase().includes('rate limit') ||
        pageContent.toLowerCase().includes('blocked');

      // Log the request
      await this.monitor.logRequest({
        domain,
        target,
        success: !isBlocked && statusCode === 200,
        responseTime,
        statusCode,
        errorType: isBlocked ? 'Blocked/Rate Limited' : undefined,
        blocked: isBlocked,
      });

      if (isBlocked) {
        return {
          success: false,
          error: `Request blocked or rate limited (Status: ${statusCode})`,
        };
      }

      return { success: true };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      await this.monitor.logRequest({
        domain,
        target,
        success: false,
        responseTime,
        errorType: errorMessage,
      });

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  async humanLikeActions(page: Page): Promise<void> {
    if (!this.config.humanLikeTiming) {
      return;
    }

    try {
      // Random mouse movements
      const viewport = page.viewport();
      if (viewport) {
        await page.mouse.move(Math.random() * viewport.width, Math.random() * viewport.height);
      }

      // Random small scroll
      await page.evaluate(() => {
        window.scrollBy(0, Math.random() * 100 - 50);
      });

      // Random short delay
      await new Promise(resolve => setTimeout(resolve, getRandomDelay(500, 2000)));
    } catch (error) {
      // Ignore errors in human-like actions
    }
  }

  async cleanup(): Promise<void> {
    const closingPromises = Array.from(this.sessions.keys()).map(domain =>
      this.closeSession(domain)
    );

    await Promise.all(closingPromises);
  }

  getSessionInfo(): {
    activeSessions: number;
    domains: string[];
    totalRequests: number;
    proxyEnabled: boolean;
  } {
    const domains = Array.from(this.sessions.keys());
    const totalRequests = Array.from(this.sessions.values()).reduce(
      (sum, session) => sum + session.requestCount,
      0
    );

    return {
      activeSessions: this.sessions.size,
      domains,
      totalRequests,
      proxyEnabled: this.proxyConfig.enabled,
    };
  }

  // Method to rotate proxy if using a rotating proxy service
  private async rotateProxy(): Promise<void> {
    if (!this.proxyConfig.enabled || !this.proxyConfig.rotateEndpoint) {
      return;
    }

    try {
      // Call the rotate endpoint if provided (for services like Bright Data, Oxylabs, etc.)
      const response = await fetch(this.proxyConfig.rotateEndpoint, {
        method: 'POST',
        headers: {
          Authorization: `Basic ${Buffer.from(`${this.proxyConfig.username}:${this.proxyConfig.password}`).toString('base64')}`,
        },
      });

      if (!response.ok) {
        console.warn('Failed to rotate proxy:', response.statusText);
      }
    } catch (error) {
      console.warn('Error rotating proxy:', error);
    }
  }

  // Method to get current proxy status
  getProxyStatus(): ProxyConfig & { active: boolean } {
    return {
      ...this.proxyConfig,
      active: this.proxyConfig.enabled && !!this.proxyConfig.url,
    };
  }
}
