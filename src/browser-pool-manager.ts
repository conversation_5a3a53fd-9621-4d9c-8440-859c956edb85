/**
 * Browser Pool Manager - Optimized Browser Instance Management
 * 
 * Manages a pool of browser instances for efficient concurrent scraping:
 * - Connection pooling to reduce startup overhead
 * - Automatic cleanup of idle browsers to save memory
 * - Browser recycling after N requests to prevent memory leaks
 * - Graceful error handling and recovery
 * - Load balancing across available browsers
 * - Resource optimization with configurable limits
 */

import puppeteer from 'puppeteer-extra';
import { <PERSON>rows<PERSON>, <PERSON> } from 'puppeteer';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import { EventEmitter } from 'events';

// Configure puppeteer-extra with stealth plugin
puppeteer.use(StealthPlugin());

interface BrowserInstance {
  id: string;
  browser: Browser;
  createdAt: number;
  lastUsed: number;
  requestCount: number;
  isActive: boolean;
  isHealthy: boolean;
  activePagesCount: number;
  maxPages: number;
}

interface BrowserPoolConfig {
  minBrowsers: number;          // Minimum browsers to keep in pool
  maxBrowsers: number;          // Maximum browsers in pool
  maxPagesPerBrowser: number;   // Max pages per browser instance
  maxRequestsPerBrowser: number; // Recycle browser after N requests
  idleTimeout: number;          // Remove idle browsers after N ms
  healthCheckInterval: number;  // Health check frequency
  launchOptions: Record<string, unknown>;           // Puppeteer launch options
  enableStealth: boolean;       // Enable stealth plugin
}

interface PoolStats {
  totalBrowsers: number;
  activeBrowsers: number;
  idleBrowsers: number;
  totalPages: number;
  totalRequests: number;
  averageResponseTime: number;
  healthyBrowsers: number;
  poolUtilization: number;
  memoryUsage?: NodeJS.MemoryUsage;
}

const DEFAULT_CONFIG: BrowserPoolConfig = {
  minBrowsers: 1,
  maxBrowsers: 5,
  maxPagesPerBrowser: 10,
  maxRequestsPerBrowser: 100,
  idleTimeout: 300000, // 5 minutes
  healthCheckInterval: 60000, // 1 minute
  enableStealth: true,
  launchOptions: {
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--disable-features=VizDisplayCompositor',
      '--disable-images',
      '--disable-javascript',
      '--disable-plugins',
      '--disable-extensions',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--memory-pressure-off',
      '--max_old_space_size=4096'
    ]
  }
};

export class BrowserPoolManager extends EventEmitter {
  private config: BrowserPoolConfig;
  private browserPool: Map<string, BrowserInstance> = new Map();
  private availableBrowsers: Set<string> = new Set();
  private requestQueue: Array<{ resolve: (browser: BrowserInstance) => void; reject: (error: Error) => void }> = [];
  
  private healthCheckTimer: NodeJS.Timeout | null = null;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private stats = {
    totalRequests: 0,
    totalResponseTime: 0,
    createdBrowsers: 0,
    destroyedBrowsers: 0
  };
  
  constructor(config: Partial<BrowserPoolConfig> = {}) {
    super();
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    this.startHealthCheck();
    this.startCleanupTimer();
    void this.initializeMinBrowsers();
    
    console.log('🚀 Browser Pool Manager initialized', {
      minBrowsers: this.config.minBrowsers,
      maxBrowsers: this.config.maxBrowsers,
      stealth: this.config.enableStealth
    });
  }
  
  private async initializeMinBrowsers(): Promise<void> {
    const promises: Promise<void>[] = [];
    
    for (let i = 0; i < this.config.minBrowsers; i++) {
      promises.push(this.createBrowser().then(() => {}).catch(error => {
        console.warn(`Failed to initialize browser ${i + 1}:`, error);
      }));
    }
    
    await Promise.allSettled(promises);
    console.log(`✅ Initialized ${this.availableBrowsers.size}/${this.config.minBrowsers} minimum browsers`);
  }
  
  private async createBrowser(): Promise<BrowserInstance> {
    if (this.browserPool.size >= this.config.maxBrowsers) {
      throw new Error('Browser pool at maximum capacity');
    }
    
    console.log('🌐 Creating new browser instance...');
    const startTime = Date.now();
    
    try {
      const browser = await puppeteer.launch(this.config.launchOptions);
      const id = this.generateBrowserId();
      
      const instance: BrowserInstance = {
        id,
        browser,
        createdAt: Date.now(),
        lastUsed: Date.now(),
        requestCount: 0,
        isActive: false,
        isHealthy: true,
        activePagesCount: 0,
        maxPages: this.config.maxPagesPerBrowser
      };
      
      this.browserPool.set(id, instance);
      this.availableBrowsers.add(id);
      this.stats.createdBrowsers++;
      
      // Set up browser event handlers
      browser.on('disconnected', () => {
        console.warn(`Browser ${id} disconnected unexpectedly`);
        this.handleBrowserDisconnection(id);
      });
      
      const initTime = Date.now() - startTime;
      console.log(`✅ Browser ${id} created in ${initTime}ms`);
      
      this.emit('browserCreated', { id, initTime });
      return instance;
      
    } catch (error) {
      console.error('Failed to create browser:', error);
      throw error;
    }
  }
  
  private generateBrowserId(): string {
    return `browser_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }
  
  private handleBrowserDisconnection(browserId: string): void {
    const instance = this.browserPool.get(browserId);
    if (instance) {
      instance.isHealthy = false;
      this.availableBrowsers.delete(browserId);
      this.browserPool.delete(browserId);
      
      this.emit('browserDisconnected', { id: browserId });
      
      // If we're below minimum, create a new browser
      if (this.browserPool.size < this.config.minBrowsers) {
        this.createBrowser().catch(error => {
          console.error('Failed to replace disconnected browser:', error);
        });
      }
    }
  }
  
  // Get an available browser instance
  async getBrowser(): Promise<BrowserInstance> {
    return new Promise((resolve, reject) => {
      const handleRequest = async (): Promise<void> => {
        try {
          // Try to get an available browser immediately
          const availableId = this.getAvailableBrowserId();
          
          if (availableId) {
            const instance = this.browserPool.get(availableId)!;
            this.assignBrowser(instance);
            resolve(instance);
            return;
          }
          
          // No available browser, try to create one
          if (this.browserPool.size < this.config.maxBrowsers) {
            try {
              const newInstance = await this.createBrowser();
              this.assignBrowser(newInstance);
              resolve(newInstance);
              return;
            } catch (error) {
              // Creation failed, fall through to queuing
            }
          }
          
          // Queue the request
          this.requestQueue.push({ resolve, reject });
          console.log(`📋 Request queued, ${this.requestQueue.length} waiting`);
          
          // Set timeout for queued requests
          setTimeout(() => {
            const index = this.requestQueue.findIndex(req => req.resolve === resolve);
            if (index !== -1) {
              this.requestQueue.splice(index, 1);
              reject(new Error('Browser acquisition timeout'));
            }
          }, 30000); // 30 second timeout
          
        } catch (error) {
          reject(error);
        }
      };
      
      void handleRequest();
    });
  }
  
  private getAvailableBrowserId(): string | null {
    for (const id of this.availableBrowsers) {
      const instance = this.browserPool.get(id);
      if (instance && instance.isHealthy && !instance.isActive && 
          instance.activePagesCount < instance.maxPages) {
        return id;
      }
    }
    return null;
  }
  
  private assignBrowser(instance: BrowserInstance): void {
    instance.isActive = true;
    instance.lastUsed = Date.now();
    this.availableBrowsers.delete(instance.id);
    
    this.stats.totalRequests++;
  }
  
  // Release browser back to pool
  async releaseBrowser(instance: BrowserInstance): Promise<void> {
    if (!this.browserPool.has(instance.id)) {
      console.warn(`Attempted to release unknown browser ${instance.id}`);
      return;
    }
    
    instance.isActive = false;
    instance.requestCount++;
    instance.lastUsed = Date.now();
    
    // Check if browser needs recycling
    if (instance.requestCount >= this.config.maxRequestsPerBrowser) {
      console.log(`♻️ Recycling browser ${instance.id} after ${instance.requestCount} requests`);
      await this.destroyBrowser(instance.id);
      
      // Create replacement if needed
      if (this.browserPool.size < this.config.minBrowsers) {
        void this.createBrowser().catch(error => {
          console.error('Failed to create replacement browser:', error);
        });
      }
    } else {
      // Return to available pool
      this.availableBrowsers.add(instance.id);
    }
    
    // Process queued requests
    this.processRequestQueue();
  }
  
  private processRequestQueue(): void {
    while (this.requestQueue.length > 0 && this.availableBrowsers.size > 0) {
      const request = this.requestQueue.shift()!;
      const availableId = this.getAvailableBrowserId();
      
      if (availableId) {
        const instance = this.browserPool.get(availableId)!;
        this.assignBrowser(instance);
        request.resolve(instance);
      } else {
        // Put request back at the front
        this.requestQueue.unshift(request);
        break;
      }
    }
  }
  
  // Get page from browser instance
  async getPage(instance: BrowserInstance): Promise<Page> {
    if (instance.activePagesCount >= instance.maxPages) {
      throw new Error(`Browser ${instance.id} at max page capacity`);
    }
    
    const page = await instance.browser.newPage();
    instance.activePagesCount++;
    
    // Set up page cleanup
    page.once('close', () => {
      instance.activePagesCount = Math.max(0, instance.activePagesCount - 1);
    });
    
    return page;
  }
  
  private async destroyBrowser(browserId: string): Promise<void> {
    const instance = this.browserPool.get(browserId);
    if (!instance) {return;}
    
    try {
      console.log(`🗑️ Destroying browser ${browserId}`);
      
      this.browserPool.delete(browserId);
      this.availableBrowsers.delete(browserId);
      this.stats.destroyedBrowsers++;
      
      // Close all pages first
      const pages = await instance.browser.pages();
      await Promise.allSettled(pages.map(page => page.close()));
      
      // Close browser
      await instance.browser.close();
      
      this.emit('browserDestroyed', { id: browserId });
      
    } catch (error) {
      console.warn(`Error destroying browser ${browserId}:`, error);
    }
  }
  
  private startHealthCheck(): void {
    this.healthCheckTimer = setInterval(() => {
      void this.performHealthCheck();
    }, this.config.healthCheckInterval);
  }
  
  private async performHealthCheck(): Promise<void> {
    const healthCheckPromises: Promise<void>[] = [];
    
    for (const [id, instance] of this.browserPool.entries()) {
      if (!instance.isActive) {
        healthCheckPromises.push(this.checkBrowserHealth(id, instance));
      }
    }
    
    await Promise.allSettled(healthCheckPromises);
  }
  
  private async checkBrowserHealth(id: string, instance: BrowserInstance): Promise<void> {
    try {
      // Simple health check - try to get browser version
      const version = await instance.browser.version();
      if (version) {
        instance.isHealthy = true;
      } else {
        throw new Error('Browser not responding');
      }
    } catch (error) {
      console.warn(`Browser ${id} failed health check:`, error);
      instance.isHealthy = false;
      this.availableBrowsers.delete(id);
      
      // Schedule for destruction
      setTimeout(() => {
        void this.destroyBrowser(id);
      }, 1000);
    }
  }
  
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupIdleBrowsers();
    }, this.config.idleTimeout / 2); // Check twice as often as timeout
  }
  
  private cleanupIdleBrowsers(): void {
    const now = Date.now();
    const idleThreshold = now - this.config.idleTimeout;
    
    for (const [id, instance] of this.browserPool.entries()) {
      if (!instance.isActive && 
          instance.lastUsed < idleThreshold && 
          this.browserPool.size > this.config.minBrowsers) {
        
        console.log(`🧹 Cleaning up idle browser ${id} (idle for ${Math.round((now - instance.lastUsed) / 1000)}s)`);
        void this.destroyBrowser(id);
      }
    }
  }
  
  // Get pool statistics
  getStats(): PoolStats {
    const activeBrowsers = Array.from(this.browserPool.values()).filter(b => b.isActive).length;
    const healthyBrowsers = Array.from(this.browserPool.values()).filter(b => b.isHealthy).length;
    const totalPages = Array.from(this.browserPool.values()).reduce((sum, b) => sum + b.activePagesCount, 0);
    
    const avgResponseTime = this.stats.totalRequests > 0 
      ? this.stats.totalResponseTime / this.stats.totalRequests 
      : 0;
    
    const poolUtilization = this.config.maxBrowsers > 0 
      ? (this.browserPool.size / this.config.maxBrowsers) * 100 
      : 0;
    
    return {
      totalBrowsers: this.browserPool.size,
      activeBrowsers,
      idleBrowsers: this.browserPool.size - activeBrowsers,
      totalPages,
      totalRequests: this.stats.totalRequests,
      averageResponseTime: Math.round(avgResponseTime),
      healthyBrowsers,
      poolUtilization: Math.round(poolUtilization * 100) / 100,
      memoryUsage: process.memoryUsage()
    };
  }
  
  // Get detailed browser information
  getBrowserInfo(): Array<{
    id: string;
    createdAt: number;
    lastUsed: number;
    requestCount: number;
    isActive: boolean;
    isHealthy: boolean;
    activePagesCount: number;
    ageMinutes: number;
    idleMinutes: number;
  }> {
    const now = Date.now();
    
    return Array.from(this.browserPool.values()).map(instance => ({
      id: instance.id,
      createdAt: instance.createdAt,
      lastUsed: instance.lastUsed,
      requestCount: instance.requestCount,
      isActive: instance.isActive,
      isHealthy: instance.isHealthy,
      activePagesCount: instance.activePagesCount,
      ageMinutes: Math.round((now - instance.createdAt) / 60000),
      idleMinutes: Math.round((now - instance.lastUsed) / 60000)
    }));
  }
  
  // Manual cleanup methods
  async recycleBrowser(browserId: string): Promise<void> {
    const instance = this.browserPool.get(browserId);
    if (instance && !instance.isActive) {
      await this.destroyBrowser(browserId);
      
      // Create replacement
      if (this.browserPool.size < this.config.minBrowsers) {
        await this.createBrowser();
      }
    }
  }
  
  async recycleAllBrowsers(): Promise<void> {
    console.log('♻️ Recycling all browser instances...');
    
    const idleBrowsers = Array.from(this.browserPool.values())
      .filter(instance => !instance.isActive)
      .map(instance => instance.id);
    
    for (const id of idleBrowsers) {
      await this.destroyBrowser(id);
    }
    
    // Recreate minimum browsers
    await this.initializeMinBrowsers();
  }
  
  // Cleanup method
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up Browser Pool Manager...');
    
    // Clear timers
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    
    // Reject all queued requests
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift()!;
      request.reject(new Error('Browser pool shutting down'));
    }
    
    // Close all browsers
    const closePromises = Array.from(this.browserPool.keys()).map(id => 
      this.destroyBrowser(id)
    );
    
    await Promise.allSettled(closePromises);
    
    console.log(`✅ Browser Pool cleanup complete. Stats: ${this.stats.createdBrowsers} created, ${this.stats.destroyedBrowsers} destroyed`);
  }
}