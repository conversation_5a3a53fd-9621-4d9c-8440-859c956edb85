#!/usr/bin/env python3
"""
Python bridge for the AI agent functionality
This script provides a subprocess interface for the TypeScript MCP server
to interact with the Python AI agent.
"""

import sys
import json
import asyncio
import os
from typing import Dict, Any

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(__file__))

from agent import ChatAgent, AgentConfig, ChatSession

class PythonBridge:
    def __init__(self):
        self.config = AgentConfig(
            model="gpt-4-turbo-preview",
            temperature=0.7,
            max_tokens=4000
        )
        self.agent = ChatAgent(self.config)
        self.sessions: Dict[str, ChatSession] = {}
    
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle incoming requests from TypeScript"""
        try:
            action = request.get("action")
            
            if action == "create_session":
                session = await self.agent.create_session()
                self.sessions[session.id] = session
                return {
                    "success": True,
                    "data": {
                        "session_id": session.id,
                        "created_at": session.messages[0].timestamp.isoformat()
                    }
                }
            
            elif action == "chat":
                session_id = request.get("session_id")
                message = request.get("message")
                
                if not session_id or not message:
                    return {"success": False, "error": "Missing session_id or message"}
                
                if session_id not in self.sessions:
                    # Create session if it doesn't exist
                    session = await self.agent.create_session()
                    self.sessions[session.id] = session
                    session_id = session.id
                
                response = await self.agent.process_message(session_id, message)
                
                return {
                    "success": True,
                    "data": {
                        "session_id": session_id,
                        "response": response["message"],
                        "metadata": response.get("metadata", {})
                    }
                }
            
            elif action == "get_session":
                session_id = request.get("session_id")
                session = self.sessions.get(session_id)
                
                if not session:
                    return {"success": False, "error": "Session not found"}
                
                return {
                    "success": True,
                    "data": {
                        "session_id": session.id,
                        "message_count": len(session.messages),
                        "has_active_report": session.active_report is not None,
                        "context_keys": list(session.context.keys())
                    }
                }
            
            elif action == "export_report":
                session_id = request.get("session_id")
                format_type = request.get("format", "json")
                
                session = self.sessions.get(session_id)
                if not session or not session.active_report:
                    return {"success": False, "error": "No active report found"}
                
                exported = self.agent.report_generator.export_report(
                    session.active_report, 
                    format_type
                )
                
                return {
                    "success": True,
                    "data": {
                        "report": exported,
                        "format": format_type,
                        "report_id": session.active_report.id
                    }
                }
            
            elif action == "get_trending":
                sources = request.get("sources", ["both"])
                limit = request.get("limit", 10)
                
                trending = await self.agent.scraping_coordinator.discover_trending_keywords(
                    sources=sources,
                    limit=limit
                )
                
                return {
                    "success": True,
                    "data": {
                        "trending": [t.dict() for t in trending],
                        "sources": sources,
                        "timestamp": asyncio.get_event_loop().time()
                    }
                }
            
            elif action == "analyze_keywords":
                keywords = request.get("keywords", [])
                sources = request.get("sources", ["both"])
                
                if not keywords:
                    return {"success": False, "error": "No keywords provided"}
                
                stats = await self.agent.scraping_coordinator.get_keyword_statistics(
                    keywords=keywords,
                    sources=sources
                )
                
                return {
                    "success": True,
                    "data": {
                        "statistics": stats,
                        "keywords": keywords,
                        "sources": sources
                    }
                }
            
            else:
                return {"success": False, "error": f"Unknown action: {action}"}
        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "type": type(e).__name__
            }

async def main():
    """Main function to handle stdio communication"""
    bridge = PythonBridge()
    
    # Read from stdin line by line
    while True:
        try:
            line = sys.stdin.readline().strip()
            if not line:
                break
            
            # Parse JSON request
            request = json.loads(line)
            
            # Process request
            response = await bridge.handle_request(request)
            
            # Send response
            print(json.dumps(response), flush=True)
            
        except json.JSONDecodeError as e:
            error_response = {
                "success": False,
                "error": f"Invalid JSON: {str(e)}"
            }
            print(json.dumps(error_response), flush=True)
        
        except KeyboardInterrupt:
            break
        
        except Exception as e:
            error_response = {
                "success": False,
                "error": f"Unexpected error: {str(e)}"
            }
            print(json.dumps(error_response), flush=True)

if __name__ == "__main__":
    # Ensure we have required environment variables
    if not os.getenv("OPENAI_API_KEY"):
        print(json.dumps({
            "success": False,
            "error": "OPENAI_API_KEY environment variable is required"
        }), flush=True)
        sys.exit(1)
    
    # Run the main async function
    asyncio.run(main())