// Safe Scraping Orchestrator - Coordinates all safety systems for protected scraping

import { ScrapingConfig, SAFE_SCRAPING_CONFIGS } from './safe-scraper-config.js';
import { RequestMonitor } from './request-monitor.js';
import { SafeBrowserManager } from './safe-browser-manager.js';
import { DomainCircuitBreakers } from './circuit-breaker.js';

export interface SafeScrapingResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  cached?: boolean;
  fromFallback?: boolean;
  stats: {
    responseTime: number;
    attemptsUsed: number;
    circuitBreakerState: string;
  };
}

export class SafeScrapingOrchestrator {
  private config: ScrapingConfig;
  private monitor: RequestMonitor;
  private browserManager: SafeBrowserManager;
  private circuitBreakers: DomainCircuitBreakers;
  private cache: Map<string, { data: unknown; timestamp: number }> = new Map();
  private cacheTimeout = 600000; // 10 minutes default cache

  constructor(safetyMode: 'strict' | 'moderate' | 'aggressive' = 'strict') {
    this.config = SAFE_SCRAPING_CONFIGS[safetyMode];
    this.monitor = new RequestMonitor(this.config);
    this.browserManager = new SafeBrowserManager(this.config, this.monitor);
    this.circuitBreakers = new DomainCircuitBreakers();
    
    console.log(`🛡️  SafeScrapingOrchestrator initialized in ${safetyMode.toUpperCase()} mode`);
    this.logSafetySettings();
  }

  private logSafetySettings(): void {
    console.log(`📊 Safety Configuration:
    • Request Delays: ${this.config.minDelay}ms - ${this.config.maxDelay}ms
    • Daily Limit: ${this.config.dailyRequestLimit} requests per target
    • Hourly Limit: ${this.config.hourlyRequestLimit} requests per target  
    • Max Concurrent: ${this.config.maxConcurrent}
    • Error Threshold: ${this.config.errorThreshold}%
    • User Agent Rotation: ${this.config.userAgentRotation}
    • Human-like Timing: ${this.config.humanLikeTiming}`);
  }

  async safeRequest<T>(
    requestFn: () => Promise<T>,
    options: {
      domain: string;
      target: string;
      cacheKey?: string;
      fallbackFn?: () => Promise<T>;
      maxRetries?: number;
    }
  ): Promise<SafeScrapingResult<T>> {
    const startTime = Date.now();
    let attemptsUsed = 0;
    const _maxRetries = options.maxRetries ?? this.config.maxRetries;

    // Check cache first
    if (options.cacheKey) {
      const cached = this.getCachedData<T>(options.cacheKey);
      if (cached) {
        return {
          success: true,
          data: cached,
          cached: true,
          stats: {
            responseTime: Date.now() - startTime,
            attemptsUsed: 0,
            circuitBreakerState: 'N/A'
          }
        };
      }
    }

    // Check if we can make this request
    const canRequest = await this.monitor.canMakeRequest(options.domain, options.target);
    if (!canRequest.allowed) {
      // Try fallback if available
      if (options.fallbackFn) {
        try {
          const fallbackData = await options.fallbackFn();
          return {
            success: true,
            data: fallbackData,
            fromFallback: true,
            stats: {
              responseTime: Date.now() - startTime,
              attemptsUsed: 0,
              circuitBreakerState: 'N/A'
            }
          };
        } catch (fallbackError) {
          return {
            success: false,
            error: `Rate limited and fallback failed: ${canRequest.reason}`,
            stats: {
              responseTime: Date.now() - startTime,
              attemptsUsed: 0,
              circuitBreakerState: 'N/A'
            }
          };
        }
      }

      return {
        success: false,
        error: `Request blocked: ${canRequest.reason}. Wait ${Math.ceil((canRequest.waitTime || 0) / 1000)}s`,
        stats: {
          responseTime: Date.now() - startTime,
          attemptsUsed: 0,
          circuitBreakerState: 'N/A'
        }
      };
    }

    // Check if system should pause (high error rate)
    if (this.monitor.shouldPauseAllRequests()) {
      if (options.fallbackFn) {
        try {
          const fallbackData = await options.fallbackFn();
          return {
            success: true,
            data: fallbackData,
            fromFallback: true,
            stats: {
              responseTime: Date.now() - startTime,
              attemptsUsed: 0,
              circuitBreakerState: 'PAUSED'
            }
          };
        } catch (fallbackError) {
          // Continue with cached data if available
          const staleCache = this.getCachedData<T>(options.cacheKey || '', 24 * 60 * 60 * 1000); // 24h stale cache
          if (staleCache) {
            return {
              success: true,
              data: staleCache,
              cached: true,
              stats: {
                responseTime: Date.now() - startTime,
                attemptsUsed: 0,
                circuitBreakerState: 'PAUSED'
              }
            };
          }
        }
      }

      return {
        success: false,
        error: 'System paused due to high error rate. Using circuit breaker protection.',
        stats: {
          responseTime: Date.now() - startTime,
          attemptsUsed: 0,
          circuitBreakerState: 'PAUSED'
        }
      };
    }

    // Execute request with circuit breaker
    const circuitBreaker = this.circuitBreakers.getBreaker(options.domain);
    const circuitBreakerState = circuitBreaker.getState();

    try {
      const result = await circuitBreaker.execute(
        async () => {
          attemptsUsed++;
          return await requestFn();
        },
        options.fallbackFn
      );

      // Cache successful result
      if (options.cacheKey) {
        this.setCachedData(options.cacheKey, result);
      }

      return {
        success: true,
        data: result,
        stats: {
          responseTime: Date.now() - startTime,
          attemptsUsed,
          circuitBreakerState: circuitBreakerState.state
        }
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      return {
        success: false,
        error: errorMessage,
        stats: {
          responseTime: Date.now() - startTime,
          attemptsUsed,
          circuitBreakerState: circuitBreakerState.state
        }
      };
    }
  }

  private getCachedData<T>(cacheKey: string, maxAge: number = this.cacheTimeout): T | null {
    const cached = this.cache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < maxAge) {
      return cached.data as T;
    }
    return null;
  }

  private setCachedData<T>(cacheKey: string, data: T): void {
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  async getSystemStatus(): Promise<{
    safetyMode: string;
    requestStats: unknown;
    circuitBreakerStates: unknown;
    browserSessions: unknown;
    cacheStats: {
      entries: number;
      hitRate: number;
    };
  }> {
    const requestStats = this.monitor.getStats();
    const circuitBreakerStates = this.circuitBreakers.getAllStates();
    const browserSessions = this.browserManager.getSessionInfo();

    return {
      safetyMode: this.config.safetyMode,
      requestStats,
      circuitBreakerStates,
      browserSessions,
      cacheStats: {
        entries: this.cache.size,
        hitRate: 0 // TODO: Implement hit rate tracking
      }
    };
  }

  async testConnection(domain: string, target: string = 'test'): Promise<SafeScrapingResult<string>> {
    const testUrl = domain.includes('reddit') 
      ? `https://www.reddit.com/robots.txt`
      : `https://${domain}/robots.txt`;

    return this.safeRequest(
      async () => {
        const { page, cleanup } = await this.browserManager.safePage(domain);
        try {
          const result = await this.browserManager.safeNavigate(page, testUrl, domain, target);
          if (result.success) {
            return `Connection test successful for ${domain}`;
          } else {
            throw new Error(result.error || 'Connection test failed');
          }
        } finally {
          await cleanup();
        }
      },
      {
        domain,
        target: `connection-test-${target}`,
        fallbackFn: async (): Promise<string> => `Fallback: ${domain} connection cannot be tested safely`
      }
    );
  }

  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up SafeScrapingOrchestrator...');
    await this.browserManager.cleanup();
    this.monitor.cleanup();
    this.cache.clear();
    console.log('✅ SafeScrapingOrchestrator cleanup complete');
  }

  // Emergency stop - immediately halt all scraping
  emergencyStop(): void {
    console.log('🚨 EMERGENCY STOP ACTIVATED');
    this.circuitBreakers.resetAll();
    // Force all circuit breakers to OPEN state
    setTimeout(() => {
      void this.cleanup();
    }, 1000);
  }

  // Reset all safety systems (use carefully!)
  resetSafetySystems(): void {
    console.log('🔄 Resetting all safety systems...');
    this.circuitBreakers.resetAll();
    this.cache.clear();
    console.log('✅ Safety systems reset complete');
  }
}