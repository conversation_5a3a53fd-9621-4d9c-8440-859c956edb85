// Request Monitoring & Rate Limiting System for Safe Scraping

import { ScrapingConfig, BLOCKING_INDICATORS, addJitter } from './safe-scraper-config.js';
import { writeFileSync, existsSync, readFileSync } from 'fs';

interface RequestLog {
  timestamp: number;
  domain: string;
  target: string;
  success: boolean;
  responseTime: number;
  statusCode?: number;
  errorType?: string;
  blocked?: boolean;
}

interface DomainStats {
  requests: number;
  successes: number;
  failures: number;
  lastRequest: number;
  isBlocked: boolean;
  blockUntil: number;
  consecutiveFailures: number;
}

export class RequestMonitor {
  private config: ScrapingConfig;
  private logs: RequestLog[] = [];
  private domainStats: Map<string, DomainStats> = new Map();
  private logFile: string;
  private dailyRequests: Map<string, number> = new Map();
  private hourlyRequests: Map<string, number> = new Map();
  private lastHourReset = Date.now();

  constructor(config: ScrapingConfig, logFile: string = './logs/scraping-monitor.json') {
    this.config = config;
    this.logFile = logFile;
    this.loadLogs();
    this.startHourlyReset();
  }

  private loadLogs(): void {
    try {
      if (existsSync(this.logFile)) {
        const data = JSON.parse(readFileSync(this.logFile, 'utf-8'));
        this.logs = data.logs || [];
        
        // Rebuild domain stats from logs
        this.logs.forEach(log => {
          this.updateDomainStats(log);
        });
      }
    } catch (error) {
      console.warn('Failed to load request logs:', error);
    }
  }

  private saveLogs(): void {
    try {
      const data = {
        logs: this.logs.slice(-1000), // Keep last 1000 logs
        domainStats: Object.fromEntries(this.domainStats),
        lastUpdated: new Date().toISOString()
      };
      writeFileSync(this.logFile, JSON.stringify(data, null, 2));
    } catch (error) {
      console.warn('Failed to save request logs:', error);
    }
  }

  private startHourlyReset(): void {
    setInterval(() => {
      const now = Date.now();
      if (now - this.lastHourReset >= 3600000) { // 1 hour
        this.hourlyRequests.clear();
        this.lastHourReset = now;
      }
    }, 60000); // Check every minute
  }

  private updateDomainStats(log: RequestLog): void {
    const domain = log.domain;
    const stats = this.domainStats.get(domain) || {
      requests: 0,
      successes: 0,
      failures: 0,
      lastRequest: 0,
      isBlocked: false,
      blockUntil: 0,
      consecutiveFailures: 0
    };

    stats.requests++;
    stats.lastRequest = log.timestamp;

    if (log.success) {
      stats.successes++;
      stats.consecutiveFailures = 0;
      stats.isBlocked = false;
    } else {
      stats.failures++;
      stats.consecutiveFailures++;

      // Check for blocking indicators
      if (log.blocked || stats.consecutiveFailures >= this.config.circuitBreakerThreshold) {
        stats.isBlocked = true;
        stats.blockUntil = Date.now() + this.config.cooldownPeriod;
      }
    }

    this.domainStats.set(domain, stats);
  }

  async canMakeRequest(domain: string, target: string): Promise<{ allowed: boolean; reason?: string; waitTime?: number }> {
    const now = Date.now();
    
    // Check if domain is blocked
    const stats = this.domainStats.get(domain);
    if (stats?.isBlocked && now < stats.blockUntil) {
      return {
        allowed: false,
        reason: 'Domain temporarily blocked due to errors',
        waitTime: stats.blockUntil - now
      };
    }

    // Check daily limits
    const dailyKey = `${target}_${new Date().toDateString()}`;
    const dailyCount = this.dailyRequests.get(dailyKey) ?? 0;
    if (dailyCount >= this.config.dailyRequestLimit) {
      return {
        allowed: false,
        reason: 'Daily request limit exceeded',
        waitTime: this.getMillisUntilTomorrow()
      };
    }

    // Check hourly limits  
    const hourlyKey = `${target}_${Math.floor(now / 3600000)}`;
    const hourlyCount = this.hourlyRequests.get(hourlyKey) ?? 0;
    if (hourlyCount >= this.config.hourlyRequestLimit) {
      return {
        allowed: false,
        reason: 'Hourly request limit exceeded',
        waitTime: 3600000 - (now % 3600000)
      };
    }

    // Check minimum delay between requests
    if (stats?.lastRequest) {
      const timeSinceLastRequest = now - stats.lastRequest;
      const minDelay = addJitter(this.config.minDelay, this.config.jitterFactor);
      
      if (timeSinceLastRequest < minDelay) {
        return {
          allowed: false,
          reason: 'Rate limiting - too soon since last request',
          waitTime: minDelay - timeSinceLastRequest
        };
      }
    }

    return { allowed: true };
  }

  async logRequest(request: Omit<RequestLog, 'timestamp'>): Promise<void> {
    const log: RequestLog = {
      ...request,
      timestamp: Date.now()
    };

    // Check for blocking indicators
    if (request.errorType) {
      const errorText = request.errorType.toLowerCase();
      log.blocked = BLOCKING_INDICATORS.some(indicator => 
        errorText.includes(indicator.toLowerCase())
      );
    }

    this.logs.push(log);
    this.updateDomainStats(log);

    // Update request counters
    const dailyKey = `${request.target}_${new Date().toDateString()}`;
    this.dailyRequests.set(dailyKey, (this.dailyRequests.get(dailyKey) ?? 0) + 1);

    const hourlyKey = `${request.target}_${Math.floor(Date.now() / 3600000)}`;
    this.hourlyRequests.set(hourlyKey, (this.hourlyRequests.get(hourlyKey) ?? 0) + 1);

    // Save logs periodically
    if (this.logs.length % 10 === 0) {
      this.saveLogs();
    }

    // Log to console if configured
    if (this.config.logAllRequests) {
      const status = log.success ? '✅' : '❌';
      const blocked = log.blocked ? ' [BLOCKED]' : '';
      console.info(`${status} ${log.domain}/${log.target} (${log.responseTime}ms)${blocked}`);
    }
  }

  getStats(): {
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    blockedDomains: string[];
    recentErrors: string[];
  } {
    const recentLogs = this.logs.slice(-100);
    const totalRequests = recentLogs.length;
    const successes = recentLogs.filter(log => log.success).length;
    const successRate = totalRequests > 0 ? (successes / totalRequests) * 100 : 0;
    
    const responseTimes = recentLogs
      .filter(log => log.responseTime > 0)
      .map(log => log.responseTime);
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0;

    const blockedDomains = Array.from(this.domainStats.entries())
      .filter(([_, stats]) => stats.isBlocked && Date.now() < stats.blockUntil)
      .map(([domain, _]) => domain);

    const recentErrors = recentLogs
      .filter(log => !log.success && log.errorType)
      .slice(-10)
      .map(log => log.errorType!);

    return {
      totalRequests,
      successRate,
      averageResponseTime,
      blockedDomains,
      recentErrors
    };
  }

  shouldPauseAllRequests(): boolean {
    const stats = this.getStats();
    return stats.successRate < (100 - this.config.errorThreshold);
  }

  private getMillisUntilTomorrow(): number {
    const now = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(now.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow.getTime() - now.getTime();
  }

  cleanup(): void {
    this.saveLogs();
  }
}