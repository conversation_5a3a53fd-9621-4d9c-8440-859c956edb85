// Circuit Breaker Pattern for Error Recovery and Graceful Degradation

export enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation
  OPEN = 'OPEN',         // Failing, reject requests
  HALF_OPEN = 'HALF_OPEN' // Testing if service recovered
}

interface CircuitBreakerConfig {
  failureThreshold: number;    // Number of failures before opening
  recoveryTimeout: number;     // Time before attempting recovery (ms)
  monitoringPeriod: number;    // Time window for failure counting (ms)
  halfOpenMaxCalls: number;    // Max calls to test in HALF_OPEN state
}

interface CallResult {
  success: boolean;
  timestamp: number;
  error?: string;
  responseTime?: number;
}

export class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private config: CircuitBreakerConfig;
  private failures: CallResult[] = [];
  private lastStateChange: number = Date.now();
  private halfOpenCalls: number = 0;
  private serviceName: string;

  constructor(serviceName: string, config: Partial<CircuitBreakerConfig> = {}) {
    this.serviceName = serviceName;
    this.config = {
      failureThreshold: config.failureThreshold || 5,
      recoveryTimeout: config.recoveryTimeout || 60000, // 1 minute
      monitoringPeriod: config.monitoringPeriod || 300000, // 5 minutes
      halfOpenMaxCalls: config.halfOpenMaxCalls || 3
    };
  }

  async execute<T>(fn: () => Promise<T>, fallback?: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
        this.halfOpenCalls = 0;
        this.log('Circuit breaker moving to HALF_OPEN state for recovery test');
      } else {
        const error = new Error(`Circuit breaker is OPEN for ${this.serviceName}. Service unavailable.`);
        if (fallback) {
          this.log('Circuit breaker OPEN - using fallback');
          return await fallback();
        }
        throw error;
      }
    }

    if (this.state === CircuitState.HALF_OPEN) {
      if (this.halfOpenCalls >= this.config.halfOpenMaxCalls) {
        throw new Error(`Circuit breaker HALF_OPEN call limit reached for ${this.serviceName}`);
      }
      this.halfOpenCalls++;
    }

    const startTime = Date.now();
    
    try {
      const result = await fn();
      
      // Record success
      this.recordResult({
        success: true,
        timestamp: Date.now(),
        responseTime: Date.now() - startTime
      });

      return result;

    } catch (error) {
      // Record failure
      this.recordResult({
        success: false,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : String(error),
        responseTime: Date.now() - startTime
      });

      // Use fallback if available
      if (fallback) {
        this.log(`Primary service failed, using fallback: ${String(error)}`);
        try {
          return await fallback();
        } catch (fallbackError) {
          this.log(`Fallback also failed: ${String(fallbackError)}`);
        }
      }

      throw error;
    }
  }

  private recordResult(result: CallResult): void {
    this.failures.push(result);
    this.cleanupOldResults();

    if (result.success) {
      if (this.state === CircuitState.HALF_OPEN) {
        // Success in HALF_OPEN means we can close the circuit
        this.state = CircuitState.CLOSED;
        this.failures = []; // Clear failure history
        this.lastStateChange = Date.now();
        this.log('Circuit breaker closing - service recovered');
      }
    } else {
      // Count recent failures
      const recentFailures = this.getRecentFailures();
      
      if (this.state === CircuitState.CLOSED && recentFailures >= this.config.failureThreshold) {
        this.state = CircuitState.OPEN;
        this.lastStateChange = Date.now();
        this.log(`Circuit breaker opening - ${recentFailures} failures exceeded threshold`);
      } else if (this.state === CircuitState.HALF_OPEN) {
        // Failure in HALF_OPEN means we go back to OPEN
        this.state = CircuitState.OPEN;
        this.lastStateChange = Date.now();
        this.log('Circuit breaker reopening - recovery test failed');
      }
    }
  }

  private getRecentFailures(): number {
    const cutoffTime = Date.now() - this.config.monitoringPeriod;
    return this.failures.filter(f => 
      !f.success && f.timestamp > cutoffTime
    ).length;
  }

  private cleanupOldResults(): void {
    const cutoffTime = Date.now() - this.config.monitoringPeriod;
    this.failures = this.failures.filter(f => f.timestamp > cutoffTime);
  }

  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastStateChange >= this.config.recoveryTimeout;
  }

  private log(message: string): void {
    console.log(`[CircuitBreaker:${this.serviceName}] ${message}`);
  }

  getState(): {
    state: CircuitState;
    failureCount: number;
    lastStateChange: Date;
    nextRetryTime?: Date;
    stats: {
      totalCalls: number;
      successRate: number;
      averageResponseTime: number;
    };
  } {
    const now = Date.now();
    const recentCalls = this.failures.filter(f => f.timestamp > now - this.config.monitoringPeriod);
    const successes = recentCalls.filter(f => f.success).length;
    const totalCalls = recentCalls.length;
    const successRate = totalCalls > 0 ? (successes / totalCalls) * 100 : 0;
    
    const responseTimes = recentCalls
      .filter(f => f.responseTime !== undefined)
      .map(f => f.responseTime!);
    const averageResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      : 0;

    const nextRetryTime = this.state === CircuitState.OPEN
      ? new Date(this.lastStateChange + this.config.recoveryTimeout)
      : undefined;

    return {
      state: this.state,
      failureCount: this.getRecentFailures(),
      lastStateChange: new Date(this.lastStateChange),
      nextRetryTime,
      stats: {
        totalCalls,
        successRate,
        averageResponseTime
      }
    };
  }

  reset(): void {
    this.state = CircuitState.CLOSED;
    this.failures = [];
    this.halfOpenCalls = 0;
    this.lastStateChange = Date.now();
    this.log('Circuit breaker manually reset');
  }
}

// Domain-specific circuit breakers for different services
export class DomainCircuitBreakers {
  private breakers: Map<string, CircuitBreaker> = new Map();
  
  getBreaker(domain: string): CircuitBreaker {
    if (!this.breakers.has(domain)) {
      // Configure different thresholds for different domains
      let config: Partial<CircuitBreakerConfig> = {};
      
      if (domain.includes('x.com') || domain.includes('twitter.com')) {
        config = {
          failureThreshold: 3,    // Twitter is stricter
          recoveryTimeout: 300000, // 5 minutes
          halfOpenMaxCalls: 1     // Very conservative
        };
      } else if (domain.includes('reddit.com')) {
        config = {
          failureThreshold: 5,    // Reddit more lenient
          recoveryTimeout: 180000, // 3 minutes
          halfOpenMaxCalls: 2
        };
      }
      
      this.breakers.set(domain, new CircuitBreaker(domain, config));
    }
    
    return this.breakers.get(domain)!;
  }

  getAllStates(): Record<string, any> {
    const states: Record<string, any> = {};
    
    for (const [domain, breaker] of this.breakers.entries()) {
      states[domain] = breaker.getState();
    }
    
    return states;
  }

  resetAll(): void {
    for (const breaker of this.breakers.values()) {
      breaker.reset();
    }
  }
}