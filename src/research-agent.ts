/**
 * Research Agent for Continuous Improvement
 * 
 * Automated research system that:
 * - Monitors anti-bot detection trends and new techniques
 * - Researches emerging scraping methodologies
 * - Benchmarks against industry standards and competitors
 * - Generates implementation proposals for new techniques
 * - Tracks technology evolution and provides strategic insights
 * - Maintains knowledge base of best practices and innovations
 */

import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { LLMOrchestrator } from './llm-orchestrator.js';
import axios from 'axios';

interface ResearchTopic {
  id: string;
  name: string;
  category: 'anti_detection' | 'performance' | 'quality' | 'industry_trends' | 'technology' | 'compliance';
  keywords: string[];
  sources: ResearchSource[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  lastResearched: number;
  researchFrequency: number; // milliseconds between research cycles
}

interface ResearchSource {
  type: 'web_search' | 'github' | 'blog' | 'documentation' | 'forum' | 'academic';
  url?: string;
  searchQuery?: string;
  reliability: number; // 0-1 scale
  lastChecked: number;
}

interface ResearchFinding {
  id: string;
  topicId: string;
  title: string;
  summary: string;
  content: string;
  source: ResearchSource;
  timestamp: number;
  relevanceScore: number; // 0-1 scale
  implementationDifficulty: 'low' | 'medium' | 'high';
  potentialImpact: {
    successRate: number;
    blockRate: number;
    performance: number;
    cost: number;
  };
  tags: string[];
  status: 'new' | 'reviewed' | 'implemented' | 'dismissed';
}

interface TechnologyTrend {
  name: string;
  category: string;
  description: string;
  adoptionRate: number;
  maturity: 'emerging' | 'growing' | 'mature' | 'declining';
  relevance: number;
  implementationCost: number;
  timeToImplement: number; // days
  risks: string[];
  benefits: string[];
  competitors: string[]; // Who's using this
}

interface BenchmarkReport {
  timestamp: number;
  competitors: Array<{
    name: string;
    capabilities: string[];
    performance: {
      successRate?: number;
      blockRate?: number;
      coverage?: number;
      cost?: number;
    };
    strengths: string[];
    weaknesses: string[];
    differentiators: string[];
  }>;
  industryAverages: {
    successRate: number;
    blockRate: number;
    avgResponseTime: number;
    costPerRequest: number;
  };
  ourPosition: {
    strengths: string[];
    gaps: string[];
    opportunities: string[];
    threats: string[];
  };
  recommendations: Array<{
    priority: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    expectedImpact: string;
    effort: string;
  }>;
}

interface ImplementationProposal {
  id: string;
  findingId: string;
  title: string;
  description: string;
  technicalApproach: string;
  benefits: string[];
  risks: string[];
  effort: {
    development: number; // hours
    testing: number;     // hours
    deployment: number;  // hours
  };
  timeline: string;
  dependencies: string[];
  successMetrics: string[];
  rolloutStrategy: string;
  status: 'draft' | 'proposed' | 'approved' | 'in_development' | 'completed';
}

export class ResearchAgent {
  private llmOrchestrator: LLMOrchestrator;
  private dataDir: string;
  
  private researchTopics: Map<string, ResearchTopic> = new Map();
  private findings: Map<string, ResearchFinding> = new Map();
  private trends: Map<string, TechnologyTrend> = new Map();
  private benchmarkReports: BenchmarkReport[] = [];
  private proposals: Map<string, ImplementationProposal> = new Map();
  
  private researchSchedule: Map<string, NodeJS.Timeout> = new Map();
  private knowledgeBase: string = '';

  constructor(
    llmOrchestrator: LLMOrchestrator,
    dataDir: string = './data/research'
  ) {
    this.llmOrchestrator = llmOrchestrator;
    this.dataDir = dataDir;
    
    this.ensureDataDir();
    this.initializeResearchTopics();
    this.loadHistoricalData();
    this.startResearchSchedule();
  }

  private ensureDataDir(): void {
    if (!existsSync(this.dataDir)) {
      mkdirSync(this.dataDir, { recursive: true });
    }
  }

  private initializeResearchTopics(): void {
    const topics: ResearchTopic[] = [
      {
        id: 'anti_bot_trends',
        name: 'Anti-Bot Detection Trends',
        category: 'anti_detection',
        keywords: ['bot detection', 'anti scraping', 'cloudflare', 'datadome', 'imperva', 'fingerprinting'],
        sources: [
          { type: 'web_search', searchQuery: 'anti bot detection 2025 trends', reliability: 0.8, lastChecked: 0 },
          { type: 'github', searchQuery: 'bot detection cloudflare', reliability: 0.9, lastChecked: 0 },
          { type: 'blog', url: 'https://blog.cloudflare.com', reliability: 0.9, lastChecked: 0 }
        ],
        priority: 'critical',
        lastResearched: 0,
        researchFrequency: 7 * 24 * 60 * 60 * 1000 // Weekly
      },
      {
        id: 'browser_fingerprinting',
        name: 'Browser Fingerprinting Techniques',
        category: 'anti_detection',
        keywords: ['browser fingerprinting', 'canvas fingerprinting', 'webgl fingerprinting', 'audio fingerprinting'],
        sources: [
          { type: 'academic', searchQuery: 'browser fingerprinting techniques', reliability: 0.95, lastChecked: 0 },
          { type: 'github', searchQuery: 'fingerprinting evasion', reliability: 0.9, lastChecked: 0 }
        ],
        priority: 'high',
        lastResearched: 0,
        researchFrequency: 14 * 24 * 60 * 60 * 1000 // Bi-weekly
      },
      {
        id: 'scraping_performance',
        name: 'Scraping Performance Optimization',
        category: 'performance',
        keywords: ['web scraping performance', 'concurrent scraping', 'rate limiting', 'proxy optimization'],
        sources: [
          { type: 'web_search', searchQuery: 'web scraping performance optimization 2025', reliability: 0.8, lastChecked: 0 },
          { type: 'documentation', url: 'https://docs.scrapy.org', reliability: 0.9, lastChecked: 0 }
        ],
        priority: 'medium',
        lastResearched: 0,
        researchFrequency: 30 * 24 * 60 * 60 * 1000 // Monthly
      },
      {
        id: 'industry_benchmarks',
        name: 'Industry Benchmarks and Standards',
        category: 'industry_trends',
        keywords: ['scraping industry standards', 'bright data', 'apify', 'scrapfly', 'proxymesh'],
        sources: [
          { type: 'web_search', searchQuery: 'web scraping industry benchmarks 2025', reliability: 0.8, lastChecked: 0 },
          { type: 'blog', searchQuery: 'scraping service comparison', reliability: 0.7, lastChecked: 0 }
        ],
        priority: 'high',
        lastResearched: 0,
        researchFrequency: 21 * 24 * 60 * 60 * 1000 // Every 3 weeks
      },
      {
        id: 'compliance_updates',
        name: 'Legal and Compliance Updates',
        category: 'compliance',
        keywords: ['web scraping legal', 'robots.txt', 'gdpr scraping', 'terms of service'],
        sources: [
          { type: 'web_search', searchQuery: 'web scraping legal updates 2025', reliability: 0.9, lastChecked: 0 },
          { type: 'forum', searchQuery: 'scraping compliance reddit', reliability: 0.6, lastChecked: 0 }
        ],
        priority: 'medium',
        lastResearched: 0,
        researchFrequency: 60 * 24 * 60 * 60 * 1000 // Every 2 months
      }
    ];

    topics.forEach(topic => {
      this.researchTopics.set(topic.id, topic);
    });

    console.log(`🔬 Research Agent initialized with ${topics.length} research topics`);
  }

  private loadHistoricalData(): void {
    try {
      const files = ['findings.json', 'trends.json', 'benchmarks.json', 'proposals.json'];
      
      files.forEach(file => {
        const filePath = join(this.dataDir, file);
        if (existsSync(filePath)) {
          const data = JSON.parse(readFileSync(filePath, 'utf-8'));
          
          switch (file) {
            case 'findings.json':
              data.findings?.forEach((finding: ResearchFinding) => {
                this.findings.set(finding.id, finding);
              });
              break;
            case 'trends.json':
              data.trends?.forEach((trend: TechnologyTrend) => {
                this.trends.set(trend.name, trend);
              });
              break;
            case 'benchmarks.json':
              this.benchmarkReports = data.reports || [];
              break;
            case 'proposals.json':
              data.proposals?.forEach((proposal: ImplementationProposal) => {
                this.proposals.set(proposal.id, proposal);
              });
              break;
          }
        }
      });

      console.log(`📊 Research data loaded: ${this.findings.size} findings, ${this.trends.size} trends, ${this.benchmarkReports.length} benchmarks`);
    } catch (error) {
      console.warn('Failed to load research data:', error);
    }
  }

  private startResearchSchedule(): void {
    for (const topic of this.researchTopics.values()) {
      // Schedule initial research if never done
      if (topic.lastResearched === 0) {
        setTimeout(() => {
          void this.conductResearch(topic.id);
        }, Math.random() * 60000); // Randomize initial research within 1 minute
      }
      
      // Schedule periodic research
      const interval = setInterval(() => {
        void this.conductResearch(topic.id);
      }, topic.researchFrequency);
      
      this.researchSchedule.set(topic.id, interval);
    }
    
    console.log(`⏰ Research schedule started for ${this.researchTopics.size} topics`);
  }

  async conductResearch(topicId: string): Promise<ResearchFinding[]> {
    const topic = this.researchTopics.get(topicId);
    if (!topic) {
      console.error(`Research topic not found: ${topicId}`);
      return [];
    }

    console.log(`🔍 Conducting research on: ${topic.name}`);
    
    const findings: ResearchFinding[] = [];
    
    // Research each source
    for (const source of topic.sources) {
      try {
        const sourceFindings = await this.researchSource(topic, source);
        findings.push(...sourceFindings);
      } catch (error) {
        console.warn(`Failed to research source for ${topic.name}:`, error);
      }
    }
    
    // Update topic research timestamp
    topic.lastResearched = Date.now();
    
    // Analyze and score findings
    const analyzedFindings = await this.analyzeFindings(findings);
    
    // Store findings
    analyzedFindings.forEach(finding => {
      this.findings.set(finding.id, finding);
    });
    
    // Generate implementation proposals for high-value findings
    const highValueFindings = analyzedFindings.filter(f => f.relevanceScore > 0.7 && f.status === 'new');
    for (const finding of highValueFindings) {
      const proposal = await this.generateImplementationProposal(finding);
      if (proposal) {
        this.proposals.set(proposal.id, proposal);
      }
    }
    
    this.persistData();
    
    console.log(`✅ Research completed: ${analyzedFindings.length} findings, ${highValueFindings.length} high-value`);
    return analyzedFindings;
  }

  private async researchSource(topic: ResearchTopic, source: ResearchSource): Promise<ResearchFinding[]> {
    const findings: ResearchFinding[] = [];
    
    // Simulate research based on source type
    // In production, this would use actual APIs and web scraping
    
    switch (source.type) {
      case 'web_search':
        findings.push(...await this.simulateWebSearch(topic, source));
        break;
      case 'github':
        findings.push(...await this.simulateGithubSearch(topic, source));
        break;
      case 'blog':
        findings.push(...await this.simulateBlogResearch(topic, source));
        break;
      case 'academic':
        findings.push(...await this.simulateAcademicResearch(topic, source));
        break;
      default:
        console.warn(`Unknown source type: ${source.type}`);
    }
    
    source.lastChecked = Date.now();
    return findings;
  }

  private async simulateWebSearch(topic: ResearchTopic, source: ResearchSource): Promise<ResearchFinding[]> {
    // Simulate finding recent trends and techniques
    const simulatedFindings = [
      {
        title: 'New Canvas Fingerprinting Protection Technique',
        summary: 'Advanced method to add noise to canvas fingerprinting while maintaining visual fidelity',
        content: 'Research shows that adding subtle pixel-level noise to canvas elements can effectively prevent fingerprinting while being imperceptible to human users.',
        relevanceScore: 0.85,
        implementationDifficulty: 'medium' as const,
        potentialImpact: { successRate: 5, blockRate: -15, performance: -2, cost: 0 },
        tags: ['canvas', 'fingerprinting', 'evasion']
      },
      {
        title: 'HTTP/3 Adoption in Anti-Bot Systems',
        summary: 'Major CDNs are implementing HTTP/3-based bot detection',
        content: 'Cloudflare and other CDNs are now using HTTP/3 protocol characteristics to identify automated clients. Scrapers need to implement proper HTTP/3 support.',
        relevanceScore: 0.92,
        implementationDifficulty: 'high' as const,
        potentialImpact: { successRate: 10, blockRate: -20, performance: 5, cost: -10 },
        tags: ['http3', 'protocol', 'cloudflare']
      }
    ];

    return simulatedFindings.map(finding => ({
      id: `finding_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      topicId: topic.id,
      title: finding.title,
      summary: finding.summary,
      content: finding.content,
      source,
      timestamp: Date.now(),
      relevanceScore: finding.relevanceScore,
      implementationDifficulty: finding.implementationDifficulty,
      potentialImpact: finding.potentialImpact,
      tags: finding.tags,
      status: 'new' as const
    }));
  }

  private async simulateGithubSearch(topic: ResearchTopic, source: ResearchSource): Promise<ResearchFinding[]> {
    // Simulate finding open source tools and techniques
    const simulatedFindings = [
      {
        title: 'Advanced Puppeteer Stealth Plugin Updates',
        summary: 'New evasion techniques added to puppeteer-extra-plugin-stealth',
        content: 'Recent commits show improved WebRTC fingerprinting protection and better user agent spoofing mechanisms.',
        relevanceScore: 0.88,
        implementationDifficulty: 'low' as const,
        potentialImpact: { successRate: 8, blockRate: -12, performance: 0, cost: 0 },
        tags: ['puppeteer', 'stealth', 'webrtc']
      }
    ];

    return simulatedFindings.map(finding => ({
      id: `finding_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      topicId: topic.id,
      title: finding.title,
      summary: finding.summary,
      content: finding.content,
      source,
      timestamp: Date.now(),
      relevanceScore: finding.relevanceScore,
      implementationDifficulty: finding.implementationDifficulty,
      potentialImpact: finding.potentialImpact,
      tags: finding.tags,
      status: 'new' as const
    }));
  }

  private async simulateBlogResearch(topic: ResearchTopic, source: ResearchSource): Promise<ResearchFinding[]> {
    // Simulate findings from industry blogs
    return [];
  }

  private async simulateAcademicResearch(topic: ResearchTopic, source: ResearchSource): Promise<ResearchFinding[]> {
    // Simulate findings from academic papers
    return [];
  }

  private async analyzeFindings(findings: ResearchFinding[]): Promise<ResearchFinding[]> {
    // Use LLM to analyze and enhance findings
    for (const finding of findings) {
      try {
        const analysisPrompt = `Analyze this research finding and provide enhanced insights:

Title: ${finding.title}
Summary: ${finding.summary}
Content: ${finding.content}
Tags: ${finding.tags.join(', ')}

Please respond with JSON:
{
  "enhancedSummary": "Improved summary with technical details",
  "relevanceScore": 0.85,
  "implementationComplexity": "low|medium|high",
  "strategicImportance": "Brief explanation of strategic importance",
  "relatedTechnologies": ["tech1", "tech2"],
  "competitorAdoption": "Analysis of competitor adoption"
}`;

        const response = await this.llmOrchestrator['callLLM']('smart', analysisPrompt);
        const analysis = this.parseLLMResponse(response);
        
        if (analysis) {
          finding.summary = analysis.enhancedSummary || finding.summary;
          finding.relevanceScore = analysis.relevanceScore || finding.relevanceScore;
          finding.tags = [...finding.tags, ...(analysis.relatedTechnologies || [])];
        }
      } catch (error) {
        console.warn(`Failed to analyze finding: ${finding.title}`, error);
      }
    }
    
    return findings;
  }

  private parseLLMResponse(response: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      return jsonMatch ? JSON.parse(jsonMatch[0]) : null;
    } catch (error) {
      return null;
    }
  }

  private async generateImplementationProposal(finding: ResearchFinding): Promise<ImplementationProposal | null> {
    const proposalPrompt = `Generate an implementation proposal for this research finding:

Title: ${finding.title}
Summary: ${finding.summary}
Implementation Difficulty: ${finding.implementationDifficulty}
Potential Impact: Success Rate +${finding.potentialImpact.successRate}%, Block Rate ${finding.potentialImpact.blockRate}%

Respond with JSON:
{
  "title": "Implementation proposal title",
  "description": "Detailed implementation description",
  "technicalApproach": "Technical approach and architecture",
  "benefits": ["benefit1", "benefit2"],
  "risks": ["risk1", "risk2"],
  "effort": {"development": 16, "testing": 8, "deployment": 4},
  "timeline": "2-3 weeks",
  "dependencies": ["dependency1", "dependency2"],
  "successMetrics": ["metric1", "metric2"],
  "rolloutStrategy": "Phased rollout approach"
}`;

    try {
      const response = await this.llmOrchestrator['callLLM']('smart', proposalPrompt);
      const proposal = this.parseLLMResponse(response);
      
      if (proposal) {
        return {
          id: `proposal_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
          findingId: finding.id,
          title: proposal.title,
          description: proposal.description,
          technicalApproach: proposal.technicalApproach,
          benefits: proposal.benefits || [],
          risks: proposal.risks || [],
          effort: proposal.effort || { development: 8, testing: 4, deployment: 2 },
          timeline: proposal.timeline || '1-2 weeks',
          dependencies: proposal.dependencies || [],
          successMetrics: proposal.successMetrics || [],
          rolloutStrategy: proposal.rolloutStrategy || 'Gradual rollout with monitoring',
          status: 'draft'
        };
      }
    } catch (error) {
      console.warn('Failed to generate implementation proposal:', error);
    }

    return null;
  }

  async generateBenchmarkReport(): Promise<BenchmarkReport> {
    console.log('📊 Generating industry benchmark report...');
    
    // Simulate competitor analysis
    const competitors = [
      {
        name: 'Bright Data',
        capabilities: ['Residential proxies', 'Web Unlocking', 'CAPTCHA solving', 'Browser automation'],
        performance: { successRate: 98, blockRate: 0.5, coverage: 95, cost: 0.02 },
        strengths: ['Largest proxy network', 'Advanced unblocking', 'Enterprise features'],
        weaknesses: ['High cost', 'Complex pricing'],
        differentiators: ['150M+ IPs', 'Automatic retry logic', 'Real-time optimization']
      },
      {
        name: 'Apify',
        capabilities: ['Cloud scrapers', 'Proxy rotation', 'Data pipelines', 'Scheduled runs'],
        performance: { successRate: 95, blockRate: 2, coverage: 90, cost: 0.015 },
        strengths: ['Developer-friendly', 'Pre-built scrapers', 'Good documentation'],
        weaknesses: ['Limited customization', 'Smaller proxy pool'],
        differentiators: ['Actor marketplace', 'Serverless architecture', 'Easy scaling']
      },
      {
        name: 'ScrapFly',
        capabilities: ['Anti-bot bypass', 'JavaScript rendering', 'Screenshot API', 'Webhook delivery'],
        performance: { successRate: 94, blockRate: 3, coverage: 85, cost: 0.01 },
        strengths: ['Cost effective', 'Good performance', 'Simple API'],
        weaknesses: ['Smaller scale', 'Limited enterprise features'],
        differentiators: ['Cost-performance ratio', 'Developer API', 'Fast response times']
      }
    ];

    const industryAverages = {
      successRate: 95.7,
      blockRate: 1.8,
      avgResponseTime: 2500,
      costPerRequest: 0.015
    };

    // Analyze our current position (would use actual metrics)
    const ourPosition = {
      strengths: [
        'LLM-powered intelligence',
        'Advanced visual analysis',
        'Comprehensive monitoring',
        'Quality assurance framework'
      ],
      gaps: [
        'Proxy network size',
        'Global coverage',
        'Enterprise scalability',
        'Managed service options'
      ],
      opportunities: [
        'AI-powered optimization',
        'Cost leadership',
        'Specialized verticals',
        'Open source community'
      ],
      threats: [
        'Increasing competition',
        'Platform restrictions',
        'Legal compliance requirements',
        'Technology evolution speed'
      ]
    };

    const recommendations = [
      {
        priority: 'critical' as const,
        description: 'Expand proxy network partnerships',
        expectedImpact: 'Reduce block rate to <1%',
        effort: '3-4 months development'
      },
      {
        priority: 'high' as const,
        description: 'Implement managed service tier',
        expectedImpact: 'Access enterprise market segment',
        effort: '2-3 months development'
      },
      {
        priority: 'medium' as const,
        description: 'Enhance global geographic coverage',
        expectedImpact: 'Improve success rate by 2-3%',
        effort: '1-2 months partnerships'
      }
    ];

    const report: BenchmarkReport = {
      timestamp: Date.now(),
      competitors,
      industryAverages,
      ourPosition,
      recommendations
    };

    this.benchmarkReports.push(report);
    
    // Keep only last 12 reports
    if (this.benchmarkReports.length > 12) {
      this.benchmarkReports = this.benchmarkReports.slice(-12);
    }
    
    this.persistData();
    
    console.log('✅ Benchmark report generated');
    return report;
  }

  async identifyTechnologyTrends(): Promise<TechnologyTrend[]> {
    console.log('📈 Analyzing technology trends...');
    
    const trends: TechnologyTrend[] = [
      {
        name: 'AI-Powered Bot Detection',
        category: 'Anti-Detection',
        description: 'Machine learning models trained on behavioral patterns to detect automated traffic',
        adoptionRate: 0.65,
        maturity: 'growing',
        relevance: 0.95,
        implementationCost: 50000,
        timeToImplement: 120,
        risks: ['Model training complexity', 'False positive rates', 'Ongoing model updates'],
        benefits: ['Higher detection accuracy', 'Adaptive learning', 'Reduced maintenance'],
        competitors: ['Cloudflare', 'DataDome', 'Imperva']
      },
      {
        name: 'HTTP/3 and QUIC Protocol',
        category: 'Infrastructure',
        description: 'Next-generation HTTP protocol with improved performance and security',
        adoptionRate: 0.35,
        maturity: 'emerging',
        relevance: 0.8,
        implementationCost: 25000,
        timeToImplement: 60,
        risks: ['Protocol complexity', 'Limited tooling', 'Compatibility issues'],
        benefits: ['Improved performance', 'Better connection reliability', 'Future-proofing'],
        competitors: ['Google', 'Cloudflare', 'Fastly']
      },
      {
        name: 'WebAssembly Bot Detection',
        category: 'Anti-Detection',
        description: 'Using WebAssembly modules for client-side bot detection and fingerprinting',
        adoptionRate: 0.15,
        maturity: 'emerging',
        relevance: 0.7,
        implementationCost: 30000,
        timeToImplement: 90,
        risks: ['Browser compatibility', 'Reverse engineering', 'Performance impact'],
        benefits: ['Harder to bypass', 'Client-side processing', 'Obfuscated logic'],
        competitors: ['DataDome', 'Kasada', 'PerimeterX']
      }
    ];

    // Store trends
    trends.forEach(trend => {
      this.trends.set(trend.name, trend);
    });
    
    this.persistData();
    
    console.log(`✅ Identified ${trends.length} technology trends`);
    return trends;
  }

  getResearchSummary(days: number = 30): {
    totalFindings: number;
    newFindings: number;
    highImpactFindings: number;
    implementedProposals: number;
    activeTrends: number;
    topCategories: Array<{ category: string; count: number }>;
    recentBenchmarks: BenchmarkReport[];
  } {
    const cutoff = Date.now() - (days * 24 * 60 * 60 * 1000);
    const allFindings = Array.from(this.findings.values());
    const recentFindings = allFindings.filter(f => f.timestamp > cutoff);
    
    const categoryCount = new Map<string, number>();
    allFindings.forEach(f => {
      const topic = this.researchTopics.get(f.topicId);
      if (topic) {
        const count = categoryCount.get(topic.category) || 0;
        categoryCount.set(topic.category, count + 1);
      }
    });

    const topCategories = Array.from(categoryCount.entries())
      .map(([category, count]) => ({ category, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalFindings: allFindings.length,
      newFindings: recentFindings.length,
      highImpactFindings: allFindings.filter(f => f.relevanceScore > 0.8).length,
      implementedProposals: Array.from(this.proposals.values()).filter(p => p.status === 'completed').length,
      activeTrends: Array.from(this.trends.values()).filter(t => t.maturity !== 'declining').length,
      topCategories,
      recentBenchmarks: this.benchmarkReports.slice(-3)
    };
  }

  private persistData(): void {
    try {
      const dataFiles = [
        { file: 'findings.json', data: { findings: Array.from(this.findings.values()) } },
        { file: 'trends.json', data: { trends: Array.from(this.trends.values()) } },
        { file: 'benchmarks.json', data: { reports: this.benchmarkReports } },
        { file: 'proposals.json', data: { proposals: Array.from(this.proposals.values()) } }
      ];

      dataFiles.forEach(({ file, data }) => {
        writeFileSync(join(this.dataDir, file), JSON.stringify(data, null, 2));
      });
    } catch (error) {
      console.error('Failed to persist research data:', error);
    }
  }

  cleanup(): void {
    console.log('🧹 Cleaning up Research Agent...');
    
    // Clear research schedules
    for (const interval of this.researchSchedule.values()) {
      clearInterval(interval);
    }
    
    this.persistData();
    console.log('✅ Research Agent cleanup complete');
  }
}