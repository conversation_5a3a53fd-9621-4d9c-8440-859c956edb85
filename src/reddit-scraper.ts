import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import <PERSON><PERSON><PERSON><PERSON> from 'node-cache';
import { RedditPost, RedditComment, TrendingSubreddit } from './types.js';

export class RedditScraper {
  private cache: NodeCache;
  private browser: Browser | null = null;

  constructor() {
    this.cache = new NodeCache({ stdTTL: 600 }); // Cache for 10 minutes
  }

  private async initBrowser(): Promise<Browser> {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
    }
    return this.browser;
  }

  async getSubredditPosts(
    subreddit: string, 
    sort: 'hot' | 'new' | 'top' | 'rising' = 'hot',
    limit: number = 25
  ): Promise<RedditPost[]> {
    const cacheKey = `reddit_posts_${subreddit}_${sort}_${limit}`;
    const cached = this.cache.get<RedditPost[]>(cacheKey);
    if (cached) {return cached;}

    const browser = await this.initBrowser();
    const page = await browser.newPage();
    
    try {
      await page.goto(`https://www.reddit.com/r/${subreddit}/${sort}/`, { 
        waitUntil: 'networkidle2' 
      });

      // Wait for posts to load
      await page.waitForSelector('[data-testid="post-container"]', { timeout: 10000 });

      // Scroll to load more posts
      await this.autoScroll(page, Math.ceil(limit / 10));

      const posts = await page.evaluate((maxPosts) => {
        const postElements = document.querySelectorAll('[data-testid="post-container"]');
        const posts: any[] = [];

        for (let i = 0; i < Math.min(postElements.length, maxPosts); i++) {
          const element = postElements[i];
          
          // Extract title
          const titleElement = element.querySelector('h3');
          const title = titleElement ? titleElement.textContent || '' : '';
          
          // Extract author
          const authorElement = element.querySelector('a[href^="/user/"]');
          const author = authorElement ? authorElement.textContent?.replace('u/', '') || '' : '';
          
          // Extract score
          const scoreElement = element.querySelector('[data-click-id="upvote"]')?.nextElementSibling;
          const score = scoreElement ? scoreElement.textContent || '0' : '0';
          
          // Extract comment count
          const commentElement = element.querySelector('a[data-click-id="comments"]');
          const commentText = commentElement ? commentElement.textContent || '' : '';
          const commentCount = parseInt(commentText.match(/\d+/)?.[0] || '0');
          
          // Extract URL
          const linkElement = element.querySelector('a[data-click-id="body"]');
          const url = linkElement ? 'https://www.reddit.com' + linkElement.getAttribute('href') : '';
          
          // Extract content (if text post)
          const contentElement = element.querySelector('[data-click-id="text"]');
          const content = contentElement ? contentElement.textContent || '' : '';
          
          // Extract timestamp
          const timeElement = element.querySelector('time');
          const timestamp = timeElement ? timeElement.getAttribute('datetime') || '' : '';

          posts.push({
            id: `post_${i}`,
            title,
            author,
            score: this.parseScore(score),
            commentCount,
            url,
            content,
            timestamp,
            subreddit: `r/${subreddit}`
          });
        }

        return posts;
      }, limit);

      this.cache.set(cacheKey, posts);
      return posts;
    } finally {
      await page.close();
    }
  }

  async getPostComments(
    postUrl: string,
    limit: number = 50,
    sortBy: 'best' | 'top' | 'new' | 'controversial' = 'best'
  ): Promise<RedditComment[]> {
    const cacheKey = `reddit_comments_${postUrl}_${sortBy}_${limit}`;
    const cached = this.cache.get<RedditComment[]>(cacheKey);
    if (cached) {return cached;}

    const browser = await this.initBrowser();
    const page = await browser.newPage();
    
    try {
      // Add sort parameter to URL
      const sortUrl = postUrl.includes('?') 
        ? `${postUrl}&sort=${sortBy}` 
        : `${postUrl}?sort=${sortBy}`;
      
      await page.goto(sortUrl, { waitUntil: 'networkidle2' });

      // Wait for comments to load
      await page.waitForSelector('[data-testid="comment"]', { timeout: 10000 });

      const comments = await page.evaluate((maxComments) => {
        const commentElements = document.querySelectorAll('[data-testid="comment"]');
        const comments: any[] = [];

        for (let i = 0; i < Math.min(commentElements.length, maxComments); i++) {
          const element = commentElements[i];
          
          // Extract author
          const authorElement = element.querySelector('a[href^="/user/"]');
          const author = authorElement ? authorElement.textContent || '' : '[deleted]';
          
          // Extract content
          const contentElement = element.querySelector('[data-testid="comment"]');
          const content = contentElement ? contentElement.textContent || '' : '';
          
          // Extract score
          const scoreElement = element.querySelector('[data-click-id="upvote"]')?.nextElementSibling;
          const score = scoreElement ? scoreElement.textContent || '0' : '0';
          
          // Extract depth (comment level)
          const depth = (element.closest('[data-testid="comment-tree-line"]')?.children.length || 1) - 1;
          
          // Extract timestamp
          const timeElement = element.querySelector('time');
          const timestamp = timeElement ? timeElement.getAttribute('datetime') || '' : '';

          comments.push({
            id: `comment_${i}`,
            author,
            content,
            score: this.parseScore(score),
            depth,
            timestamp,
            replies: [] // Would need recursive parsing for full reply tree
          });
        }

        return comments;
      }, limit);

      this.cache.set(cacheKey, comments);
      return comments;
    } finally {
      await page.close();
    }
  }

  async getTrendingSubreddits(
    category: string = 'all',
    limit: number = 10
  ): Promise<TrendingSubreddit[]> {
    const cacheKey = `reddit_trending_${category}_${limit}`;
    const cached = this.cache.get<TrendingSubreddit[]>(cacheKey);
    if (cached) {return cached;}

    const browser = await this.initBrowser();
    const page = await browser.newPage();
    
    try {
      // Reddit's popular page shows trending content
      const url = category === 'all' 
        ? 'https://www.reddit.com/best/' 
        : `https://www.reddit.com/r/popular/top/?t=day&category=${category}`;
      
      await page.goto(url, { waitUntil: 'networkidle2' });

      // Extract unique subreddits from posts
      const trending = await page.evaluate((maxSubreddits) => {
        const postElements = document.querySelectorAll('[data-testid="post-container"]');
        const subredditMap = new Map<string, any>();

        postElements.forEach(element => {
          // Extract subreddit info
          const subredditElement = element.querySelector('a[data-click-id="subreddit"]');
          if (!subredditElement) {return;}
          
          const subredditName = subredditElement.textContent || '';
          if (subredditMap.has(subredditName)) {
            subredditMap.get(subredditName).postCount++;
            return;
          }
          
          // Extract subscriber count if available
          const subscriberElement = element.querySelector('[data-testid="subreddit-subscribers"]');
          const subscriberText = subscriberElement ? subscriberElement.textContent || '' : '';
          const subscribers = (this as any).parseSubscribers(subscriberText);
          
          subredditMap.set(subredditName, {
            name: subredditName,
            subscribers,
            postCount: 1,
            description: '', // Would need to visit subreddit page for description
            rank: subredditMap.size + 1
          });
        });

        // Convert to array and sort by post count
        return Array.from(subredditMap.values())
          .sort((a: any, b: any) => b.postCount - a.postCount)
          .slice(0, maxSubreddits);
      }, limit);

      this.cache.set(cacheKey, trending);
      return trending;
    } finally {
      await page.close();
    }
  }

  async searchReddit(
    query: string,
    subreddit?: string,
    sort: 'relevance' | 'hot' | 'top' | 'new' | 'comments' = 'relevance',
    timeframe: 'all' | 'year' | 'month' | 'week' | 'day' | 'hour' = 'all',
    limit: number = 25
  ): Promise<RedditPost[]> {
    const cacheKey = `reddit_search_${query}_${subreddit}_${sort}_${timeframe}_${limit}`;
    const cached = this.cache.get<RedditPost[]>(cacheKey);
    if (cached) {return cached;}

    const browser = await this.initBrowser();
    const page = await browser.newPage();
    
    try {
      // Build search URL
      let searchUrl = subreddit 
        ? `https://www.reddit.com/r/${subreddit}/search/?q=${encodeURIComponent(query)}&restrict_sr=1`
        : `https://www.reddit.com/search/?q=${encodeURIComponent(query)}`;
      
      searchUrl += `&sort=${sort}&t=${timeframe}`;
      
      await page.goto(searchUrl, { waitUntil: 'networkidle2' });

      // Wait for search results
      await page.waitForSelector('[data-testid="post-container"]', { timeout: 10000 });

      const posts = await page.evaluate((maxPosts) => {
        const postElements = document.querySelectorAll('[data-testid="post-container"]');
        const posts: any[] = [];

        for (let i = 0; i < Math.min(postElements.length, maxPosts); i++) {
          const element = postElements[i];
          
          // Extract title
          const titleElement = element.querySelector('h3');
          const title = titleElement ? titleElement.textContent || '' : '';
          
          // Extract subreddit
          const subredditElement = element.querySelector('a[data-click-id="subreddit"]');
          const subredditName = subredditElement ? subredditElement.textContent || '' : '';
          
          // Extract author
          const authorElement = element.querySelector('a[href^="/user/"]');
          const author = authorElement ? authorElement.textContent?.replace('u/', '') || '' : '';
          
          // Extract score
          const scoreElement = element.querySelector('[data-click-id="upvote"]')?.nextElementSibling;
          const score = scoreElement ? scoreElement.textContent || '0' : '0';
          
          // Extract URL
          const linkElement = element.querySelector('a[data-click-id="body"]');
          const url = linkElement ? 'https://www.reddit.com' + linkElement.getAttribute('href') : '';
          
          // Extract timestamp
          const timeElement = element.querySelector('time');
          const timestamp = timeElement ? timeElement.getAttribute('datetime') || '' : '';

          posts.push({
            id: `search_${i}`,
            title,
            author,
            score: this.parseScore(score),
            url,
            timestamp,
            subreddit: subredditName,
            relevance: 1 - (i / maxPosts) // Simple relevance based on order
          });
        }

        return posts;
      }, limit);

      this.cache.set(cacheKey, posts);
      return posts;
    } finally {
      await page.close();
    }
  }

  private parseScore(score: string): number {
    if (score.includes('k')) {
      return parseFloat(score) * 1000;
    } else if (score.includes('m')) {
      return parseFloat(score) * 1000000;
    }
    return parseInt(score) || 0;
  }

  private parseSubscribers(text: string): number {
    const match = text.match(/(\d+\.?\d*)\s*([km]?)/i);
    if (!match) {return 0;}
    
    const num = parseFloat(match[1]);
    const suffix = match[2].toLowerCase();
    
    if (suffix === 'k') {return num * 1000;}
    if (suffix === 'm') {return num * 1000000;}
    return num;
  }

  private async autoScroll(page: Page, maxScrolls: number = 5): Promise<void> {
    await page.evaluate(async (maxScrolls) => {
      await new Promise<void>((resolve) => {
        let totalHeight = 0;
        let scrolls = 0;
        const distance = 100;
        const timer = setInterval(() => {
          const scrollHeight = document.body.scrollHeight;
          window.scrollBy(0, distance);
          totalHeight += distance;
          scrolls++;

          if (totalHeight >= scrollHeight - window.innerHeight || scrolls >= maxScrolls) {
            clearInterval(timer);
            resolve();
          }
        }, 100);
      });
    }, maxScrolls);
  }

  async close(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}