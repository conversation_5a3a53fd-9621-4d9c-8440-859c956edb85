/**
 * Visual Scraping Engine
 * 
 * Advanced screenshot-based data extraction system that can:
 * - Extract text from images using OCR (Tesseract)
 * - Detect and analyze CAPTCHA challenges
 * - Identify layout changes and blocking mechanisms
 * - Extract data from canvas elements and protected content
 * - Perform visual regression testing for scraping reliability
 */

import { Page } from 'puppeteer';
import { create<PERSON><PERSON><PERSON>, Worker } from 'tesseract.js';
import { createCanvas, loadImage, Canvas } from 'canvas';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import sharp from 'sharp';

interface OCRResult {
  text: string;
  confidence: number;
  words: Array<{
    text: string;
    confidence: number;
    bbox: { x0: number; y0: number; x1: number; y1: number };
  }>;
  lines: Array<{
    text: string;
    confidence: number;
    bbox: { x0: number; y0: number; x1: number; y1: number };
  }>;
}

interface VisualElement {
  type: 'text' | 'image' | 'button' | 'input' | 'captcha' | 'error' | 'content';
  content: string;
  confidence: number;
  position: { x: number; y: number; width: number; height: number };
  metadata?: Record<string, unknown>;
}

interface CaptchaDetection {
  detected: boolean;
  type: 'recaptcha' | 'hcaptcha' | 'cloudflare' | 'custom' | 'unknown';
  confidence: number;
  position?: { x: number; y: number; width: number; height: number };
  challenge?: string;
}

export interface ScreenshotAnalysis {
  elements: VisualElement[];
  pageType: 'normal' | 'captcha' | 'blocked' | 'error' | 'loading' | 'protected';
  blockingIndicators: string[];
  extractedData: Record<string, unknown>;
  confidence: number;
  processingTime: number;
}

export class VisualScrapingEngine {
  private ocrWorker: Worker | null = null;
  private screenshotsDir: string;
  private isInitialized = false;
  private ocrCache: Map<string, OCRResult> = new Map();
  
  // Known visual patterns for common blocking scenarios
  private blockingPatterns = {
    captcha: [
      'select all images',
      'prove you are human',
      'verify you are not a robot',
      'complete the security check',
      'i\'m not a robot',
      'recaptcha',
      'hcaptcha'
    ],
    blocked: [
      'access denied',
      'blocked',
      'forbidden',
      '403',
      'bot detected',
      'suspicious activity',
      'rate limit exceeded',
      'temporarily unavailable'
    ],
    cloudflare: [
      'cloudflare',
      'checking your browser',
      'security check',
      'ddos protection',
      'ray id'
    ]
  };

  constructor(screenshotsDir: string = './screenshots') {
    this.screenshotsDir = screenshotsDir;
    this.ensureScreenshotsDir();
  }

  private ensureScreenshotsDir(): void {
    if (!existsSync(this.screenshotsDir)) {
      mkdirSync(this.screenshotsDir, { recursive: true });
    }
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {return;}

    try {
      console.log('🔍 Initializing Visual Scraping Engine...');
      
      // Initialize Tesseract OCR worker
      this.ocrWorker = await createWorker('eng');
      await this.ocrWorker.setParameters({
        tessedit_pageseg_mode: 6 as any, // Uniform block of text
        tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,!?@#$%^&*()_+-=[]{}|;:\'\"<>/ \n\t'
      });

      this.isInitialized = true;
      console.log('✅ Visual Scraping Engine initialized with OCR capabilities');
    } catch (error) {
      console.error('❌ Failed to initialize Visual Scraping Engine:', error);
      throw error;
    }
  }

  async analyzeScreenshot(
    page: Page, 
    options: {
      fullPage?: boolean;
      element?: string;
      name?: string;
      detectBlocking?: boolean;
      extractText?: boolean;
      detectCaptcha?: boolean;
    } = {}
  ): Promise<ScreenshotAnalysis> {
    const startTime = Date.now();
    
    if (!this.isInitialized) {
      await this.initialize();
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${options.name || 'analysis'}-${timestamp}.png`;
    const filepath = join(this.screenshotsDir, filename);

    try {
      // Take screenshot
      const screenshotBuffer = await this.takeScreenshot(page, filepath, {
        fullPage: options.fullPage,
        element: options.element
      });

      // Perform analysis
      const analysis: ScreenshotAnalysis = {
        elements: [],
        pageType: 'normal',
        blockingIndicators: [],
        extractedData: {},
        confidence: 0,
        processingTime: 0
      };

      // OCR text extraction
      if (options.extractText !== false && this.ocrWorker) {
        const ocrResult = await this.performOCR(screenshotBuffer);
        analysis.elements.push(...this.extractTextElements(ocrResult));
        analysis.extractedData.ocrText = ocrResult.text;
        analysis.extractedData.ocrConfidence = ocrResult.confidence;
      }

      // CAPTCHA detection
      if (options.detectCaptcha !== false) {
        const captchaDetection = await this.detectCaptcha(screenshotBuffer);
        if (captchaDetection.detected) {
          analysis.pageType = 'captcha';
          analysis.elements.push({
            type: 'captcha',
            content: captchaDetection.type,
            confidence: captchaDetection.confidence,
            position: captchaDetection.position || { x: 0, y: 0, width: 0, height: 0 },
            metadata: { captchaType: captchaDetection.type, challenge: captchaDetection.challenge }
          });
        }
      }

      // Blocking detection
      if (options.detectBlocking !== false) {
        const blockingAnalysis = await this.detectBlocking(screenshotBuffer);
        analysis.blockingIndicators = blockingAnalysis.indicators;
        if (blockingAnalysis.isBlocked) {
          analysis.pageType = blockingAnalysis.type;
        }
      }

      // Visual element detection
      const visualElements = await this.detectVisualElements(screenshotBuffer);
      analysis.elements.push(...visualElements);

      // Calculate overall confidence
      analysis.confidence = this.calculateOverallConfidence(analysis.elements);
      analysis.processingTime = Date.now() - startTime;

      console.log(`📸 Screenshot analysis completed in ${analysis.processingTime}ms`, {
        pageType: analysis.pageType,
        elementsFound: analysis.elements.length,
        confidence: analysis.confidence
      });

      return analysis;
    } catch (error) {
      console.error('❌ Screenshot analysis failed:', error);
      return {
        elements: [],
        pageType: 'error',
        blockingIndicators: [`Analysis failed: ${error}`],
        extractedData: {},
        confidence: 0,
        processingTime: Date.now() - startTime
      };
    }
  }

  private async takeScreenshot(
    page: Page, 
    filepath: string, 
    options: { fullPage?: boolean; element?: string }
  ): Promise<Buffer> {
    const screenshotOptions: any = {
      path: filepath,
      type: 'png',
      fullPage: options.fullPage || false
    };

    if (options.element) {
      const element = await page.$(options.element);
      if (element) {
        return await element.screenshot({ type: 'png' }) as Buffer;
      }
    }

    return await page.screenshot(screenshotOptions) as unknown as Buffer;
  }

  private async performOCR(imageBuffer: Buffer): Promise<OCRResult> {
    if (!this.ocrWorker) {
      throw new Error('OCR worker not initialized');
    }

    // Check cache first
    const cacheKey = imageBuffer.toString('base64').slice(0, 32);
    if (this.ocrCache.has(cacheKey)) {
      return this.ocrCache.get(cacheKey)!;
    }

    try {
      // Preprocess image for better OCR accuracy
      const processedImage = await this.preprocessImage(imageBuffer);
      
      const { data } = await this.ocrWorker.recognize(processedImage) as any;
      
      const result: OCRResult = {
        text: data.text?.trim() || '',
        confidence: data.confidence || 0,
        words: (data.words || []).map((word: any) => ({
          text: word.text || '',
          confidence: word.confidence || 0,
          bbox: word.bbox || { x0: 0, y0: 0, x1: 0, y1: 0 }
        })),
        lines: (data.lines || []).map((line: any) => ({
          text: line.text || '',
          confidence: line.confidence || 0,
          bbox: line.bbox || { x0: 0, y0: 0, x1: 0, y1: 0 }
        }))
      };

      // Cache result
      this.ocrCache.set(cacheKey, result);
      
      // Limit cache size
      if (this.ocrCache.size > 100) {
        const firstKey = this.ocrCache.keys().next().value;
        if (firstKey) {
          this.ocrCache.delete(firstKey);
        }
      }

      return result;
    } catch (error) {
      console.error('OCR processing failed:', error);
      return {
        text: '',
        confidence: 0,
        words: [],
        lines: []
      };
    }
  }

  private async preprocessImage(imageBuffer: Buffer): Promise<Buffer> {
    try {
      // Use sharp to enhance image for OCR
      return await sharp(imageBuffer)
        .greyscale() // Convert to grayscale
        .normalize() // Enhance contrast
        .sharpen() // Sharpen text
        .png()
        .toBuffer();
    } catch (error) {
      console.warn('Image preprocessing failed, using original:', error);
      return imageBuffer;
    }
  }

  private extractTextElements(ocrResult: OCRResult): VisualElement[] {
    const elements: VisualElement[] = [];

    // Process lines for coherent text blocks
    ocrResult.lines.forEach((line, index) => {
      if (line.confidence > 60 && line.text.trim().length > 2) {
        elements.push({
          type: 'text',
          content: line.text.trim(),
          confidence: line.confidence,
          position: {
            x: line.bbox.x0,
            y: line.bbox.y0,
            width: line.bbox.x1 - line.bbox.x0,
            height: line.bbox.y1 - line.bbox.y0
          },
          metadata: { lineIndex: index, wordCount: line.text.split(' ').length }
        });
      }
    });

    return elements;
  }

  private async detectCaptcha(imageBuffer: Buffer): Promise<CaptchaDetection> {
    try {
      // Quick OCR scan for CAPTCHA keywords
      const ocrResult = await this.performOCR(imageBuffer);
      const text = ocrResult.text.toLowerCase();
      
      // Check for known CAPTCHA patterns
      for (const pattern of this.blockingPatterns.captcha) {
        if (text.includes(pattern)) {
          let type: CaptchaDetection['type'] = 'unknown';
          
          if (text.includes('recaptcha')) {type = 'recaptcha';}
          else if (text.includes('hcaptcha')) {type = 'hcaptcha';}
          else if (text.includes('cloudflare')) {type = 'cloudflare';}
          else if (pattern !== pattern.toLowerCase()) {type = 'custom';}

          return {
            detected: true,
            type,
            confidence: 85,
            challenge: text.includes('select') ? 'image_selection' : 'checkbox'
          };
        }
      }

      // Visual detection of common CAPTCHA elements
      // This would require more sophisticated image analysis
      // For now, we rely on text-based detection

      return { detected: false, type: 'unknown', confidence: 0 };
    } catch (error) {
      console.error('CAPTCHA detection failed:', error);
      return { detected: false, type: 'unknown', confidence: 0 };
    }
  }

  private async detectBlocking(imageBuffer: Buffer): Promise<{
    isBlocked: boolean;
    type: ScreenshotAnalysis['pageType'];
    indicators: string[];
  }> {
    try {
      const ocrResult = await this.performOCR(imageBuffer);
      const text = ocrResult.text.toLowerCase();
      const indicators: string[] = [];
      let type: ScreenshotAnalysis['pageType'] = 'normal';

      // Check for blocking patterns
      Object.entries(this.blockingPatterns).forEach(([category, patterns]) => {
        patterns.forEach(pattern => {
          if (text.includes(pattern)) {
            indicators.push(`${category}:${pattern}`);
            if (category === 'blocked') {type = 'blocked';}
            else if (category === 'cloudflare') {type = 'blocked';}
          }
        });
      });

      // Additional heuristics
      if (text.includes('error') || text.includes('404') || text.includes('500')) {
        indicators.push('error_page');
        type = 'error';
      }

      if (text.includes('loading') || text.includes('please wait')) {
        indicators.push('loading_state');
        type = 'loading';
      }

      return {
        isBlocked: indicators.length > 0,
        type,
        indicators
      };
    } catch (error) {
      console.error('Blocking detection failed:', error);
      return {
        isBlocked: false,
        type: 'normal',
        indicators: []
      };
    }
  }

  private async detectVisualElements(imageBuffer: Buffer): Promise<VisualElement[]> {
    // This is a simplified implementation
    // In a production system, you would use computer vision libraries
    // like OpenCV or cloud services like AWS Rekognition/Google Vision
    
    const elements: VisualElement[] = [];
    
    try {
      // For now, we'll detect based on OCR text patterns
      const ocrResult = await this.performOCR(imageBuffer);
      
      ocrResult.words.forEach(word => {
        const text = word.text.toLowerCase();
        let elementType: VisualElement['type'] = 'text';
        
        // Simple pattern matching for element types
        if (['click', 'submit', 'login', 'search', 'go', 'enter'].some(btn => text.includes(btn))) {
          elementType = 'button';
        } else if (['username', 'password', 'email', 'search', 'input'].some(inp => text.includes(inp))) {
          elementType = 'input';
        } else if (text.match(/\.(jpg|png|gif|svg)/)) {
          elementType = 'image';
        }
        
        if (word.confidence > 70) {
          elements.push({
            type: elementType,
            content: word.text,
            confidence: word.confidence,
            position: {
              x: word.bbox.x0,
              y: word.bbox.y0,
              width: word.bbox.x1 - word.bbox.x0,
              height: word.bbox.y1 - word.bbox.y0
            }
          });
        }
      });
    } catch (error) {
      console.error('Visual element detection failed:', error);
    }
    
    return elements;
  }

  private calculateOverallConfidence(elements: VisualElement[]): number {
    if (elements.length === 0) {return 0;}
    
    const totalConfidence = elements.reduce((sum, el) => sum + el.confidence, 0);
    return Math.round(totalConfidence / elements.length);
  }

  async extractDataFromCanvas(page: Page, selector: string): Promise<{
    success: boolean;
    data?: string;
    error?: string;
  }> {
    try {
      // Extract canvas content as image data
      const canvasData = await page.evaluate((sel) => {
        const canvas = document.querySelector(sel) as HTMLCanvasElement;
        if (!canvas) {return null;}
        return canvas.toDataURL('image/png');
      }, selector);

      if (!canvasData) {
        return { success: false, error: 'Canvas element not found' };
      }

      // Convert data URL to buffer
      const base64Data = canvasData.replace(/^data:image\/png;base64,/, '');
      const imageBuffer = Buffer.from(base64Data, 'base64');

      // Perform OCR on canvas content
      const ocrResult = await this.performOCR(imageBuffer);

      return {
        success: true,
        data: ocrResult.text
      };
    } catch (error) {
      return {
        success: false,
        error: `Canvas extraction failed: ${error}`
      };
    }
  }

  async compareScreenshots(
    screenshot1Path: string, 
    screenshot2Path: string
  ): Promise<{
    similarity: number;
    differences: Array<{ x: number; y: number; width: number; height: number }>;
  }> {
    try {
      // This is a simplified implementation
      // In production, you would use libraries like pixelmatch or resemble.js
      
      const img1 = await sharp(screenshot1Path).raw().toBuffer({ resolveWithObject: true });
      const img2 = await sharp(screenshot2Path).raw().toBuffer({ resolveWithObject: true });

      // Basic pixel comparison (simplified)
      if (img1.info.width !== img2.info.width || img1.info.height !== img2.info.height) {
        return { similarity: 0, differences: [] };
      }

      let matchingPixels = 0;
      const totalPixels = img1.info.width * img1.info.height;

      for (let i = 0; i < img1.data.length; i += 3) {
        const r1 = img1.data[i], g1 = img1.data[i + 1], b1 = img1.data[i + 2];
        const r2 = img2.data[i], g2 = img2.data[i + 1], b2 = img2.data[i + 2];
        
        const diff = Math.abs(r1 - r2) + Math.abs(g1 - g2) + Math.abs(b1 - b2);
        if (diff < 30) { // Tolerance threshold
          matchingPixels++;
        }
      }

      const similarity = (matchingPixels / totalPixels) * 100;
      
      return {
        similarity: Math.round(similarity),
        differences: [] // Simplified - would calculate actual difference regions
      };
    } catch (error) {
      console.error('Screenshot comparison failed:', error);
      return { similarity: 0, differences: [] };
    }
  }

  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up Visual Scraping Engine...');
    
    if (this.ocrWorker) {
      await this.ocrWorker.terminate();
      this.ocrWorker = null;
    }
    
    this.ocrCache.clear();
    this.isInitialized = false;
    
    console.log('✅ Visual Scraping Engine cleanup complete');
  }

  getStats(): {
    initialized: boolean;
    cacheSize: number;
    screenshotsDir: string;
  } {
    return {
      initialized: this.isInitialized,
      cacheSize: this.ocrCache.size,
      screenshotsDir: this.screenshotsDir
    };
  }
}