export interface Tweet {
  id: string;
  text: string;
  timestamp: string;
  author: string;
  likes: number;
  retweets: number;
  replies: number;
  isReply: boolean;
}

export interface Contributor {
  username: string;
  displayName: string;
  bio: string;
  followers: number;
  relevanceScore: number;
  verifiedStatus: boolean;
}

export interface TrendingTopic {
  name: string;
  tweetCount: number;
  category: string;
  rank: number;
  timestamp: string;
}

export interface SentimentAnalysis {
  overall: 'positive' | 'negative' | 'neutral';
  score: number;
  breakdown?: {
    positive: number;
    negative: number;
    neutral: number;
  };
  keywords?: string[];
}

// Reddit types
export interface RedditPost {
  id: string;
  title: string;
  author: string;
  score: number;
  commentCount?: number;
  url: string;
  content?: string;
  timestamp: string;
  subreddit: string;
  relevance?: number;
}

export interface RedditComment {
  id: string;
  author: string;
  content: string;
  score: number;
  depth: number;
  timestamp: string;
  replies: RedditComment[];
}

export interface TrendingSubreddit {
  name: string;
  subscribers: number;
  postCount: number;
  description: string;
  rank: number;
}