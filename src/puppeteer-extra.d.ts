// Type declarations for puppeteer-extra and plugins

declare module 'puppeteer-extra' {
  import { <PERSON>rows<PERSON>, LaunchOptions } from 'puppeteer';

  interface PuppeteerExtra {
    use(plugin: any): PuppeteerExtra;
    launch(options?: LaunchOptions): Promise<Browser>;
  }

  const puppeteer: PuppeteerExtra;
  export default puppeteer;
}

declare module 'puppeteer-extra-plugin-stealth' {
  const plugin: () => any;
  export default plugin;
}

declare module 'puppeteer-extra-plugin-proxy' {
  interface ProxyOptions {
    proxy: {
      url: string;
      username?: string;
      password?: string;
    };
  }

  const plugin: (options: ProxyOptions) => any;
  export default plugin;
}
