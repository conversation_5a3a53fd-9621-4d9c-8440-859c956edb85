/**
 * Communication & Notification Layer
 * 
 * Comprehensive communication system that provides:
 * - Multi-channel notifications (<PERSON><PERSON><PERSON>, Discord, Email, webhooks)
 * - Real-time alerts for critical issues and performance degradation
 * - Executive reporting with automated delivery schedules
 * - Interactive dashboard APIs for real-time monitoring
 * - Integration with existing health and monitoring systems
 * - Configurable notification rules and escalation policies
 */

import { EventEmitter } from 'events';
import axios from 'axios';
import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { HealthReporter } from './health-reporter.js';
import { KPIMonitoringSystem } from './kpi-monitoring-system.js';
import { EnhancementEngine } from './enhancement-engine.js';
import { ResearchAgent } from './research-agent.js';

interface NotificationChannel {
  type: 'slack' | 'discord' | 'email' | 'webhook' | 'teams' | 'telegram';
  name: string;
  config: {
    webhookUrl?: string;
    apiToken?: string;
    channel?: string;
    email?: string;
    chatId?: string;
    [key: string]: unknown;
  };
  enabled: boolean;
  rules: NotificationRule[];
}

interface NotificationRule {
  id: string;
  name: string;
  conditions: {
    eventTypes: string[];
    severityLevels: ('info' | 'warning' | 'error' | 'critical')[];
    domains?: string[];
    thresholds?: Record<string, number>;
    timeWindow?: number; // milliseconds
    frequency?: 'immediate' | 'batched' | 'daily' | 'weekly';
  };
  message: {
    template: string;
    includeMetrics?: boolean;
    includeCharts?: boolean;
    includeTrends?: boolean;
  };
  escalation?: {
    after: number; // milliseconds
    channels: string[];
  };
}

interface NotificationEvent {
  id: string;
  timestamp: number;
  type: 'health_alert' | 'performance_degradation' | 'blocking_detected' | 'enhancement_completed' | 'system_status' | 'research_finding' | 'custom';
  severity: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  data?: Record<string, unknown>;
  domain?: string;
  tags?: string[];
  resolved?: boolean;
  resolvedAt?: number;
}

interface DashboardEndpoint {
  path: string;
  method: 'GET' | 'POST';
  handler: string;
  auth?: boolean;
  rateLimit?: number;
  description: string;
}

interface CommunicationConfig {
  channels: NotificationChannel[];
  rules: NotificationRule[];
  dashboard: {
    enabled: boolean;
    port: number;
    endpoints: DashboardEndpoint[];
    cors?: boolean;
    auth?: {
      enabled: boolean;
      apiKey?: string;
      jwt?: boolean;
    };
  };
  reporting: {
    dailyReports: {
      enabled: boolean;
      time: string; // HH:MM format
      recipients: string[];
      channels: string[];
    };
    weeklyReports: {
      enabled: boolean;
      day: number; // 0-6, Sunday = 0
      time: string;
      recipients: string[];
      channels: string[];
    };
    alertBatching: {
      enabled: boolean;
      windowMinutes: number;
      maxBatchSize: number;
    };
  };
}

export class CommunicationLayer extends EventEmitter {
  private healthReporter: HealthReporter;
  private kpiMonitor: KPIMonitoringSystem;
  private enhancementEngine?: EnhancementEngine;
  private researchAgent?: ResearchAgent;

  private config: CommunicationConfig = this.createDefaultConfiguration();
  private dataDir: string;
  private channels: Map<string, NotificationChannel> = new Map();
  private rules: Map<string, NotificationRule> = new Map();
  private eventQueue: NotificationEvent[] = [];
  private batchedEvents: Map<string, NotificationEvent[]> = new Map();
  private dashboardServer: any = null;

  // Rate limiting and throttling
  private rateLimits: Map<string, { count: number; resetTime: number }> = new Map();
  private lastNotificationTime: Map<string, number> = new Map();
  
  // Metrics tracking
  private notificationStats = {
    sent: 0,
    failed: 0,
    batched: 0,
    throttled: 0
  };

  constructor(
    healthReporter: HealthReporter,
    kpiMonitor: KPIMonitoringSystem,
    dataDir: string = './data/communication',
    enhancementEngine?: EnhancementEngine,
    researchAgent?: ResearchAgent
  ) {
    super();
    
    this.healthReporter = healthReporter;
    this.kpiMonitor = kpiMonitor;
    this.enhancementEngine = enhancementEngine;
    this.researchAgent = researchAgent;
    this.dataDir = dataDir;

    this.ensureDataDir();
    this.loadConfiguration();
    this.setupEventListeners();
    this.startPeriodicTasks();
    
    console.log('📡 Communication Layer initialized');
  }

  private ensureDataDir(): void {
    if (!existsSync(this.dataDir)) {
      mkdirSync(this.dataDir, { recursive: true });
    }
  }

  private loadConfiguration(): void {
    try {
      const configFile = join(this.dataDir, 'communication-config.json');
      
      if (existsSync(configFile)) {
        this.config = JSON.parse(readFileSync(configFile, 'utf-8'));
      } else {
        this.config = this.createDefaultConfiguration();
        this.saveConfiguration();
      }

      // Load channels and rules
      this.config.channels.forEach(channel => {
        this.channels.set(channel.name, channel);
      });

      this.config.rules.forEach(rule => {
        this.rules.set(rule.id, rule);
      });

      console.log(`📋 Loaded ${this.channels.size} channels and ${this.rules.size} notification rules`);
    } catch (error) {
      console.error('Failed to load communication configuration:', error);
      this.config = this.createDefaultConfiguration();
    }
  }

  private createDefaultConfiguration(): CommunicationConfig {
    return {
      channels: [
        {
          type: 'webhook',
          name: 'default-webhook',
          config: {
            webhookUrl: process.env.DEFAULT_WEBHOOK_URL || 'https://hooks.slack.com/services/YOUR/WEBHOOK/URL'
          },
          enabled: false,
          rules: []
        }
      ],
      rules: [
        {
          id: 'critical_alerts',
          name: 'Critical System Alerts',
          conditions: {
            eventTypes: ['health_alert', 'performance_degradation', 'blocking_detected'],
            severityLevels: ['critical', 'error'],
            frequency: 'immediate'
          },
          message: {
            template: '🚨 **CRITICAL ALERT**: {{title}}\n{{message}}\n\n📊 Current Metrics:\n- Success Rate: {{metrics.successRate}}%\n- Block Rate: {{metrics.blockRate}}%\n- Response Time: {{metrics.avgResponseTime}}ms',
            includeMetrics: true
          },
          escalation: {
            after: 300000, // 5 minutes
            channels: ['critical-alerts']
          }
        },
        {
          id: 'daily_summary',
          name: 'Daily Health Summary',
          conditions: {
            eventTypes: ['system_status'],
            severityLevels: ['info'],
            frequency: 'daily'
          },
          message: {
            template: '📊 **Daily Health Report**\n\n{{summary}}\n\n📈 Key Metrics:\n- Overall Health: {{health.overall}}/100\n- Uptime: {{metrics.uptime}}%\n- Data Quality: {{metrics.dataQuality}}/100',
            includeMetrics: true,
            includeTrends: true
          }
        }
      ],
      dashboard: {
        enabled: true,
        port: 3001,
        cors: true,
        endpoints: [
          {
            path: '/api/health',
            method: 'GET',
            handler: 'getSystemHealth',
            description: 'Get current system health status'
          },
          {
            path: '/api/metrics',
            method: 'GET',
            handler: 'getLiveMetrics',
            description: 'Get real-time performance metrics'
          },
          {
            path: '/api/alerts',
            method: 'GET',
            handler: 'getActiveAlerts',
            description: 'Get current active alerts'
          }
        ]
      },
      reporting: {
        dailyReports: {
          enabled: true,
          time: '08:00',
          recipients: [],
          channels: ['default-webhook']
        },
        weeklyReports: {
          enabled: true,
          day: 1, // Monday
          time: '09:00',
          recipients: [],
          channels: ['default-webhook']
        },
        alertBatching: {
          enabled: true,
          windowMinutes: 5,
          maxBatchSize: 10
        }
      }
    };
  }

  private setupEventListeners(): void {
    // Set up periodic health checks instead of event listeners
    setInterval(async () => {
      try {
        const health = await this.healthReporter.calculateSystemHealth();
        if (health.score.overall < 70) {
          (this as any).emit('notification', {
            type: 'health_alert',
            severity: health.score.overall < 50 ? 'critical' : 'warning',
            title: 'System Health Degradation',
            message: `Overall health score dropped to ${health.score.overall}/100`,
            data: { health }
          });
        }
      } catch (error) {
        // Ignore health check errors
      }
    }, 5 * 60 * 1000); // Check every 5 minutes

    // Listen to our own notification events
    this.on('notification', this.handleNotificationEvent.bind(this));

    console.log('🔗 Event listeners configured');
  }

  private startPeriodicTasks(): void {
    // Process batched notifications every minute
    setInterval(() => {
      this.processBatchedNotifications();
    }, 60 * 1000);

    // Generate daily reports
    if (this.config.reporting.dailyReports.enabled) {
      setInterval(() => {
        const now = new Date();
        const timeString = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
        
        if (timeString === this.config.reporting.dailyReports.time) {
          void this.sendDailyReport();
        }
      }, 60 * 1000);
    }

    // Generate weekly reports
    if (this.config.reporting.weeklyReports.enabled) {
      setInterval(() => {
        const now = new Date();
        const timeString = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
        
        if (now.getDay() === this.config.reporting.weeklyReports.day && 
            timeString === this.config.reporting.weeklyReports.time) {
          void this.sendWeeklyReport();
        }
      }, 60 * 1000);
    }

    console.log('⏰ Periodic tasks scheduled');
  }

  private async handleNotificationEvent(event: Partial<NotificationEvent>): Promise<void> {
    const notification: NotificationEvent = {
      id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
      timestamp: Date.now(),
      type: event.type || 'custom',
      severity: event.severity || 'info',
      title: event.title || 'System Notification',
      message: event.message || '',
      data: event.data,
      domain: event.domain,
      tags: event.tags,
      resolved: event.resolved || false
    };

    // Find matching rules
    const matchingRules = Array.from(this.rules.values()).filter(rule => 
      this.doesEventMatchRule(notification, rule)
    );

    if (matchingRules.length === 0) {
      console.log(`📋 No matching rules for notification: ${notification.title}`);
      return;
    }

    // Process each matching rule
    for (const rule of matchingRules) {
      await this.processNotificationRule(notification, rule);
    }
  }

  private doesEventMatchRule(event: NotificationEvent, rule: NotificationRule): boolean {
    const conditions = rule.conditions;

    // Check event type
    if (!conditions.eventTypes.includes(event.type)) {
      return false;
    }

    // Check severity
    if (!conditions.severityLevels.includes(event.severity)) {
      return false;
    }

    // Check domain if specified
    if (conditions.domains && event.domain && !conditions.domains.includes(event.domain)) {
      return false;
    }

    // Check thresholds if specified (would need more complex logic for actual implementation)
    if (conditions.thresholds && event.data) {
      // Simplified threshold checking
      for (const [key, threshold] of Object.entries(conditions.thresholds)) {
        const value = this.extractNestedValue(event.data, key);
        if (typeof value === 'number' && value < threshold) {
          return false;
        }
      }
    }

    return true;
  }

  private async processNotificationRule(event: NotificationEvent, rule: NotificationRule): Promise<void> {
    const frequency = rule.conditions.frequency || 'immediate';

    switch (frequency) {
      case 'immediate':
        await this.sendImmediateNotification(event, rule);
        break;
      
      case 'batched':
        this.addToBatch(event, rule);
        break;
        
      case 'daily':
      case 'weekly':
        // These are handled by periodic tasks
        break;
    }
  }

  private async sendImmediateNotification(event: NotificationEvent, rule: NotificationRule): Promise<void> {
    const message = this.renderMessageTemplate(rule.message.template, event);
    
    // Get channels for this rule (simplified - would use rule-specific channel mapping)
    const channels = Array.from(this.channels.values()).filter(ch => ch.enabled);
    
    for (const channel of channels) {
      try {
        await this.sendToChannel(channel, {
          title: event.title,
          message,
          severity: event.severity,
          timestamp: event.timestamp
        });
        
        this.notificationStats.sent++;
        console.log(`📤 Sent notification to ${channel.name}: ${event.title}`);
      } catch (error) {
        console.error(`Failed to send notification to ${channel.name}:`, error);
        this.notificationStats.failed++;
      }
    }
  }

  private addToBatch(event: NotificationEvent, rule: NotificationRule): void {
    const batchKey = `${rule.id}_${event.type}`;
    
    if (!this.batchedEvents.has(batchKey)) {
      this.batchedEvents.set(batchKey, []);
    }
    
    const batch = this.batchedEvents.get(batchKey)!;
    batch.push(event);
    
    this.notificationStats.batched++;
    
    // Check if batch should be sent immediately due to size
    const maxBatchSize = this.config.reporting.alertBatching.maxBatchSize;
    if (batch.length >= maxBatchSize) {
      void this.sendBatchNotification(batchKey, rule);
    }
  }

  private async processBatchedNotifications(): Promise<void> {
    const windowMs = this.config.reporting.alertBatching.windowMinutes * 60 * 1000;
    const now = Date.now();
    
    for (const [batchKey, events] of this.batchedEvents.entries()) {
      if (events.length === 0) {continue;}
      
      const oldestEvent = Math.min(...events.map(e => e.timestamp));
      if (now - oldestEvent >= windowMs) {
        const rule = this.findRuleForBatch(batchKey);
        if (rule) {
          await this.sendBatchNotification(batchKey, rule);
        }
      }
    }
  }

  private async sendBatchNotification(batchKey: string, rule: NotificationRule): Promise<void> {
    const events = this.batchedEvents.get(batchKey) || [];
    if (events.length === 0) {return;}
    
    const summary = this.createBatchSummary(events);
    const message = this.renderMessageTemplate(rule.message.template, {
      id: `batch_${Date.now()}`,
      timestamp: Date.now(),
      type: 'system_status',
      severity: 'info',
      title: `Batch Notification (${events.length} events)`,
      message: summary,
      data: { events, batchSize: events.length }
    });
    
    // Send to channels
    const channels = Array.from(this.channels.values()).filter(ch => ch.enabled);
    for (const channel of channels) {
      try {
        await this.sendToChannel(channel, {
          title: `Batch Alert: ${events.length} Events`,
          message,
          severity: 'info',
          timestamp: Date.now()
        });
      } catch (error) {
        console.error(`Failed to send batch notification to ${channel.name}:`, error);
      }
    }
    
    // Clear the batch
    this.batchedEvents.set(batchKey, []);
    console.log(`📦 Sent batched notification: ${events.length} events`);
  }

  private async sendToChannel(channel: NotificationChannel, notification: {
    title: string;
    message: string;
    severity: string;
    timestamp: number;
  }): Promise<void> {
    switch (channel.type) {
      case 'slack':
        await this.sendSlackNotification(channel, notification);
        break;
        
      case 'discord':
        await this.sendDiscordNotification(channel, notification);
        break;
        
      case 'webhook':
        await this.sendWebhookNotification(channel, notification);
        break;
        
      case 'email':
        await this.sendEmailNotification(channel, notification);
        break;
        
      default:
        console.warn(`Unsupported channel type: ${channel.type}`);
    }
  }

  private async sendSlackNotification(channel: NotificationChannel, notification: any): Promise<void> {
    const webhookUrl = channel.config.webhookUrl as string;
    if (!webhookUrl) {throw new Error('Slack webhook URL not configured');}
    
    const severityEmoji: Record<string, string> = {
      info: '📋',
      warning: '⚠️',
      error: '❌',
      critical: '🚨'
    };
    const emoji = severityEmoji[notification.severity] || '📋';
    
    const payload = {
      text: `${emoji} ${notification.title}`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*${emoji} ${notification.title}*\n${notification.message}`
          }
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `🕒 ${new Date(notification.timestamp).toISOString()}`
            }
          ]
        }
      ]
    };
    
    await axios.post(webhookUrl, payload);
  }

  private async sendDiscordNotification(channel: NotificationChannel, notification: any): Promise<void> {
    const webhookUrl = channel.config.webhookUrl as string;
    if (!webhookUrl) {throw new Error('Discord webhook URL not configured');}
    
    const severityColor: Record<string, number> = {
      info: 0x3498db,      // Blue
      warning: 0xf39c12,   // Orange
      error: 0xe74c3c,     // Red
      critical: 0x9b59b6   // Purple
    };
    const color = severityColor[notification.severity] || 0x3498db;
    
    const payload = {
      embeds: [
        {
          title: notification.title,
          description: notification.message,
          color: color,
          timestamp: new Date(notification.timestamp).toISOString(),
          footer: {
            text: 'MCP X-Reddit Scraper'
          }
        }
      ]
    };
    
    await axios.post(webhookUrl, payload);
  }

  private async sendWebhookNotification(channel: NotificationChannel, notification: any): Promise<void> {
    const webhookUrl = channel.config.webhookUrl as string;
    if (!webhookUrl) {throw new Error('Webhook URL not configured');}
    
    const payload = {
      title: notification.title,
      message: notification.message,
      severity: notification.severity,
      timestamp: notification.timestamp,
      source: 'mcp-scraper'
    };
    
    await axios.post(webhookUrl, payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  private async sendEmailNotification(channel: NotificationChannel, notification: any): Promise<void> {
    // Email integration would require additional setup (SMTP, SendGrid, etc.)
    console.log(`📧 Email notification (${channel.config.email}): ${notification.title}`);
  }

  private async sendDailyReport(): Promise<void> {
    try {
      const report = await this.healthReporter.generateDailyReport();
      const message = `📊 **Daily Health Report**

**Overall Health**: ${report.summary.overallHealth.overall}/100 (${report.summary.overallHealth.trend})

**Key Metrics**:
- Success Rate: ${report.summary.keyMetrics.successRate}%
- Block Rate: ${report.summary.keyMetrics.blockRate}%  
- Data Quality: ${report.summary.keyMetrics.dataQuality}/100
- Uptime: ${report.summary.keyMetrics.uptime}%

**Achievements**: ${report.summary.achievements.join(', ') || 'None'}
**Challenges**: ${report.summary.challenges.join(', ') || 'None'}

**Executive Summary**:
${report.executiveSummary}`;

      await (this as any).emit('notification', {
        type: 'system_status',
        severity: 'info',
        title: 'Daily Health Report',
        message,
        data: { report }
      });

      console.log('📊 Daily report sent');
    } catch (error) {
      console.error('Failed to send daily report:', error);
    }
  }

  private async sendWeeklyReport(): Promise<void> {
    try {
      const report = await this.healthReporter.generateWeeklyReport();
      
      // Include enhancement and research summaries if available
      let enhancementSummary = '';
      let researchSummary = '';
      
      if (this.enhancementEngine) {
        const stats = this.enhancementEngine.getEnhancementStats();
        enhancementSummary = `\n**Enhancements**: ${stats.total} total, ${stats.successRate}% success rate`;
      }
      
      if (this.researchAgent) {
        const research = this.researchAgent.getResearchSummary(7);
        researchSummary = `\n**Research**: ${research.newFindings} new findings, ${research.highImpactFindings} high-impact`;
      }

      const message = `📈 **Weekly Health Report**

**Overall Health**: ${report.summary.overallHealth.overall}/100 (${report.summary.overallHealth.trend})

**Performance Trends**:
- Success Rate: ${report.performance.successRate.current}% (${report.performance.successRate.change > 0 ? '+' : ''}${report.performance.successRate.change})
- Block Rate: ${report.performance.blockRate.current}% (${report.performance.blockRate.change > 0 ? '+' : ''}${report.performance.blockRate.change})
- Response Time: ${report.performance.responseTime.current}ms (${report.performance.responseTime.change > 0 ? '+' : ''}${report.performance.responseTime.change})

**Incidents**: ${report.incidents.length} incidents resolved
**Improvements**: ${report.improvements.length} improvements deployed${enhancementSummary}${researchSummary}

**Executive Summary**:
${report.executiveSummary}

**Industry Benchmarks**:
- Success Rate: ${report.benchmarks.industryComparison.successRate.percentile}th percentile
- Block Rate: ${report.benchmarks.industryComparison.blockRate.percentile}th percentile`;

      await (this as any).emit('notification', {
        type: 'system_status',
        severity: 'info',
        title: 'Weekly Health Report', 
        message,
        data: { report }
      });

      console.log('📈 Weekly report sent');
    } catch (error) {
      console.error('Failed to send weekly report:', error);
    }
  }

  private renderMessageTemplate(template: string, event: NotificationEvent): string {
    let message = template;
    
    // Basic template variables
    message = message.replace(/\{\{title\}\}/g, event.title);
    message = message.replace(/\{\{message\}\}/g, event.message);
    message = message.replace(/\{\{severity\}\}/g, event.severity);
    message = message.replace(/\{\{timestamp\}\}/g, new Date(event.timestamp).toISOString());
    
    // Metrics from current snapshot
    try {
      const currentMetrics = this.kpiMonitor.getCurrentSnapshot();
      if (currentMetrics?.overall) {
        message = message.replace(/\{\{metrics\.successRate\}\}/g, currentMetrics.overall.successRate?.toString() || '0');
        message = message.replace(/\{\{metrics\.blockRate\}\}/g, currentMetrics.overall.blockRate?.toString() || '0');
        message = message.replace(/\{\{metrics\.avgResponseTime\}\}/g, currentMetrics.overall.avgResponseTime?.toString() || '0');
      }
    } catch (error) {
      // Ignore template rendering errors
    }
    
    // Health metrics
    try {
      const dashboardData = this.healthReporter.getDashboardData();
      message = message.replace(/\{\{health\.overall\}\}/g, dashboardData.healthTrend[dashboardData.healthTrend.length - 1]?.score?.toString() || '0');
      message = message.replace(/\{\{metrics\.uptime\}\}/g, dashboardData.quickStats.uptime?.value?.toString() || '0');
      message = message.replace(/\{\{metrics\.dataQuality\}\}/g, dashboardData.quickStats.dataQuality?.value?.toString() || '0');
    } catch (error) {
      // Ignore template rendering errors
    }
    
    return message;
  }

  private createBatchSummary(events: NotificationEvent[]): string {
    const typeCounts = events.reduce((acc, event) => {
      acc[event.type] = (acc[event.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const severityCounts = events.reduce((acc, event) => {
      acc[event.severity] = (acc[event.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return `**Event Summary**:
${Object.entries(typeCounts).map(([type, count]) => `- ${type}: ${count}`).join('\n')}

**Severity Breakdown**:
${Object.entries(severityCounts).map(([severity, count]) => `- ${severity}: ${count}`).join('\n')}

**Most Recent Events**:
${events.slice(-3).map(e => `- ${e.title} (${e.severity})`).join('\n')}`;
  }

  private findRuleForBatch(batchKey: string): NotificationRule | null {
    const ruleId = batchKey.split('_')[0];
    return this.rules.get(ruleId) || null;
  }

  private extractNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private saveConfiguration(): void {
    try {
      writeFileSync(
        join(this.dataDir, 'communication-config.json'),
        JSON.stringify(this.config, null, 2)
      );
    } catch (error) {
      console.error('Failed to save communication configuration:', error);
    }
  }

  // Public API methods

  async addChannel(channel: NotificationChannel): Promise<void> {
    this.channels.set(channel.name, channel);
    this.config.channels = Array.from(this.channels.values());
    this.saveConfiguration();
    
    console.log(`➕ Added notification channel: ${channel.name} (${channel.type})`);
  }

  async removeChannel(channelName: string): Promise<void> {
    this.channels.delete(channelName);
    this.config.channels = Array.from(this.channels.values());
    this.saveConfiguration();
    
    console.log(`➖ Removed notification channel: ${channelName}`);
  }

  async addRule(rule: NotificationRule): Promise<void> {
    this.rules.set(rule.id, rule);
    this.config.rules = Array.from(this.rules.values());
    this.saveConfiguration();
    
    console.log(`📋 Added notification rule: ${rule.name}`);
  }

  async testNotification(channelName: string): Promise<boolean> {
    const channel = this.channels.get(channelName);
    if (!channel) {
      console.error(`Channel not found: ${channelName}`);
      return false;
    }

    try {
      await this.sendToChannel(channel, {
        title: 'Test Notification',
        message: 'This is a test message from the MCP Scraper Communication Layer',
        severity: 'info',
        timestamp: Date.now()
      });
      
      console.log(`✅ Test notification sent to ${channelName}`);
      return true;
    } catch (error) {
      console.error(`❌ Test notification failed for ${channelName}:`, error);
      return false;
    }
  }

  getStats(): {
    channels: number;
    rules: number;
    notifications: { sent: number; failed: number; batched: number; throttled: number };
    queueSize: number;
    batchedEvents: number;
  } {
    return {
      channels: this.channels.size,
      rules: this.rules.size,
      notifications: { ...this.notificationStats },
      queueSize: this.eventQueue.length,
      batchedEvents: Array.from(this.batchedEvents.values()).reduce((sum, batch) => sum + batch.length, 0)
    };
  }

  cleanup(): void {
    console.log('🧹 Cleaning up Communication Layer...');
    
    if (this.dashboardServer) {
      this.dashboardServer.close();
    }
    
    this.saveConfiguration();
    this.removeAllListeners();
    
    console.log('✅ Communication Layer cleanup complete');
  }
}