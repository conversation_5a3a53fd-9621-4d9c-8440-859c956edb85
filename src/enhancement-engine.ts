/**
 * Automated Enhancement Pipeline
 * 
 * Continuous improvement system that:
 * - Analyzes scraping performance weekly
 * - Generates LLM-powered enhancement proposals
 * - Creates A/B testing framework for new techniques
 * - Automatically creates PRs for improvements
 * - Manages enhancement lifecycle with CI/CD integration
 * - Tracks success metrics and rollback capabilities
 */

import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { execSync } from 'child_process';
import { KPIMonitoringSystem } from './kpi-monitoring-system.js';
import { DataQualityAnalyzer } from './data-quality-analyzer.js';
import { LLMOrchestrator } from './llm-orchestrator.js';
import { BlockAnalyzer } from './block-analyzer.js';

interface Enhancement {
  id: string;
  title: string;
  description: string;
  category: 'performance' | 'detection_evasion' | 'data_quality' | 'reliability' | 'cost_optimization';
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedImpact: {
    successRate: number;    // Expected improvement in success rate (%)
    blockRate: number;      // Expected reduction in block rate (%)
    dataQuality: number;    // Expected improvement in data quality (%)
    responseTime: number;   // Expected improvement in response time (%)
    costEfficiency: number; // Expected cost improvement (%)
  };
  implementation: {
    files: Array<{ path: string; changes: string; type: 'modify' | 'create' | 'delete' }>;
    dependencies: string[];
    configChanges: Record<string, any>;
    migrationSteps: string[];
  };
  testingPlan: {
    unitTests: string[];
    integrationTests: string[];
    performanceTests: string[];
    abTestConfig: ABTestConfig;
  };
  timeline: {
    generated: number;
    reviewed?: number;
    implemented?: number;
    tested?: number;
    deployed?: number;
  };
  metrics: {
    beforeMetrics?: PerformanceMetrics;
    afterMetrics?: PerformanceMetrics;
    improvement?: number;
  };
  status: 'proposed' | 'approved' | 'in_progress' | 'testing' | 'deployed' | 'rejected' | 'rolled_back';
  pullRequest?: {
    number: number;
    url: string;
    branch: string;
    status: 'open' | 'merged' | 'closed';
  };
}

interface ABTestConfig {
  name: string;
  trafficSplit: number; // Percentage of traffic for new approach
  duration: number;     // Test duration in milliseconds
  successCriteria: {
    minSuccessRate: number;
    maxBlockRate: number;
    minDataQuality: number;
  };
  rollbackTriggers: {
    successRateThreshold: number;
    blockRateThreshold: number;
    errorRateThreshold: number;
  };
}

interface PerformanceMetrics {
  successRate: number;
  blockRate: number;
  dataQualityScore: number;
  avgResponseTime: number;
  costPerRequest: number;
  timestamp: number;
}

interface EnhancementProposal {
  analysis: {
    currentIssues: string[];
    opportunities: string[];
    benchmarkGaps: string[];
    industryTrends: string[];
  };
  recommendations: Enhancement[];
  implementation: {
    schedule: Array<{ week: number; tasks: string[] }>;
    resources: { development: number; testing: number; deployment: number };
    risks: Array<{ risk: string; mitigation: string; probability: number }>;
  };
  expectedOutcome: {
    overallImprovement: number;
    timeframe: string;
    costBenefit: { investment: number; savings: number; roi: number };
  };
}

export class EnhancementEngine {
  private kpiMonitor: KPIMonitoringSystem;
  private qualityAnalyzer: DataQualityAnalyzer;
  private llmOrchestrator: LLMOrchestrator;
  private blockAnalyzer: BlockAnalyzer;
  
  private enhancements: Map<string, Enhancement> = new Map();
  private proposals: EnhancementProposal[] = [];
  private dataDir: string;
  private gitRepo: string;
  
  // A/B testing framework
  private activeTests: Map<string, ABTestConfig> = new Map();
  private testResults: Map<string, any> = new Map();

  constructor(
    kpiMonitor: KPIMonitoringSystem,
    qualityAnalyzer: DataQualityAnalyzer,
    llmOrchestrator: LLMOrchestrator,
    blockAnalyzer: BlockAnalyzer,
    dataDir: string = './data/enhancements',
    gitRepo: string = process.cwd()
  ) {
    this.kpiMonitor = kpiMonitor;
    this.qualityAnalyzer = qualityAnalyzer;
    this.llmOrchestrator = llmOrchestrator;
    this.blockAnalyzer = blockAnalyzer;
    this.dataDir = dataDir;
    this.gitRepo = gitRepo;
    
    this.ensureDataDir();
    this.loadHistoricalData();
  }

  private ensureDataDir(): void {
    if (!existsSync(this.dataDir)) {
      mkdirSync(this.dataDir, { recursive: true });
    }
  }

  private loadHistoricalData(): void {
    try {
      const enhancementsFile = join(this.dataDir, 'enhancements.json');
      const proposalsFile = join(this.dataDir, 'proposals.json');

      if (existsSync(enhancementsFile)) {
        const data = JSON.parse(readFileSync(enhancementsFile, 'utf-8'));
        data.enhancements?.forEach((enhancement: Enhancement) => {
          this.enhancements.set(enhancement.id, enhancement);
        });
      }

      if (existsSync(proposalsFile)) {
        const data = JSON.parse(readFileSync(proposalsFile, 'utf-8'));
        this.proposals = data.proposals || [];
      }

      console.log(`🔄 Enhancement Engine loaded: ${this.enhancements.size} enhancements, ${this.proposals.length} proposals`);
    } catch (error) {
      console.warn('Failed to load enhancement history:', error);
    }
  }

  async generateWeeklyEnhancements(): Promise<EnhancementProposal> {
    console.log('📊 Generating weekly enhancement analysis...');
    
    // Gather performance data
    const kpiSnapshot = this.kpiMonitor.getCurrentSnapshot();
    const blockingStats = this.blockAnalyzer.getBlockingStats();
    const qualityTrends = await this.analyzeQualityTrends();
    
    // Create analysis prompt for LLM
    const analysisPrompt = this.buildAnalysisPrompt(kpiSnapshot, blockingStats, qualityTrends);
    
    try {
      // Get LLM analysis
      const llmResponse = await this.llmOrchestrator['callLLM']('smart', analysisPrompt);
      const parsedAnalysis = this.parseLLMAnalysis(llmResponse);
      
      // Generate specific enhancement proposals
      const enhancements = await this.generateEnhancementProposals(parsedAnalysis);
      
      const proposal: EnhancementProposal = {
        analysis: parsedAnalysis,
        recommendations: enhancements,
        implementation: this.createImplementationPlan(enhancements),
        expectedOutcome: this.calculateExpectedOutcome(enhancements)
      };

      this.proposals.push(proposal);
      this.persistData();
      
      console.log(`✅ Generated ${enhancements.length} enhancement proposals`);
      return proposal;
    } catch (error) {
      console.error('Failed to generate enhancements:', error);
      return this.createFallbackProposal();
    }
  }

  private buildAnalysisPrompt(kpiSnapshot: any, blockingStats: any, qualityTrends: any): string {
    return `You are an expert web scraping engineer analyzing system performance to identify improvement opportunities.

CURRENT PERFORMANCE DATA:
Success Rate: ${kpiSnapshot?.overall?.successRate || 0}%
Block Rate: ${kpiSnapshot?.overall?.blockRate || 0}%
Average Response Time: ${kpiSnapshot?.overall?.avgResponseTime || 0}ms
Cost Per Request: $${kpiSnapshot?.overall?.costPerRequest || 0}

BLOCKING ANALYSIS:
- Total blocking events: ${blockingStats.totalEvents}
- Successful mitigations: ${blockingStats.successfulMitigations}
- Top block types: ${blockingStats.topBlockTypes.map((t: any) => `${t.type}(${t.count})`).join(', ')}
- Strategy effectiveness: ${blockingStats.strategyEffectiveness.map((s: any) => `${s.strategy}:${s.successRate}%`).join(', ')}

QUALITY TRENDS:
${qualityTrends}

INDUSTRY BENCHMARKS:
- Target Success Rate: >95%
- Target Block Rate: <1%
- Target Response Time: <3s
- Target Cost: <$0.01 per request

Please analyze and respond with JSON:
{
  "currentIssues": ["issue1", "issue2", ...],
  "opportunities": ["opportunity1", "opportunity2", ...],
  "benchmarkGaps": ["gap1", "gap2", ...],
  "industryTrends": ["trend1", "trend2", ...]
}

Focus on actionable insights that could improve performance metrics, reduce costs, or enhance reliability.`;
  }

  private async analyzeQualityTrends(): Promise<string> {
    // Get quality trend data for multiple domains
    const trends = ['x.com', 'reddit.com', 'general'].map(domain => {
      const trend = this.qualityAnalyzer.getQualityTrend(domain);
      return `${domain}: ${trend.trend} (${trend.currentScore}/100, change: ${trend.change})`;
    });
    
    const topIssues = this.qualityAnalyzer.getTopIssues(5)
      .map(issue => `${issue.issue} (${issue.frequency}x, ${issue.severity})`)
      .join(', ');

    return `Quality Trends: ${trends.join(', ')}. Top Issues: ${topIssues}`;
  }

  private parseLLMAnalysis(response: string): EnhancementProposal['analysis'] {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.warn('Failed to parse LLM analysis:', error);
    }

    return {
      currentIssues: ['Performance needs improvement'],
      opportunities: ['Optimize rate limiting', 'Improve blocking detection'],
      benchmarkGaps: ['Success rate below 95%'],
      industryTrends: ['Advanced fingerprinting protection']
    };
  }

  private async generateEnhancementProposals(analysis: EnhancementProposal['analysis']): Promise<Enhancement[]> {
    const enhancements: Enhancement[] = [];
    
    // Generate enhancement for each identified issue/opportunity
    for (const issue of analysis.currentIssues.slice(0, 3)) {
      const enhancement = await this.createEnhancementFromIssue(issue, 'high');
      if (enhancement) {enhancements.push(enhancement);}
    }

    for (const opportunity of analysis.opportunities.slice(0, 2)) {
      const enhancement = await this.createEnhancementFromIssue(opportunity, 'medium');
      if (enhancement) {enhancements.push(enhancement);}
    }

    return enhancements;
  }

  private async createEnhancementFromIssue(issue: string, priority: Enhancement['priority']): Promise<Enhancement | null> {
    const enhancementId = `enh_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
    
    // Use LLM to generate specific enhancement details
    const enhancementPrompt = `Create a specific technical enhancement to address: "${issue}"

Respond with JSON:
{
  "title": "Enhancement Title",
  "description": "Detailed technical description",
  "category": "performance|detection_evasion|data_quality|reliability|cost_optimization",
  "files": [{"path": "src/file.ts", "changes": "Description of changes", "type": "modify"}],
  "dependencies": ["package1", "package2"],
  "estimatedImpact": {
    "successRate": 5,
    "blockRate": -2,
    "dataQuality": 3,
    "responseTime": -10,
    "costEfficiency": 15
  }
}`;

    try {
      const response = await this.llmOrchestrator['callLLM']('smart', enhancementPrompt);
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      
      if (jsonMatch) {
        const enhancementData = JSON.parse(jsonMatch[0]);
        
        return {
          id: enhancementId,
          title: enhancementData.title || `Address: ${issue}`,
          description: enhancementData.description || `Enhancement to address: ${issue}`,
          category: enhancementData.category || 'performance',
          priority,
          estimatedImpact: enhancementData.estimatedImpact || {
            successRate: 2,
            blockRate: -1,
            dataQuality: 1,
            responseTime: -5,
            costEfficiency: 5
          },
          implementation: {
            files: enhancementData.files || [],
            dependencies: enhancementData.dependencies || [],
            configChanges: {},
            migrationSteps: []
          },
          testingPlan: {
            unitTests: [],
            integrationTests: [],
            performanceTests: [],
            abTestConfig: this.createDefaultABTest(`test_${enhancementId}`)
          },
          timeline: {
            generated: Date.now()
          },
          metrics: {},
          status: 'proposed'
        };
      }
    } catch (error) {
      console.error('Failed to generate enhancement details:', error);
    }

    return null;
  }

  private createDefaultABTest(name: string): ABTestConfig {
    return {
      name,
      trafficSplit: 10, // Start with 10% traffic
      duration: 7 * 24 * 60 * 60 * 1000, // 7 days
      successCriteria: {
        minSuccessRate: 90,
        maxBlockRate: 5,
        minDataQuality: 85
      },
      rollbackTriggers: {
        successRateThreshold: 70,
        blockRateThreshold: 15,
        errorRateThreshold: 20
      }
    };
  }

  private createImplementationPlan(enhancements: Enhancement[]): EnhancementProposal['implementation'] {
    const weeks = Math.ceil(enhancements.length / 2);
    const schedule = [];
    
    for (let week = 1; week <= weeks; week++) {
      const weekEnhancements = enhancements.slice((week - 1) * 2, week * 2);
      schedule.push({
        week,
        tasks: weekEnhancements.map(e => `Implement: ${e.title}`)
      });
    }

    return {
      schedule,
      resources: {
        development: enhancements.length * 8, // 8 hours per enhancement
        testing: enhancements.length * 4,     // 4 hours testing
        deployment: enhancements.length * 2   // 2 hours deployment
      },
      risks: [
        { risk: 'Integration issues', mitigation: 'Thorough testing', probability: 0.3 },
        { risk: 'Performance regression', mitigation: 'A/B testing', probability: 0.2 },
        { risk: 'Unexpected blocking', mitigation: 'Gradual rollout', probability: 0.15 }
      ]
    };
  }

  private calculateExpectedOutcome(enhancements: Enhancement[]): EnhancementProposal['expectedOutcome'] {
    const totalImpact = enhancements.reduce((sum, e) => ({
      successRate: sum.successRate + e.estimatedImpact.successRate,
      blockRate: sum.blockRate + e.estimatedImpact.blockRate,
      dataQuality: sum.dataQuality + e.estimatedImpact.dataQuality,
      responseTime: sum.responseTime + e.estimatedImpact.responseTime,
      costEfficiency: sum.costEfficiency + e.estimatedImpact.costEfficiency
    }), {
      successRate: 0,
      blockRate: 0,
      dataQuality: 0,
      responseTime: 0,
      costEfficiency: 0
    });

    const overallImprovement = (
      totalImpact.successRate +
      Math.abs(totalImpact.blockRate) +
      totalImpact.dataQuality +
      Math.abs(totalImpact.responseTime) / 10 +
      totalImpact.costEfficiency
    ) / 5;

    return {
      overallImprovement: Math.round(overallImprovement),
      timeframe: `${Math.ceil(enhancements.length / 2)} weeks`,
      costBenefit: {
        investment: enhancements.length * 1000, // $1000 per enhancement
        savings: Math.round(totalImpact.costEfficiency * 100), // Annual savings
        roi: Math.round((totalImpact.costEfficiency * 100) / (enhancements.length * 1000) * 100)
      }
    };
  }

  async createPullRequest(enhancement: Enhancement): Promise<boolean> {
    console.log(`🔀 Creating PR for enhancement: ${enhancement.title}`);
    
    try {
      // Create feature branch
      const branchName = `enhancement/${enhancement.id}`;
      execSync(`git checkout -b ${branchName}`, { cwd: this.gitRepo });
      
      // Apply file changes
      await this.applyEnhancementChanges(enhancement);
      
      // Commit changes
      execSync('git add .', { cwd: this.gitRepo });
      execSync(`git commit -m "feat: ${enhancement.title}\n\n${enhancement.description}\n\n🤖 Generated with Claude Code Enhancement Engine"`, { cwd: this.gitRepo });
      
      // Push branch
      execSync(`git push -u origin ${branchName}`, { cwd: this.gitRepo });
      
      // Create PR using GitHub CLI
      const prResult = execSync(`gh pr create --title "🚀 ${enhancement.title}" --body "${this.generatePRDescription(enhancement)}" --label "enhancement,automated"`, { 
        cwd: this.gitRepo,
        encoding: 'utf-8'
      });
      
      const prUrl = prResult.trim();
      const prNumber = parseInt(prUrl.split('/').pop() || '0');
      
      // Update enhancement with PR info
      enhancement.pullRequest = {
        number: prNumber,
        url: prUrl,
        branch: branchName,
        status: 'open'
      };
      
      enhancement.status = 'in_progress';
      enhancement.timeline.implemented = Date.now();
      
      this.enhancements.set(enhancement.id, enhancement);
      this.persistData();
      
      console.log(`✅ PR created: ${prUrl}`);
      return true;
    } catch (error) {
      console.error('Failed to create PR:', error);
      
      // Cleanup: return to main branch
      try {
        execSync('git checkout main', { cwd: this.gitRepo });
        execSync(`git branch -D enhancement/${enhancement.id}`, { cwd: this.gitRepo });
      } catch (cleanupError) {
        console.warn('Failed to cleanup after PR creation failure:', cleanupError);
      }
      
      return false;
    }
  }

  private async applyEnhancementChanges(enhancement: Enhancement): Promise<void> {
    for (const fileChange of enhancement.implementation.files) {
      const filePath = join(this.gitRepo, fileChange.path);
      
      if (fileChange.type === 'create') {
        // Create new file with the changes content
        writeFileSync(filePath, fileChange.changes, 'utf-8');
      } else if (fileChange.type === 'modify') {
        // For now, we'll add the changes as comments
        // In a full implementation, you'd parse and apply specific modifications
        if (existsSync(filePath)) {
          const existingContent = readFileSync(filePath, 'utf-8');
          const modifiedContent = `${existingContent}\n\n// Enhancement: ${enhancement.title}\n// TODO: ${fileChange.changes}\n`;
          writeFileSync(filePath, modifiedContent, 'utf-8');
        }
      }
      // Delete type would use fs.unlinkSync(filePath)
    }
  }

  private generatePRDescription(enhancement: Enhancement): string {
    const impact = enhancement.estimatedImpact;
    const impactSummary = [
      impact.successRate > 0 ? `+${impact.successRate}% success rate` : null,
      impact.blockRate < 0 ? `${impact.blockRate}% block rate` : null,
      impact.dataQuality > 0 ? `+${impact.dataQuality}% data quality` : null,
      impact.responseTime < 0 ? `${impact.responseTime}% response time` : null,
      impact.costEfficiency > 0 ? `+${impact.costEfficiency}% cost efficiency` : null
    ].filter(Boolean).join(', ');

    return `## Summary
${enhancement.description}

## Expected Impact
${impactSummary}

## Category
${enhancement.category.replace('_', ' ').toUpperCase()}

## Testing Plan
- A/B test with ${enhancement.testingPlan.abTestConfig.trafficSplit}% traffic split
- Duration: ${Math.round(enhancement.testingPlan.abTestConfig.duration / (24 * 60 * 60 * 1000))} days
- Success criteria: ${enhancement.testingPlan.abTestConfig.successCriteria.minSuccessRate}% success rate

## Files Modified
${enhancement.implementation.files.map(f => `- \`${f.path}\` (${f.type})`).join('\n')}

${enhancement.implementation.dependencies.length > 0 ? `## Dependencies
${enhancement.implementation.dependencies.map(d => `- ${d}`).join('\n')}` : ''}

---
🤖 This enhancement was automatically generated and implemented by the Claude Code Enhancement Engine.

Generated at: ${new Date(enhancement.timeline.generated).toISOString()}`;
  }

  async startABTest(enhancement: Enhancement): Promise<boolean> {
    console.log(`🧪 Starting A/B test for: ${enhancement.title}`);
    
    const testConfig = enhancement.testingPlan.abTestConfig;
    this.activeTests.set(enhancement.id, testConfig);
    
    // Initialize test metrics collection
    this.testResults.set(enhancement.id, {
      startTime: Date.now(),
      controlMetrics: [],
      treatmentMetrics: [],
      status: 'running'
    });
    
    enhancement.status = 'testing';
    enhancement.timeline.tested = Date.now();
    
    this.enhancements.set(enhancement.id, enhancement);
    this.persistData();
    
    // Schedule test evaluation
    setTimeout(() => {
      void this.evaluateABTest(enhancement.id);
    }, testConfig.duration);
    
    console.log(`✅ A/B test started for ${testConfig.duration / (1000 * 60 * 60)} hours`);
    return true;
  }

  private async evaluateABTest(enhancementId: string): Promise<void> {
    console.log(`📊 Evaluating A/B test for enhancement: ${enhancementId}`);
    
    const enhancement = this.enhancements.get(enhancementId);
    const testConfig = this.activeTests.get(enhancementId);
    const testResults = this.testResults.get(enhancementId);
    
    if (!enhancement || !testConfig || !testResults) {
      console.error('Missing test data for evaluation');
      return;
    }
    
    // Collect final metrics (simplified - would use actual metrics in production)
    const currentSnapshot = this.kpiMonitor.getCurrentSnapshot();
    const treatmentMetrics = {
      successRate: currentSnapshot?.overall?.successRate || 0,
      blockRate: currentSnapshot?.overall?.blockRate || 0,
      dataQuality: 85 // Would get actual data quality score
    };
    
    // Evaluate against success criteria
    const success = (
      treatmentMetrics.successRate >= testConfig.successCriteria.minSuccessRate &&
      treatmentMetrics.blockRate <= testConfig.successCriteria.maxBlockRate &&
      treatmentMetrics.dataQuality >= testConfig.successCriteria.minDataQuality
    );
    
    if (success) {
      console.log(`✅ A/B test passed - deploying enhancement: ${enhancement.title}`);
      await this.deployEnhancement(enhancement);
    } else {
      console.log(`❌ A/B test failed - rolling back enhancement: ${enhancement.title}`);
      await this.rollbackEnhancement(enhancement);
    }
    
    // Clean up test
    this.activeTests.delete(enhancementId);
    testResults.status = success ? 'passed' : 'failed';
    testResults.endTime = Date.now();
    
    this.persistData();
  }

  private async deployEnhancement(enhancement: Enhancement): Promise<void> {
    enhancement.status = 'deployed';
    enhancement.timeline.deployed = Date.now();
    
    // Record before/after metrics
    enhancement.metrics = {
      afterMetrics: {
        successRate: this.kpiMonitor.getCurrentSnapshot()?.overall?.successRate || 0,
        blockRate: this.kpiMonitor.getCurrentSnapshot()?.overall?.blockRate || 0,
        dataQualityScore: 85, // Would get actual score
        avgResponseTime: this.kpiMonitor.getCurrentSnapshot()?.overall?.avgResponseTime || 0,
        costPerRequest: this.kpiMonitor.getCurrentSnapshot()?.overall?.costPerRequest || 0,
        timestamp: Date.now()
      }
    };
    
    // Close PR if it exists
    if (enhancement.pullRequest) {
      try {
        execSync(`gh pr merge ${enhancement.pullRequest.number} --squash`, { cwd: this.gitRepo });
        enhancement.pullRequest.status = 'merged';
      } catch (error) {
        console.warn('Failed to merge PR:', error);
      }
    }
    
    this.enhancements.set(enhancement.id, enhancement);
    console.log(`🚀 Enhancement deployed successfully: ${enhancement.title}`);
  }

  private async rollbackEnhancement(enhancement: Enhancement): Promise<void> {
    enhancement.status = 'rolled_back';
    
    // Close PR without merging
    if (enhancement.pullRequest && enhancement.pullRequest.status === 'open') {
      try {
        execSync(`gh pr close ${enhancement.pullRequest.number}`, { cwd: this.gitRepo });
        enhancement.pullRequest.status = 'closed';
      } catch (error) {
        console.warn('Failed to close PR:', error);
      }
    }
    
    this.enhancements.set(enhancement.id, enhancement);
    console.log(`⏪ Enhancement rolled back: ${enhancement.title}`);
  }

  private createFallbackProposal(): EnhancementProposal {
    return {
      analysis: {
        currentIssues: ['Unable to generate detailed analysis'],
        opportunities: ['Improve monitoring and analysis'],
        benchmarkGaps: ['Analysis system needs enhancement'],
        industryTrends: ['Better integration needed']
      },
      recommendations: [],
      implementation: {
        schedule: [],
        resources: { development: 0, testing: 0, deployment: 0 },
        risks: []
      },
      expectedOutcome: {
        overallImprovement: 0,
        timeframe: '0 weeks',
        costBenefit: { investment: 0, savings: 0, roi: 0 }
      }
    };
  }

  private persistData(): void {
    try {
      writeFileSync(
        join(this.dataDir, 'enhancements.json'),
        JSON.stringify({ enhancements: Array.from(this.enhancements.values()) }, null, 2)
      );
      
      writeFileSync(
        join(this.dataDir, 'proposals.json'),
        JSON.stringify({ proposals: this.proposals.slice(-10) }, null, 2) // Keep last 10 proposals
      );
    } catch (error) {
      console.error('Failed to persist enhancement data:', error);
    }
  }

  getEnhancementStats(): {
    total: number;
    byStatus: Record<string, number>;
    successRate: number;
    averageImpact: number;
    activeTests: number;
  } {
    const enhancements = Array.from(this.enhancements.values());
    
    const byStatus = enhancements.reduce((acc, e) => {
      acc[e.status] = (acc[e.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const deployed = enhancements.filter(e => e.status === 'deployed');
    const successRate = enhancements.length > 0 ? (deployed.length / enhancements.length) * 100 : 0;
    
    const averageImpact = deployed.length > 0 
      ? deployed.reduce((sum, e) => sum + (e.metrics?.improvement || 0), 0) / deployed.length
      : 0;

    return {
      total: enhancements.length,
      byStatus,
      successRate: Math.round(successRate),
      averageImpact: Math.round(averageImpact),
      activeTests: this.activeTests.size
    };
  }

  cleanup(): void {
    console.log('🧹 Cleaning up Enhancement Engine...');
    this.persistData();
    console.log('✅ Enhancement Engine cleanup complete');
  }
}