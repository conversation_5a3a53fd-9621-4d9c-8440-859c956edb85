import { spawn, ChildProcess } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs';

interface AgentResponse {
  success: boolean;
  data?: {
    session_id?: string;
    response?: string;
    metadata?: Record<string, unknown>;
    report?: string;
    trending?: unknown[];
    statistics?: unknown;
  };
  error?: string;
  type?: string;
}

interface ChatResponse {
  session_id: string;
  response: string;
  metadata: Record<string, unknown>;
}

export class AgentBridge {
  private pythonProcess: ChildProcess | null = null;
  private responseCallbacks: Map<number, (response: AgentResponse) => void> = new Map();
  private requestId = 0;

  constructor() {
    this.initializePythonProcess();
  }

  private findPythonExecutable(): string {
    // Priority order for Python executable detection
    const candidates = [
      // Virtual environment Python (highest priority)
      path.join(process.cwd(), 'venv', 'bin', 'python'),
      path.join(process.cwd(), 'venv', 'bin', 'python3'),
      // System Python
      'python3',
      'python'
    ];

    for (const candidate of candidates) {
      if (path.isAbsolute(candidate)) {
        if (fs.existsSync(candidate)) {
          console.log(`✅ Found Python executable: ${candidate}`);
          return candidate;
        }
      } else {
        // For system commands, we'll try them and let spawn handle the error
        console.log(`🔍 Trying system Python: ${candidate}`);
        return candidate;
      }
    }

    console.warn('⚠️  No Python executable found, falling back to python3');
    return 'python3';
  }

  private initializePythonProcess(): void {
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);

    // Python script is in src directory, not build directory
    const pythonScript = path.join(process.cwd(), 'src', 'python_bridge.py');

    // Enhanced Python environment detection
    const pythonCmd = this.findPythonExecutable();

    // Validate Python script exists
    if (!fs.existsSync(pythonScript)) {
      throw new Error(`Python bridge script not found: ${pythonScript}`);
    }

    // Validate OpenAI API key
    if (!process.env.OPENAI_API_KEY) {
      console.warn('⚠️  OPENAI_API_KEY not set - AI agent functionality will be limited');
    }

    console.log(`🐍 Starting Python process with: ${pythonCmd}`);

    this.pythonProcess = spawn(pythonCmd, [pythonScript], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        PYTHONPATH: `${__dirname}:${process.env.PYTHONPATH || ''}`,
        OPENAI_API_KEY: process.env.OPENAI_API_KEY,
        // Ensure virtual environment is properly activated
        VIRTUAL_ENV: path.join(process.cwd(), 'venv'),
        PATH: `${path.join(process.cwd(), 'venv', 'bin')}:${process.env.PATH}`
      }
    });

    if (!this.pythonProcess.stdout || !this.pythonProcess.stdin) {
      throw new Error('Failed to initialize Python process streams');
    }

    // Handle responses
    let buffer = '';
    this.pythonProcess.stdout.on('data', (data: Buffer) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim()) {
          try {
            const response = JSON.parse(line) as AgentResponse;
            this.handlePythonResponse(response);
          } catch (error) {
            console.error('Failed to parse Python response:', error);
          }
        }
      }
    });

    // Handle errors
    this.pythonProcess.stderr?.on('data', (data: Buffer) => {
      console.error('Python process error:', data.toString());
    });

    this.pythonProcess.on('close', (code) => {
      console.log(`Python process exited with code ${code}`);
      this.pythonProcess = null;
    });
  }

  private handlePythonResponse(response: AgentResponse): void {
    // For now, we'll handle responses synchronously
    // In a production system, you'd want to match responses to requests
    this.lastResponse = response;
  }

  private lastResponse: AgentResponse | null = null;

  private async sendToPython(request: Record<string, unknown>): Promise<AgentResponse> {
    return new Promise((resolve, reject) => {
      if (!this.pythonProcess?.stdin) {
        reject(new Error('Python process not available'));
        return;
      }

      const requestJson = JSON.stringify(request) + '\n';
      this.pythonProcess.stdin.write(requestJson);

      // Simple polling approach - in production, use proper request/response matching
      const checkResponse = (): void => {
        if (this.lastResponse) {
          const response = this.lastResponse;
          this.lastResponse = null;
          resolve(response);
        } else {
          setTimeout(checkResponse, 100);
        }
      };
      
      setTimeout(checkResponse, 100);
      
      // Timeout after 30 seconds
      setTimeout(() => {
        reject(new Error('Request timeout'));
      }, 30000);
    });
  }

  async createChatSession(): Promise<string> {
    const response = await this.sendToPython({ action: 'create_session' });
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to create session');
    }
    
    return response.data?.session_id ?? '';
  }

  async chat(sessionId: string, message: string): Promise<ChatResponse> {
    const response = await this.sendToPython({
      action: 'chat',
      session_id: sessionId,
      message: message
    });
    
    if (!response.success) {
      throw new Error(response.error || 'Chat request failed');
    }
    
    return response.data as ChatResponse;
  }

  async getSessionInfo(sessionId: string): Promise<any> {
    const response = await this.sendToPython({
      action: 'get_session',
      session_id: sessionId
    });
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to get session info');
    }
    
    return response.data;
  }

  async exportReport(sessionId: string, format: 'json' | 'markdown' | 'html' = 'json'): Promise<string> {
    const response = await this.sendToPython({
      action: 'export_report',
      session_id: sessionId,
      format: format
    });
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to export report');
    }
    
    return response.data?.report ?? '';
  }

  async getTrendingKeywords(sources: string[] = ['both'], limit: number = 10): Promise<any[]> {
    const response = await this.sendToPython({
      action: 'get_trending',
      sources: sources,
      limit: limit
    });
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to get trending keywords');
    }
    
    return response.data?.trending ?? [];
  }

  async analyzeKeywords(keywords: string[], sources: string[] = ['both']): Promise<any> {
    const response = await this.sendToPython({
      action: 'analyze_keywords',
      keywords: keywords,
      sources: sources
    });
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to analyze keywords');
    }
    
    return response.data?.statistics ?? {};
  }

  cleanup() {
    if (this.pythonProcess) {
      this.pythonProcess.kill();
      this.pythonProcess = null;
    }
  }
}

// Singleton instance
let agentBridge: AgentBridge | null = null;

export function getAgentBridge(): AgentBridge {
  if (!agentBridge) {
    agentBridge = new AgentBridge();
  }
  return agentBridge;
}

// Cleanup on process exit
process.on('exit', () => {
  if (agentBridge) {
    agentBridge.cleanup();
  }
});