/**
 * Block Analysis and Mitigation System
 * 
 * Advanced blocking detection and automated mitigation:
 * - Screenshot-based block detection with visual analysis
 * - Challenge type identification (CAPTCHA, CloudFlare, etc.)
 * - Automatic mitigation strategy selection and execution  
 * - Learning from block patterns to prevent future occurrences
 * - Integration with LLM orchestrator for intelligent responses
 * - Real-time adaptation to new blocking mechanisms
 */

import { Page } from 'puppeteer';
import { VisualScrapingEngine, ScreenshotAnalysis } from './visual-scraping-engine.js';
import { EnhancedEvasionSystem } from './enhanced-evasion-system.js';
import { LLMOrchestrator, LLMDecision } from './llm-orchestrator.js';
import { writeFileSync, existsSync, readFileSync, mkdirSync } from 'fs';
import { join } from 'path';

interface BlockDetection {
  detected: boolean;
  type: 'soft_block' | 'hard_block' | 'captcha' | 'rate_limit' | 'cloudflare' | 'custom' | 'unknown';
  confidence: number;
  indicators: string[];
  screenshot?: string;
  timestamp: number;
  domain: string;
  url: string;
  responseCode?: number;
  headers?: Record<string, string>;
  content?: string;
}

interface BlockPattern {
  id: string;
  name: string;
  type: BlockDetection['type'];
  domain: string;
  indicators: {
    visual: string[];        // Visual elements that indicate blocking
    textual: string[];       // Text patterns that indicate blocking
    technical: string[];     // HTTP/technical indicators
    behavioral: string[];    // Behavioral patterns
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
  frequency: number;        // How often this pattern occurs
  lastSeen: number;
  mitigation?: MitigationStrategy;
}

interface MitigationStrategy {
  name: string;
  description: string;
  steps: MitigationStep[];
  successRate: number;
  avgRecoveryTime: number; // in milliseconds
  lastUsed: number;
  effectiveness: 'low' | 'medium' | 'high';
}

interface MitigationStep {
  type: 'wait' | 'change_profile' | 'change_proxy' | 'solve_captcha' | 'navigate_away' | 'clear_cookies' | 'change_strategy' | 'manual_intervention';
  duration?: number;
  parameters?: Record<string, unknown>;
  description: string;
}

interface BlockingEvent {
  id: string;
  timestamp: number;
  domain: string;
  url: string;
  detection: BlockDetection;
  mitigation?: {
    strategy: string;
    applied: boolean;
    success: boolean;
    recoveryTime: number;
    steps: Array<{ step: MitigationStep; result: boolean; duration: number }>;
  };
  resolution: 'resolved' | 'escalated' | 'abandoned' | 'pending';
  learnings?: string[];
}

export class BlockAnalyzer {
  private visualEngine: VisualScrapingEngine;
  private evasionSystem: EnhancedEvasionSystem;
  private llmOrchestrator: LLMOrchestrator;
  
  private blockPatterns: Map<string, BlockPattern> = new Map();
  private blockingEvents: BlockingEvent[] = [];
  private mitigationStrategies: Map<string, MitigationStrategy> = new Map();
  
  private dataDir: string;
  private knowledgeBase: Map<string, any> = new Map();

  constructor(
    visualEngine: VisualScrapingEngine,
    evasionSystem: EnhancedEvasionSystem,
    llmOrchestrator: LLMOrchestrator,
    dataDir: string = './data/blocks'
  ) {
    this.visualEngine = visualEngine;
    this.evasionSystem = evasionSystem;
    this.llmOrchestrator = llmOrchestrator;
    this.dataDir = dataDir;
    
    this.ensureDataDir();
    this.initializeDefaultPatterns();
    this.initializeMitigationStrategies();
    this.loadHistoricalData();
  }

  private ensureDataDir(): void {
    if (!existsSync(this.dataDir)) {
      mkdirSync(this.dataDir, { recursive: true });
    }
  }

  private initializeDefaultPatterns(): void {
    // CloudFlare patterns
    this.addBlockPattern({
      id: 'cloudflare_challenge',
      name: 'CloudFlare Security Challenge',
      type: 'cloudflare',
      domain: '*',
      indicators: {
        visual: ['cloudflare', 'security check', 'checking your browser', 'please wait'],
        textual: ['cloudflare', 'ddos protection', 'browser check', 'ray id'],
        technical: ['cf-ray', 'cloudflare-nginx', '503', 'server temporarily unavailable'],
        behavioral: ['redirect_loop', 'javascript_challenge', 'long_load_time']
      },
      severity: 'high',
      frequency: 0,
      lastSeen: 0
    });

    // CAPTCHA patterns
    this.addBlockPattern({
      id: 'recaptcha_challenge',
      name: 'reCAPTCHA Challenge',
      type: 'captcha',
      domain: '*',
      indicators: {
        visual: ['recaptcha', "i'm not a robot", 'select all images', 'verify you are human'],
        textual: ['recaptcha', 'captcha', 'prove you are human', 'security verification'],
        technical: ['google.com/recaptcha', 'recaptcha_token', 'g-recaptcha'],
        behavioral: ['captcha_modal', 'challenge_popup']
      },
      severity: 'medium',
      frequency: 0,
      lastSeen: 0
    });

    // Rate limiting patterns
    this.addBlockPattern({
      id: 'rate_limit_429',
      name: 'HTTP 429 Rate Limit',
      type: 'rate_limit',
      domain: '*',
      indicators: {
        visual: ['too many requests', 'rate limit', 'slow down', 'try again later'],
        textual: ['rate limit exceeded', '429', 'too many requests', 'quota exceeded'],
        technical: ['429', 'retry-after', 'x-ratelimit'],
        behavioral: ['repeated_429', 'backoff_required']
      },
      severity: 'medium',
      frequency: 0,
      lastSeen: 0
    });

    // Bot detection patterns
    this.addBlockPattern({
      id: 'bot_detection_403',
      name: 'Bot Detection Block',
      type: 'hard_block',
      domain: '*',
      indicators: {
        visual: ['access denied', 'bot detected', 'automated request', 'suspicious activity'],
        textual: ['403', 'forbidden', 'bot detected', 'unauthorized', 'blocked'],
        technical: ['403', 'x-blocked', 'bot-protection'],
        behavioral: ['immediate_block', 'no_content_loaded']
      },
      severity: 'critical',
      frequency: 0,
      lastSeen: 0
    });

    // Social media specific patterns
    this.addBlockPattern({
      id: 'twitter_login_wall',
      name: 'Twitter Login Requirement',
      type: 'soft_block',
      domain: 'x.com',
      indicators: {
        visual: ['log in', 'sign up', 'create account', 'join twitter'],
        textual: ['something went wrong', 'try reloading', 'login required'],
        technical: ['login_required', 'auth_needed'],
        behavioral: ['redirect_to_login', 'content_hidden']
      },
      severity: 'medium',
      frequency: 0,
      lastSeen: 0
    });

    console.log(`🛡️  Block Analyzer initialized with ${this.blockPatterns.size} detection patterns`);
  }

  private initializeMitigationStrategies(): void {
    // CloudFlare mitigation
    this.addMitigationStrategy({
      name: 'cloudflare_wait_and_retry',
      description: 'Wait for CloudFlare challenge to complete and retry',
      steps: [
        { type: 'wait', duration: 5000, description: 'Wait for CloudFlare challenge' },
        { type: 'change_profile', description: 'Switch to different browser profile' },
        { type: 'wait', duration: 2000, description: 'Allow profile to settle' },
        { type: 'navigate_away', description: 'Navigate to safe page first' },
        { type: 'wait', duration: 3000, description: 'Wait before retry' }
      ],
      successRate: 75,
      avgRecoveryTime: 15000,
      lastUsed: 0,
      effectiveness: 'medium'
    });

    // Rate limit mitigation
    this.addMitigationStrategy({
      name: 'rate_limit_backoff',
      description: 'Exponential backoff for rate limits',
      steps: [
        { type: 'wait', duration: 10000, description: 'Initial backoff wait' },
        { type: 'change_proxy', description: 'Switch to different proxy' },
        { type: 'change_profile', description: 'Change browser profile' },
        { type: 'wait', duration: 5000, description: 'Allow changes to settle' }
      ],
      successRate: 85,
      avgRecoveryTime: 20000,
      lastUsed: 0,
      effectiveness: 'high'
    });

    // Bot detection mitigation
    this.addMitigationStrategy({
      name: 'bot_detection_full_reset',
      description: 'Full session reset for bot detection',
      steps: [
        { type: 'clear_cookies', description: 'Clear all cookies and storage' },
        { type: 'change_proxy', description: 'Switch to clean proxy' },
        { type: 'change_profile', description: 'Switch to human-like profile' },
        { type: 'wait', duration: 30000, description: 'Cool-down period' },
        { type: 'navigate_away', description: 'Visit neutral site first' }
      ],
      successRate: 60,
      avgRecoveryTime: 45000,
      lastUsed: 0,
      effectiveness: 'medium'
    });

    // CAPTCHA mitigation
    this.addMitigationStrategy({
      name: 'captcha_manual_intervention',
      description: 'Handle CAPTCHA with manual intervention',
      steps: [
        { type: 'manual_intervention', description: 'Alert for manual CAPTCHA solving' },
        { type: 'wait', duration: 60000, description: 'Wait for manual intervention' }
      ],
      successRate: 95,
      avgRecoveryTime: 60000,
      lastUsed: 0,
      effectiveness: 'high'
    });

    // Login wall mitigation
    this.addMitigationStrategy({
      name: 'login_wall_bypass',
      description: 'Attempt to bypass login requirements',
      steps: [
        { type: 'navigate_away', description: 'Try direct URL approach' },
        { type: 'change_profile', description: 'Switch to authenticated profile' },
        { type: 'wait', duration: 3000, description: 'Allow authentication to settle' }
      ],
      successRate: 40,
      avgRecoveryTime: 10000,
      lastUsed: 0,
      effectiveness: 'low'
    });

    console.log(`🔧 Initialized ${this.mitigationStrategies.size} mitigation strategies`);
  }

  private loadHistoricalData(): void {
    try {
      const eventsFile = join(this.dataDir, 'blocking_events.json');
      const patternsFile = join(this.dataDir, 'patterns.json');
      const strategiesFile = join(this.dataDir, 'strategies.json');

      if (existsSync(eventsFile)) {
        const data = JSON.parse(readFileSync(eventsFile, 'utf-8'));
        this.blockingEvents = data.events || [];
      }

      if (existsSync(patternsFile)) {
        const data = JSON.parse(readFileSync(patternsFile, 'utf-8'));
        data.patterns?.forEach((pattern: BlockPattern) => {
          this.blockPatterns.set(pattern.id, pattern);
        });
      }

      if (existsSync(strategiesFile)) {
        const data = JSON.parse(readFileSync(strategiesFile, 'utf-8'));
        data.strategies?.forEach((strategy: MitigationStrategy) => {
          this.mitigationStrategies.set(strategy.name, strategy);
        });
      }

      console.log(`📊 Loaded historical data: ${this.blockingEvents.length} events, ${this.blockPatterns.size} patterns`);
    } catch (error) {
      console.warn('Failed to load historical blocking data:', error);
    }
  }

  async analyzeForBlocking(page: Page, domain: string): Promise<BlockDetection> {
    const startTime = Date.now();
    const url = page.url();
    
    try {
      console.log(`🔍 Analyzing page for blocking: ${domain}`);
      
      // Get current page info
      const responseCode = await page.evaluate(() => {
        return (performance.getEntriesByType('navigation')[0] as any)?.responseStatus || 0;
      });

      const pageTitle = await page.title().catch(() => '');
      const pageContent = await page.content().catch(() => '');

      // Visual analysis with screenshot
      const screenshotAnalysis = await this.visualEngine.analyzeScreenshot(page, {
        name: `block-analysis-${domain}-${Date.now()}`,
        detectBlocking: true,
        detectCaptcha: true,
        extractText: true
      });

      // Analyze patterns
      const detection = this.analyzePatterns(
        domain,
        url,
        responseCode,
        pageTitle,
        pageContent,
        screenshotAnalysis
      );

      // Update pattern frequency if block detected
      if (detection.detected) {
        this.updatePatternFrequency(detection);
      }

      console.log(`📋 Block analysis completed in ${Date.now() - startTime}ms: ${detection.detected ? 'BLOCKED' : 'CLEAR'}`);
      
      return detection;
    } catch (error) {
      console.error('Block analysis failed:', error);
      return {
        detected: false,
        type: 'unknown',
        confidence: 0,
        indicators: [`Analysis failed: ${error}`],
        timestamp: Date.now(),
        domain,
        url
      };
    }
  }

  private analyzePatterns(
    domain: string,
    url: string,
    responseCode: number,
    pageTitle: string,
    pageContent: string,
    visualAnalysis: ScreenshotAnalysis
  ): BlockDetection {
    const indicators: string[] = [];
    let maxConfidence = 0;
    let detectedType: BlockDetection['type'] = 'unknown';
    let isBlocked = false;

    // Check each pattern
    for (const pattern of this.blockPatterns.values()) {
      if (pattern.domain !== '*' && pattern.domain !== domain) {continue;}

      let patternConfidence = 0;
      let patternMatches = 0;
      const totalChecks = Object.values(pattern.indicators).flat().length;

      // Visual indicators
      pattern.indicators.visual.forEach(indicator => {
        if (visualAnalysis.blockingIndicators.some((bi: string) => bi.includes(indicator)) ||
            visualAnalysis.elements.some((el: any) => el.content.toLowerCase().includes(indicator))) {
          patternMatches++;
          indicators.push(`Visual: ${indicator}`);
        }
      });

      // Textual indicators
      const lowercaseContent = pageContent.toLowerCase();
      const lowercaseTitle = pageTitle.toLowerCase();
      
      pattern.indicators.textual.forEach(indicator => {
        if (lowercaseContent.includes(indicator) || lowercaseTitle.includes(indicator)) {
          patternMatches++;
          indicators.push(`Text: ${indicator}`);
        }
      });

      // Technical indicators
      pattern.indicators.technical.forEach(indicator => {
        if (responseCode.toString().includes(indicator) ||
            lowercaseContent.includes(indicator) ||
            url.toLowerCase().includes(indicator)) {
          patternMatches += 2; // Technical indicators are more reliable
          indicators.push(`Technical: ${indicator}`);
        }
      });

      // Behavioral indicators (from visual analysis)
      pattern.indicators.behavioral.forEach(indicator => {
        if (visualAnalysis.pageType === 'blocked' ||
            visualAnalysis.pageType === 'captcha' ||
            visualAnalysis.pageType === 'error') {
          patternMatches++;
          indicators.push(`Behavioral: ${indicator}`);
        }
      });

      // Calculate pattern confidence
      patternConfidence = Math.min(100, (patternMatches / totalChecks) * 100 * 1.5);

      // Update max confidence and type
      if (patternConfidence > maxConfidence) {
        maxConfidence = patternConfidence;
        detectedType = pattern.type;
        isBlocked = patternConfidence > 30; // 30% threshold for block detection
      }
    }

    // Additional heuristics
    if (responseCode >= 400 && responseCode < 600) {
      maxConfidence = Math.max(maxConfidence, 70);
      isBlocked = true;
      indicators.push(`HTTP Error: ${responseCode}`);
    }

    if (visualAnalysis.pageType === 'captcha') {
      maxConfidence = Math.max(maxConfidence, 85);
      detectedType = 'captcha';
      isBlocked = true;
    }

    if (visualAnalysis.pageType === 'blocked') {
      maxConfidence = Math.max(maxConfidence, 90);
      isBlocked = true;
    }

    return {
      detected: isBlocked,
      type: detectedType,
      confidence: Math.round(maxConfidence),
      indicators,
      screenshot: `block-analysis-${domain}-${Date.now()}.png`,
      timestamp: Date.now(),
      domain,
      url,
      responseCode
    };
  }

  async mitigateBlocking(page: Page, detection: BlockDetection): Promise<{
    success: boolean;
    strategy: string;
    recoveryTime: number;
    steps: Array<{ step: MitigationStep; result: boolean; duration: number }>;
  }> {
    console.log(`🔧 Attempting to mitigate ${detection.type} block on ${detection.domain}`);
    
    const startTime = Date.now();
    
    // Get LLM recommendation for mitigation strategy
    const llmDecision = await this.llmOrchestrator.makeRealTimeDecision({
      domain: detection.domain,
      target: 'block_mitigation',
      previousAttempts: 0,
      successRate: 0,
      avgResponseTime: 0,
      currentStrategy: 'reactive',
      blockingIndicators: detection.indicators
    }, `Blocked by ${detection.type} with confidence ${detection.confidence}%`);

    // Select mitigation strategy
    const strategy = this.selectMitigationStrategy(detection, llmDecision);
    if (!strategy) {
      return {
        success: false,
        strategy: 'none',
        recoveryTime: 0,
        steps: []
      };
    }

    console.log(`🎯 Selected mitigation strategy: ${strategy.name}`);

    // Execute mitigation steps
    const executedSteps: Array<{ step: MitigationStep; result: boolean; duration: number }> = [];
    let success = false;

    for (const step of strategy.steps) {
      const stepStartTime = Date.now();
      let result = false;

      try {
        result = await this.executeMitigationStep(page, step, detection);
        
        if (result && step.type !== 'wait' && step.type !== 'manual_intervention') {
          // Check if block is resolved after this step
          const quickCheck = await this.quickBlockCheck(page, detection.domain);
          if (!quickCheck.detected) {
            success = true;
            break;
          }
        } else if (step.type === 'manual_intervention') {
          result = true; // Assume manual intervention is successful
        }
      } catch (error) {
        console.error(`Mitigation step failed:`, error);
        result = false;
      }

      executedSteps.push({
        step,
        result,
        duration: Date.now() - stepStartTime
      });

      if (!result && step.type !== 'wait') {
        console.warn(`❌ Mitigation step failed: ${step.description}`);
        break;
      }
    }

    const recoveryTime = Date.now() - startTime;
    
    // Update strategy effectiveness
    this.updateStrategyEffectiveness(strategy.name, success, recoveryTime);

    // Create blocking event record
    const event: BlockingEvent = {
      id: `block_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      domain: detection.domain,
      url: detection.url,
      detection,
      mitigation: {
        strategy: strategy.name,
        applied: true,
        success,
        recoveryTime,
        steps: executedSteps
      },
      resolution: success ? 'resolved' : 'escalated',
      learnings: this.extractLearnings(detection, strategy, success, executedSteps)
    };

    this.blockingEvents.push(event);
    this.persistData();

    console.log(`${success ? '✅' : '❌'} Mitigation ${success ? 'successful' : 'failed'} in ${recoveryTime}ms`);

    return {
      success,
      strategy: strategy.name,
      recoveryTime,
      steps: executedSteps
    };
  }

  private async executeMitigationStep(page: Page, step: MitigationStep, detection: BlockDetection): Promise<boolean> {
    try {
      switch (step.type) {
        case 'wait':
          console.log(`⏳ Waiting ${step.duration}ms: ${step.description}`);
          await new Promise(resolve => setTimeout(resolve, step.duration || 5000));
          return true;

        case 'change_profile':
          console.log(`🎭 Changing profile: ${step.description}`);
          const newProfile = await this.evasionSystem.selectOptimalProfile(detection.domain);
          await this.evasionSystem.applyProfileToPage(page, newProfile);
          return true;

        case 'change_proxy':
          console.log(`🌐 Changing proxy: ${step.description}`);
          const newProxy = await this.evasionSystem.selectRotatingProxy();
          return newProxy !== null;

        case 'clear_cookies':
          console.log(`🍪 Clearing cookies: ${step.description}`);
          const client = await page.target().createCDPSession();
          await client.send('Network.clearBrowserCookies');
          await client.send('Storage.clearDataForOrigin', {
            origin: new URL(page.url()).origin,
            storageTypes: 'all'
          });
          return true;

        case 'navigate_away':
          console.log(`🔄 Navigating away: ${step.description}`);
          await page.goto('https://www.google.com', { waitUntil: 'networkidle0', timeout: 15000 });
          await new Promise(resolve => setTimeout(resolve, 2000));
          return true;

        case 'solve_captcha':
          console.log(`🧩 CAPTCHA detected: ${step.description}`);
          // In a real implementation, you would integrate with CAPTCHA solving services
          return false; // For now, return false to trigger manual intervention

        case 'manual_intervention':
          console.log(`👤 Manual intervention required: ${step.description}`);
          // Emit event for manual intervention
          return true; // Assume intervention will be provided

        case 'change_strategy':
          console.log(`📋 Changing strategy: ${step.description}`);
          // This would trigger a strategy change at a higher level
          return true;

        default:
          console.warn(`Unknown mitigation step type: ${step.type}`);
          return false;
      }
    } catch (error) {
      console.error(`Failed to execute mitigation step:`, error);
      return false;
    }
  }

  private async quickBlockCheck(page: Page, domain: string): Promise<BlockDetection> {
    // Quick check without full analysis
    try {
      const title = await page.title();
      const url = page.url();
      
      // Simple heuristics for quick detection
      const lowercaseTitle = title.toLowerCase();
      const blockKeywords = ['blocked', 'access denied', 'cloudflare', 'captcha', 'rate limit'];
      
      const blocked = blockKeywords.some(keyword => 
        lowercaseTitle.includes(keyword) || url.toLowerCase().includes(keyword)
      );

      return {
        detected: blocked,
        type: blocked ? 'unknown' : 'unknown',
        confidence: blocked ? 70 : 10,
        indicators: blocked ? ['Quick check detected blocking keywords'] : [],
        timestamp: Date.now(),
        domain,
        url
      };
    } catch (error) {
      return {
        detected: true, // Assume blocked if we can't check
        type: 'unknown',
        confidence: 50,
        indicators: ['Quick check failed'],
        timestamp: Date.now(),
        domain,
        url: ''
      };
    }
  }

  private selectMitigationStrategy(detection: BlockDetection, llmDecision: LLMDecision): MitigationStrategy | null {
    // Use LLM decision if available
    if (llmDecision.action === 'change_strategy' && llmDecision.parameters?.strategy) {
      const suggestedStrategy = this.mitigationStrategies.get(llmDecision.parameters.strategy as string);
      if (suggestedStrategy) {return suggestedStrategy;}
    }

    // Fallback to rule-based selection
    const strategies = Array.from(this.mitigationStrategies.values());
    
    switch (detection.type) {
      case 'cloudflare':
        return strategies.find(s => s.name.includes('cloudflare')) || strategies[0];
      
      case 'rate_limit':
        return strategies.find(s => s.name.includes('rate_limit')) || strategies[0];
      
      case 'captcha':
        return strategies.find(s => s.name.includes('captcha')) || strategies[0];
      
      case 'hard_block':
        return strategies.find(s => s.name.includes('bot_detection')) || strategies[0];
      
      case 'soft_block':
        return strategies.find(s => s.name.includes('login_wall')) || strategies[0];
      
      default:
        return strategies.sort((a, b) => b.successRate - a.successRate)[0];
    }
  }

  private updatePatternFrequency(detection: BlockDetection): void {
    for (const pattern of this.blockPatterns.values()) {
      if (pattern.type === detection.type && 
          (pattern.domain === '*' || pattern.domain === detection.domain)) {
        pattern.frequency++;
        pattern.lastSeen = Date.now();
        break;
      }
    }
  }

  private updateStrategyEffectiveness(strategyName: string, success: boolean, recoveryTime: number): void {
    const strategy = this.mitigationStrategies.get(strategyName);
    if (!strategy) {return;}

    // Update success rate with exponential moving average
    const alpha = 0.2; // Learning rate
    strategy.successRate = success 
      ? strategy.successRate * (1 - alpha) + 100 * alpha
      : strategy.successRate * (1 - alpha);

    // Update average recovery time
    strategy.avgRecoveryTime = strategy.avgRecoveryTime * (1 - alpha) + recoveryTime * alpha;
    strategy.lastUsed = Date.now();
    
    // Update effectiveness rating
    if (strategy.successRate > 80) {
      strategy.effectiveness = 'high';
    } else if (strategy.successRate > 60) {
      strategy.effectiveness = 'medium';
    } else {
      strategy.effectiveness = 'low';
    }

    console.log(`📈 Updated strategy effectiveness: ${strategyName} - ${strategy.successRate.toFixed(1)}% success rate`);
  }

  private extractLearnings(
    detection: BlockDetection,
    strategy: MitigationStrategy,
    success: boolean,
    steps: Array<{ step: MitigationStep; result: boolean; duration: number }>
  ): string[] {
    const learnings: string[] = [];

    if (success) {
      learnings.push(`Strategy '${strategy.name}' effective for ${detection.type} on ${detection.domain}`);
      
      const criticalSteps = steps.filter(s => s.result && 
        ['change_profile', 'change_proxy', 'clear_cookies'].includes(s.step.type));
      
      if (criticalSteps.length > 0) {
        learnings.push(`Critical steps: ${criticalSteps.map(s => s.step.type).join(', ')}`);
      }
    } else {
      learnings.push(`Strategy '${strategy.name}' failed for ${detection.type} on ${detection.domain}`);
      
      const failedSteps = steps.filter(s => !s.result);
      if (failedSteps.length > 0) {
        learnings.push(`Failed steps: ${failedSteps.map(s => s.step.type).join(', ')}`);
      }
    }

    if (detection.confidence > 90) {
      learnings.push(`High confidence detection (${detection.confidence}%) - pattern well established`);
    }

    return learnings;
  }

  private addBlockPattern(pattern: BlockPattern): void {
    this.blockPatterns.set(pattern.id, pattern);
  }

  private addMitigationStrategy(strategy: MitigationStrategy): void {
    this.mitigationStrategies.set(strategy.name, strategy);
  }

  private persistData(): void {
    try {
      // Save only last 500 events to prevent file bloat
      const recentEvents = this.blockingEvents.slice(-500);
      
      writeFileSync(
        join(this.dataDir, 'blocking_events.json'),
        JSON.stringify({ events: recentEvents }, null, 2)
      );

      writeFileSync(
        join(this.dataDir, 'patterns.json'),
        JSON.stringify({ patterns: Array.from(this.blockPatterns.values()) }, null, 2)
      );

      writeFileSync(
        join(this.dataDir, 'strategies.json'),
        JSON.stringify({ strategies: Array.from(this.mitigationStrategies.values()) }, null, 2)
      );
    } catch (error) {
      console.error('Failed to persist blocking data:', error);
    }
  }

  getBlockingStats(): {
    totalEvents: number;
    successfulMitigations: number;
    topBlockTypes: Array<{ type: string; count: number }>;
    topDomains: Array<{ domain: string; count: number }>;
    strategyEffectiveness: Array<{ strategy: string; successRate: number }>;
  } {
    const recentEvents = this.blockingEvents.slice(-100);
    const successfulMitigations = recentEvents.filter(e => 
      e.mitigation?.success === true
    ).length;

    // Count block types
    const blockTypeCounts: Record<string, number> = {};
    recentEvents.forEach(e => {
      blockTypeCounts[e.detection.type] = (blockTypeCounts[e.detection.type] || 0) + 1;
    });

    // Count domains
    const domainCounts: Record<string, number> = {};
    recentEvents.forEach(e => {
      domainCounts[e.domain] = (domainCounts[e.domain] || 0) + 1;
    });

    // Strategy effectiveness
    const strategyStats = Array.from(this.mitigationStrategies.values())
      .map(s => ({ strategy: s.name, successRate: Math.round(s.successRate) }))
      .sort((a, b) => b.successRate - a.successRate);

    return {
      totalEvents: this.blockingEvents.length,
      successfulMitigations,
      topBlockTypes: Object.entries(blockTypeCounts)
        .map(([type, count]) => ({ type, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5),
      topDomains: Object.entries(domainCounts)
        .map(([domain, count]) => ({ domain, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5),
      strategyEffectiveness: strategyStats.slice(0, 5)
    };
  }

  cleanup(): void {
    console.log('🧹 Cleaning up Block Analyzer...');
    this.persistData();
    console.log('✅ Block Analyzer cleanup complete');
  }
}