#!/usr/bin/env node
/**
 * MCP X/Reddit Scraper - Modularized Version
 * This is a transitional implementation that organizes handlers by category
 * while maintaining compatibility with existing interfaces
 */

import 'dotenv/config';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import { ToolRegistry } from './tool-registry.js';

// Components are now initialized and managed by the ToolRegistry

// Initialize tool registry for modular handler execution
const toolRegistry = new ToolRegistry();

// Default session management for @chat shorthand
let defaultChatSession: string | null = null;

const server = new Server(
  {
    name: 'x-scraper',
    version: '0.1.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// Tool definitions organized by category
// Tools are now managed by the ToolRegistry - no need for manual definitions

server.setRequestHandler(ListToolsRequestSchema, async () => ({
  tools: toolRegistry.getTools(),
}));

// Simplified handler implementation - working versions only for now
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  try {
    const { name, arguments: args } = request.params;

    // Use modular handler system via tool registry
    return await toolRegistry.executeHandler(name, args);
  } catch (error: any) {
    console.error(`Tool execution error for ${request.params.name}:`, error);
    
    if (error.message?.includes('not found') || error.message?.includes('Unknown tool')) {
      throw new McpError(
        ErrorCode.MethodNotFound,
        `Tool '${request.params.name}' not found. Use list_tools to see available tools.`
      );
    }
    
    if (error.message?.includes('validation')) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid parameters for tool '${request.params.name}': ${error.message}`
      );
    }
    
    throw new McpError(
      ErrorCode.InternalError,
      `Failed to execute tool '${request.params.name}': ${error.message}`
    );
  }
});

// Handle process termination gracefully
async function cleanup() {
  console.log('🧹 Starting server cleanup...');

  try {
    // Cleanup is now handled by individual components through the tool registry
    console.log('✅ Server cleanup complete');
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }

  process.exit(0);
}

process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

async function main() {
  const transport = new StdioServerTransport();
  
  console.log('🚀 Starting MCP X/Reddit Scraper Server (Modular Version)...');
  console.log(`📊 Registered ${toolRegistry.getTools().length} tools`);
  console.log('🎯 Server ready for connections');
  
  await server.connect(transport);
}

main().catch((error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});