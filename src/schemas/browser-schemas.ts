/**
 * Browser Automation Tool Schemas  
 * Zod validation schemas for browser automation operations
 */

import { z } from 'zod';

export const NavigateSchema = z.object({
  url: z.string().describe('URL to navigate to')
});

export const ScreenshotSchema = z.object({
  name: z.string().describe('Name for the screenshot'),
  selector: z.string().optional().describe('CSS selector to capture (captures full page if not provided)')
});

export const ClickSchema = z.object({
  selector: z.string().describe('CSS selector of element to click')
});

export const FillSchema = z.object({
  selector: z.string().describe('CSS selector of input element'),
  value: z.string().describe('Value to fill in the input')
});

export const ScrollSchema = z.object({
  direction: z.enum(['up', 'down']).describe('Direction to scroll'),
  amount: z.number().optional().default(500).describe('Pixels to scroll')
});

export const AnalyzePageVisuallySchema = z.object({
  url: z.string().describe('URL to analyze with visual scraping'),
  options: z.object({
    detectBlocking: z.boolean().optional().default(true),
    detectCaptcha: z.boolean().optional().default(true),
    extractText: z.boolean().optional().default(true)
  }).optional()
});