/**
 * Notion Tool Schemas
 * JSON schemas for Notion MCP tools
 */

export const CreateNotionPageSchema = {
  type: "object",
  properties: {
    parent_id: {
      type: "string",
      description: "ID of the parent page or database where the new page will be created"
    },
    title: {
      type: "string",
      description: "Title of the new page"
    },
    content: {
      type: "string",
      description: "Content of the page in markdown format (optional)"
    },
    properties: {
      type: "object",
      description: "Additional properties for the page (optional)",
      additionalProperties: true
    }
  },
  required: ["parent_id", "title"]
};

export const SearchNotionPagesSchema = {
  type: "object",
  properties: {
    query: {
      type: "string",
      description: "Search query to find pages"
    },
    max_results: {
      type: "number",
      description: "Maximum number of results to return (default: 10, max: 100)",
      minimum: 1,
      maximum: 100
    }
  },
  required: ["query"]
};

export const GetNotionPageSchema = {
  type: "object",
  properties: {
    page_id: {
      type: "string",
      description: "ID of the Notion page to retrieve"
    }
  },
  required: ["page_id"]
};

export const ListNotionDatabasesSchema = {
  type: "object",
  properties: {
    max_results: {
      type: "number",
      description: "Maximum number of databases to return (default: 10, max: 100)",
      minimum: 1,
      maximum: 100
    }
  }
};

export const AddNotionDatabaseEntrySchema = {
  type: "object",
  properties: {
    database_id: {
      type: "string",
      description: "ID of the database to add entry to"
    },
    properties: {
      type: "object",
      description: "Properties for the new database entry",
      additionalProperties: true
    }
  },
  required: ["database_id", "properties"]
};

export const UpdateNotionPageSchema = {
  type: "object",
  properties: {
    page_id: {
      type: "string",
      description: "ID of the page to update"
    },
    properties: {
      type: "object",
      description: "Properties to update",
      additionalProperties: true
    }
  },
  required: ["page_id", "properties"]
};

export const AppendNotionPageContentSchema = {
  type: "object",
  properties: {
    page_id: {
      type: "string",
      description: "ID of the page to append content to"
    },
    content: {
      type: "string",
      description: "Content to append in markdown format"
    }
  },
  required: ["page_id", "content"]
};
