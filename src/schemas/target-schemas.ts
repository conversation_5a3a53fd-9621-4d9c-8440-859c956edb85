/**
 * Target Management Tool Schemas
 * Zod validation schemas for target management operations
 */

import { z } from 'zod';

export const ListTargetsSchema = z.object({
  type: z.enum(['twitter', 'reddit', 'both']).optional().default('both').describe('Type of targets to list'),
  category: z.string().optional().describe('Specific category to filter by')
});

export const GetTargetCategoriesSchema = z.object({});

export const AddInfluencerSchema = z.object({
  category: z.string().describe('Category to add influencer to'),
  username: z.string().describe('Twitter username'),
  followers: z.string().optional().describe('Follower count (e.g., "1M+")'),
  relevance_score: z.number().optional().default(0.8).describe('Relevance score (0-1)'),
  topics: z.array(z.string()).optional().describe('Topics they discuss')
});

export const AddSubredditSchema = z.object({
  category: z.string().describe('Category to add subreddit to'),
  name: z.string().describe('Subreddit name (without r/)'),
  subscribers: z.string().optional().describe('Subscriber count (e.g., "500K")'),
  activity_score: z.number().optional().default(0.8).describe('Activity score (0-1)')
});

export const RemoveTargetSchema = z.object({
  type: z.enum(['twitter', 'reddit']).describe('Type of target to remove'),
  category: z.string().describe('Category the target is in'),
  identifier: z.string().describe('Username (Twitter) or subreddit name (Reddit)')
});

export const GetTopTargetsSchema = z.object({
  limit: z.number().optional().default(10).describe('Number of top targets to return')
});

export const BatchAnalyzeTargetsSchema = z.object({
  twitter_categories: z.array(z.string()).optional().default([]).describe('Twitter categories to analyze'),
  reddit_categories: z.array(z.string()).optional().default([]).describe('Reddit categories to analyze'),
  analysis_type: z.enum(['sentiment', 'trending', 'full']).optional().default('full').describe('Type of analysis to perform')
});

export const DiscoverNewTargetsSchema = z.object({
  category: z.string().describe('Category to discover targets for'),
  keywords: z.array(z.string()).optional().describe('Keywords to search for (uses defaults if not provided)'),
  limit: z.number().optional().default(5).describe('Maximum number of new targets to discover')
});

export const GetRecentDiscoveriesSchema = z.object({});

export const PromoteDiscoveredTargetSchema = z.object({
  type: z.enum(['twitter', 'reddit']).describe('Type of target to promote'),
  category: z.string().describe('Category the discovered target is in'),
  identifier: z.string().describe('Username (Twitter) or subreddit name (Reddit) to promote')
});

export const UpdateTargetRelevanceSchema = z.object({
  type: z.enum(['twitter', 'reddit']).describe('Type of target to update'),
  category: z.string().describe('Category the target is in'),
  identifier: z.string().describe('Username (Twitter) or subreddit name (Reddit) to update'),
  check_current_metrics: z.boolean().optional().default(true).describe('Whether to fetch current metrics for scoring')
});