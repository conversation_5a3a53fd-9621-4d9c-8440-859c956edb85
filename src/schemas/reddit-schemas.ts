/**
 * Reddit Scraping Tool Schemas
 * Zod validation schemas for Reddit scraping operations
 */

import { z } from 'zod';

export const GetSubredditPostsSchema = z.object({
  subreddit: z.string().describe('Name of the subreddit (without r/)'),
  sort: z.enum(['hot', 'new', 'top', 'rising']).optional().default('hot').describe('Sort order for posts'),
  limit: z.number().optional().default(25).describe('Number of posts to retrieve')
});

export const GetRedditCommentsSchema = z.object({
  postUrl: z.string().describe('Full URL of the Reddit post'),
  limit: z.number().optional().default(50).describe('Number of comments to retrieve'),
  sortBy: z.enum(['best', 'top', 'new', 'controversial']).optional().default('best')
});

export const GetRedditTrendingSchema = z.object({
  category: z.enum(['all', 'gaming', 'sports', 'news', 'entertainment', 'technology']).optional().default('all'),
  limit: z.number().optional().default(10).describe('Number of trending subreddits to return')
});

export const SearchRedditSchema = z.object({
  query: z.string().describe('Search query'),
  subreddit: z.string().optional().describe('Specific subreddit to search in'),
  sort: z.enum(['relevance', 'hot', 'top', 'new', 'comments']).optional().default('relevance'),
  timeframe: z.enum(['all', 'year', 'month', 'week', 'day', 'hour']).optional().default('all'),
  limit: z.number().optional().default(25)
});

export const AnalyzeRedditTrendsSchema = z.object({
  subreddit: z.string().optional().describe('Analyze trends for a specific subreddit'),
  timeframe: z.enum(['day', 'week', 'month']).optional().default('day')
});

export const AnalyzeRedditCommentsSchema = z.object({
  postUrl: z.string().describe('Reddit post URL to analyze comments from')
});