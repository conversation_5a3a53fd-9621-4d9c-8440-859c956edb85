/**
 * AI Agent and Fabric Tool Schemas
 * Zod validation schemas for AI agent and Fabric framework operations
 */

import { z } from 'zod';

// AI Agent schemas
export const CreateChatSessionSchema = z.object({
  // No parameters needed
});

export const ChatWithAgentSchema = z.object({
  sessionId: z.string().optional().describe('Chat session ID (will create new if not provided)'),
  message: z.string().describe('Message to send to the AI agent')
});

export const ChatShorthandSchema = z.object({
  message: z.string().describe('Your message to the AI agent - simple @chat interface')
});

export const GetSessionInfoSchema = z.object({
  sessionId: z.string().describe('Chat session ID')
});

export const ExportReportSchema = z.object({
  sessionId: z.string().describe('Chat session ID'),
  format: z.enum(['json', 'markdown', 'html']).optional().default('json')
});

// Fabric schemas
export const ListFabricPatternsSchema = z.object({
  category: z.string().optional().describe('Filter patterns by category')
});

export const ApplyFabricPatternSchema = z.object({
  patternName: z.string().describe('Name of the Fabric pattern to apply'),
  content: z.string().describe('Content to process with the pattern'),
  model: z.string().optional().describe('AI model to use (e.g., gpt-4, claude-3)'),
  temperature: z.number().optional().describe('Temperature for AI generation (0-2)')
});

export const ChainFabricPatternsSchema = z.object({
  patterns: z.array(z.string()).describe('Array of pattern names to chain'),
  content: z.string().describe('Initial content to process'),
  model: z.string().optional().describe('AI model to use')
});

export const CreateCustomPatternSchema = z.object({
  name: z.string().describe('Name for the custom pattern'),
  systemPrompt: z.string().describe('System prompt for the pattern'),
  description: z.string().describe('Description of what the pattern does')
});

export const AnalyzeWithFabricSchema = z.object({
  content: z.string().describe('Content to analyze'),
  analysisType: z.enum(['insights', 'summary', 'claims']).optional().default('insights')
});

export const BatchApplyPatternSchema = z.object({
  patternName: z.string().describe('Name of the pattern to apply'),
  contentArray: z.array(z.string()).describe('Array of content items to process'),
  model: z.string().optional().describe('AI model to use')
});