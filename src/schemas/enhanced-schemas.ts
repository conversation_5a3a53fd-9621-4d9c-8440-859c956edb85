/**
 * Enhanced Scraping Engine Tool Schemas
 * Zod validation schemas for enhanced scraping operations
 */

import { z } from 'zod';

export const GetSystemHealthSchema = z.object({
  includeMetrics: z.boolean().optional().default(true).describe('Include detailed metrics in response')
});

export const GetKPISnapshotSchema = z.object({
  domain: z.string().optional().describe('Get KPIs for specific domain'),
  timeRange: z.enum(['hour', 'day', 'week', 'month']).optional().default('day').describe('Time range for metrics')
});

export const GetBlockingStatsSchema = z.object({
  domain: z.string().optional().describe('Get blocking stats for specific domain')
});

export const GetEnhancementStatsSchema = z.object({});

export const GenerateEnhancementProposalSchema = z.object({});

export const GetResearchSummarySchema = z.object({
  days: z.number().optional().default(30).describe('Number of days to analyze')
});

export const SendNotificationSchema = z.object({
  type: z.enum(['health_alert', 'performance_degradation', 'custom']).describe('Type of notification'),
  severity: z.enum(['info', 'warning', 'error', 'critical']).describe('Severity level'),
  title: z.string().describe('Notification title'),
  message: z.string().describe('Notification message'),
  domain: z.string().optional().describe('Related domain if applicable')
});

export const GetDashboardDataSchema = z.object({});

export const TestNotificationChannelSchema = z.object({
  channelName: z.string().describe('Name of the notification channel to test')
});