/**
 * Twitter/X Scraping Tool Schemas
 * Zod validation schemas for Twitter scraping operations
 */

import { z } from 'zod';

export const GetCommentsSchema = z.object({
  username: z.string().describe('X/Twitter username to scrape comments from'),
  limit: z.number().optional().default(20).describe('Maximum number of comments to retrieve'),
  includeReplies: z.boolean().optional().default(true).describe('Include reply threads')
});

export const GetKeyContributorsSchema = z.object({
  topic: z.string().describe('Topic or hashtag to find key contributors for'),
  limit: z.number().optional().default(10).describe('Number of top contributors to return')
});

export const GetTrendingTopicsSchema = z.object({
  category: z.string().optional().describe('Category to filter trends (e.g., "tech", "politics", "sports")'),
  location: z.string().optional().default('worldwide').describe('Location for trends')
});

export const AnalyzeSentimentSchema = z.object({
  posts: z.array(z.string()).describe('Array of post texts to analyze sentiment'),
  granularity: z.enum(['post', 'aggregate']).optional().default('aggregate')
});

export const GetTrendingKeywordsSchema = z.object({
  sources: z.array(z.enum(['twitter', 'reddit', 'both'])).optional().default(['both']),
  limit: z.number().optional().default(10)
});

export const AnalyzeKeywordsSchema = z.object({
  keywords: z.array(z.string()).describe('Keywords to analyze'),
  sources: z.array(z.enum(['twitter', 'reddit', 'both'])).optional().default(['both'])
});