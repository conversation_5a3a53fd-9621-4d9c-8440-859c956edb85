/**
 * Configuration Validator
 * Validates system configuration and dependencies for MCP X/Reddit Scraper
 */

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';

export interface ValidationResult {
  component: string;
  status: 'success' | 'warning' | 'error';
  message: string;
  suggestion?: string;
}

export class ConfigValidator {
  private results: ValidationResult[] = [];

  async validateAll(): Promise<ValidationResult[]> {
    this.results = [];
    
    console.log('🔍 Starting system configuration validation...\n');
    
    await this.validateNodeEnvironment();
    await this.validatePythonEnvironment();
    await this.validateFabricInstallation();
    await this.validateEnvironmentVariables();
    await this.validateFileStructure();
    
    this.printSummary();
    return this.results;
  }

  private async validateNodeEnvironment(): Promise<void> {
    try {
      const nodeVersion = process.version;
      const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
      
      if (majorVersion >= 18) {
        this.addResult('Node.js', 'success', `Version ${nodeVersion} ✅`);
      } else {
        this.addResult('Node.js', 'error', `Version ${nodeVersion} is too old`, 'Upgrade to Node.js 18+');
      }
    } catch (error) {
      this.addResult('Node.js', 'error', 'Failed to check Node.js version');
    }
  }

  private async validatePythonEnvironment(): Promise<void> {
    const venvPath = path.join(process.cwd(), 'venv');
    const venvPython = path.join(venvPath, 'bin', 'python');
    
    if (fs.existsSync(venvPath)) {
      this.addResult('Python venv', 'success', 'Virtual environment exists ✅');
      
      if (fs.existsSync(venvPython)) {
        this.addResult('Python executable', 'success', 'Virtual environment Python found ✅');
      } else {
        this.addResult('Python executable', 'warning', 'Python not found in venv', 'Recreate virtual environment');
      }
      
      // Check requirements.txt dependencies
      await this.validatePythonDependencies();
    } else {
      this.addResult('Python venv', 'error', 'Virtual environment not found', 'Run: python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt');
    }
  }

  private async validatePythonDependencies(): Promise<void> {
    const venvPython = path.join(process.cwd(), 'venv', 'bin', 'python');
    
    return new Promise((resolve) => {
      const process = spawn(venvPython, ['-c', 'import openai, pydantic, langchain; print("Dependencies OK")'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let error = '';

      process.stdout?.on('data', (data) => {
        output += data.toString();
      });

      process.stderr?.on('data', (data) => {
        error += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0 && output.includes('Dependencies OK')) {
          this.addResult('Python dependencies', 'success', 'All required packages installed ✅');
        } else {
          this.addResult('Python dependencies', 'error', 'Missing Python dependencies', 'Run: pip install -r requirements.txt');
        }
        resolve();
      });

      process.on('error', () => {
        this.addResult('Python dependencies', 'error', 'Failed to check Python dependencies');
        resolve();
      });
    });
  }

  private async validateFabricInstallation(): Promise<void> {
    const fabricPaths = [
      path.join(process.env.HOME || '', 'go', 'bin', 'fabric'),
      '/usr/local/bin/fabric',
      '/opt/homebrew/bin/fabric'
    ];

    let fabricFound = false;
    for (const fabricPath of fabricPaths) {
      if (fs.existsSync(fabricPath)) {
        this.addResult('Fabric binary', 'success', `Found at ${fabricPath} ✅`);
        fabricFound = true;
        await this.validateFabricConfiguration(fabricPath);
        break;
      }
    }

    if (!fabricFound) {
      this.addResult('Fabric binary', 'error', 'Fabric not found', 'Install with: go install github.com/danielmiessler/fabric@latest');
    }
  }

  private async validateFabricConfiguration(fabricPath: string): Promise<void> {
    return new Promise((resolve) => {
      const process = spawn(fabricPath, ['--listmodels'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let error = '';

      process.stdout?.on('data', (data) => {
        output += data.toString();
      });

      process.stderr?.on('data', (data) => {
        error += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0 && output.length > 0) {
          this.addResult('Fabric configuration', 'success', 'Fabric is configured with models ✅');
        } else {
          this.addResult('Fabric configuration', 'warning', 'Fabric not configured', 'Run: fabric --setup');
        }
        resolve();
      });

      process.on('error', () => {
        this.addResult('Fabric configuration', 'error', 'Failed to check Fabric configuration');
        resolve();
      });
    });
  }

  private validateEnvironmentVariables(): void {
    const requiredVars = ['OPENAI_API_KEY'];
    const optionalVars = ['DEFAULT_MODEL', 'DEFAULT_VENDOR'];

    for (const varName of requiredVars) {
      if (process.env[varName]) {
        this.addResult(`Environment: ${varName}`, 'success', 'Set ✅');
      } else {
        this.addResult(`Environment: ${varName}`, 'warning', 'Not set', 'Add to .env file or environment');
      }
    }

    for (const varName of optionalVars) {
      if (process.env[varName]) {
        this.addResult(`Environment: ${varName}`, 'success', 'Set ✅');
      } else {
        this.addResult(`Environment: ${varName}`, 'warning', 'Not set (optional)');
      }
    }
  }

  private validateFileStructure(): void {
    const requiredFiles = [
      'src/index.ts',
      'src/scraper.ts',
      'src/reddit-scraper.ts',
      'src/agent-bridge.ts',
      'src/fabric-bridge.ts',
      'package.json',
      'requirements.txt'
    ];

    const requiredDirs = [
      'src/schemas',
      'src/handlers',
      'build'
    ];

    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        this.addResult(`File: ${file}`, 'success', 'Exists ✅');
      } else {
        this.addResult(`File: ${file}`, 'error', 'Missing');
      }
    }

    for (const dir of requiredDirs) {
      if (fs.existsSync(dir)) {
        this.addResult(`Directory: ${dir}`, 'success', 'Exists ✅');
      } else {
        this.addResult(`Directory: ${dir}`, 'error', 'Missing');
      }
    }
  }

  private addResult(component: string, status: 'success' | 'warning' | 'error', message: string, suggestion?: string): void {
    this.results.push({ component, status, message, suggestion });
  }

  private printSummary(): void {
    console.log('\n📊 Configuration Validation Summary:');
    console.log('=====================================\n');

    const successCount = this.results.filter(r => r.status === 'success').length;
    const warningCount = this.results.filter(r => r.status === 'warning').length;
    const errorCount = this.results.filter(r => r.status === 'error').length;

    for (const result of this.results) {
      const icon = result.status === 'success' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
      console.log(`${icon} ${result.component}: ${result.message}`);
      if (result.suggestion) {
        console.log(`   💡 ${result.suggestion}`);
      }
    }

    console.log(`\n📈 Summary: ${successCount} success, ${warningCount} warnings, ${errorCount} errors`);
    
    if (errorCount === 0 && warningCount <= 2) {
      console.log('🎉 System is ready for use!');
    } else if (errorCount === 0) {
      console.log('⚠️  System is mostly ready - address warnings for optimal performance');
    } else {
      console.log('❌ System has critical issues - please fix errors before use');
    }
  }
}

export async function validateConfiguration(): Promise<ValidationResult[]> {
  const validator = new ConfigValidator();
  return await validator.validateAll();
}
