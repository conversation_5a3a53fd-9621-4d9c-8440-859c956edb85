/**
 * LLM-Powered Scraping Orchestrator
 * 
 * Intelligent decision-making engine that uses LLMs to:
 * - Analyze target sites and select optimal scraping strategies
 * - Make real-time decisions for anti-bot bypass techniques
 * - Optimize retry strategies and configuration parameters
 * - Adapt to changing site behaviors automatically
 */

import axios from 'axios';
import { ScrapingConfig, SAFE_SCRAPING_CONFIGS } from './safe-scraper-config.js';
import { RequestMonitor } from './request-monitor.js';
import { LLMCacheManager } from './llm-cache-manager.js';
import { writeFileSync, existsSync, readFileSync } from 'fs';
import { join } from 'path';

interface LLMProvider {
  name: string;
  baseUrl: string;
  apiKey: string;
  models: {
    fast: string;    // For quick decisions
    smart: string;   // For complex analysis
  };
  costPerToken: {
    input: number;
    output: number;
  };
}

interface ScrapingContext {
  domain: string;
  target: string;
  previousAttempts: number;
  lastError?: string;
  successRate: number;
  avgResponseTime: number;
  currentStrategy: string;
  blockingIndicators: string[];
  pageStructure?: string;
}

interface StrategyRecommendation {
  strategy: 'aggressive' | 'moderate' | 'strict';
  confidence: number;
  reasoning: string;
  specificTechniques: string[];
  expectedSuccessRate: number;
  riskLevel: 'low' | 'medium' | 'high';
  adjustments: {
    delays?: { min: number; max: number };
    retries?: number;
    userAgentRotation?: boolean;
    proxyRotation?: boolean;
    humanBehavior?: boolean;
  };
}

export interface LLMDecision {
  action: 'continue' | 'pause' | 'change_strategy' | 'investigate' | 'escalate';
  confidence: number;
  reasoning: string;
  parameters?: Record<string, unknown>;
  nextReviewTime?: number;
}

export class LLMOrchestrator {
  private providers: Map<string, LLMProvider> = new Map();
  private primaryProvider: string = 'openrouter';
  private fallbackProvider: string = 'groq';
  private contextCache: Map<string, ScrapingContext> = new Map();
  private decisionHistory: Array<{ timestamp: number; context: ScrapingContext; decision: LLMDecision }> = [];
  private knowledgeBase: string;
  private cacheManager: LLMCacheManager;
  
  constructor() {
    this.initializeProviders();
    this.knowledgeBase = this.loadKnowledgeBase();
    this.cacheManager = new LLMCacheManager({
      maxEntries: 2000, // Increased for high usage
      enableSemanticMatching: true,
      semanticThreshold: 0.85,
      persistToDisk: true,
      cacheDirectory: './data/llm-cache',
      costThreshold: 0.0005, // Cache responses costing more than $0.0005
      analyticsPersistence: true
    });
    
    console.log('🧠 LLM Orchestrator initialized with intelligent caching');
  }

  private initializeProviders(): void {
    // OpenRouter - Unified API with access to many models
    if (process.env.OPENROUTER_API_KEY) {
      this.providers.set('openrouter', {
        name: 'OpenRouter',
        baseUrl: process.env.OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY,
        models: {
          fast: process.env.OPENROUTER_FAST_MODEL || 'anthropic/claude-3-haiku-20240307',
          smart: process.env.OPENROUTER_MODEL || 'anthropic/claude-3-opus-20240229'
        },
        costPerToken: {
          input: 0.000015,  // $0.015 per 1K tokens (Claude 3 Opus)
          output: 0.000075  // $0.075 per 1K tokens
        }
      });
      this.primaryProvider = 'openrouter';
    }

    // OpenAI - Fallback provider
    if (process.env.OPENAI_API_KEY) {
      this.providers.set('openai', {
        name: 'OpenAI',
        baseUrl: 'https://api.openai.com/v1',
        apiKey: process.env.OPENAI_API_KEY,
        models: {
          fast: 'gpt-3.5-turbo',
          smart: process.env.OPENAI_MODEL || 'gpt-4'
        },
        costPerToken: {
          input: 0.00003,   // $0.03 per 1K tokens (GPT-4)
          output: 0.00006   // $0.06 per 1K tokens
        }
      });
      if (!this.primaryProvider) {
        this.primaryProvider = 'openai';
      } else {
        this.fallbackProvider = 'openai';
      }
    }

    // Groq - Ultra-fast inference for quick decisions
    this.providers.set('groq', {
      name: 'Groq',
      baseUrl: 'https://api.groq.com/openai/v1',
      apiKey: process.env.GROQ_API_KEY || '',
      models: {
        fast: 'llama-3.2-3b-preview',
        smart: 'llama-3.1-70b-versatile'
      },
      costPerToken: {
        input: 0.0000002,  // $0.0002 per 1K tokens
        output: 0.0000002  // $0.0002 per 1K tokens
      }
    });

    console.log('🧠 LLM Orchestrator initialized with providers:', Array.from(this.providers.keys()));
  }

  private loadKnowledgeBase(): string {
    const knowledgeFile = join(process.cwd(), 'knowledge', 'scraping-knowledge.md');
    
    if (existsSync(knowledgeFile)) {
      return readFileSync(knowledgeFile, 'utf-8');
    }

    // Default knowledge base with current best practices
    const defaultKnowledge = `
# Web Scraping Intelligence Knowledge Base

## Anti-Detection Techniques by Site Type

### Social Media Sites (Twitter/X, Reddit)
- High sensitivity to automation detection
- Require residential proxies and realistic timing
- User-agent rotation critical
- JavaScript challenges common
- Rate limiting strictly enforced

### E-commerce Sites
- Strong bot protection (Cloudflare, Akamai)
- Browser fingerprinting prevalent
- Session management important
- TLS fingerprint analysis

### News/Content Sites  
- Generally more permissive
- Standard rate limiting sufficient
- Focus on respectful scraping

## Blocking Indicators
- 403 Forbidden responses
- CAPTCHA challenges
- Unusual redirects
- Connection timeouts
- Rate limit error messages
- JavaScript bot detection scripts

## Successful Patterns
- Residential proxy rotation
- Human-like mouse movements
- Realistic scroll patterns
- Random timing variations
- HTTP/2 protocol compliance
- Consistent header sets
    `;

    return defaultKnowledge;
  }

  async analyzeScrapingContext(
    domain: string, 
    target: string, 
    monitor: RequestMonitor
  ): Promise<ScrapingContext> {
    const stats = monitor.getStats();
    const key = `${domain}:${target}`;
    
    const context: ScrapingContext = {
      domain,
      target,
      previousAttempts: stats.totalRequests,
      lastError: stats.recentErrors[0] || undefined,
      successRate: stats.successRate,
      avgResponseTime: stats.averageResponseTime,
      currentStrategy: 'moderate', // Default
      blockingIndicators: stats.recentErrors.filter(error => 
        ['403', '429', 'captcha', 'blocked', 'timeout'].some(indicator => 
          error.toLowerCase().includes(indicator)
        )
      )
    };

    this.contextCache.set(key, context);
    return context;
  }

  async getStrategyRecommendation(context: ScrapingContext): Promise<StrategyRecommendation> {
    const prompt = this.buildStrategyPrompt(context);
    
    try {
      const response = await this.callLLM('smart', prompt, { promptType: 'strategy' });
      const recommendation = this.parseStrategyResponse(response);
      
      console.log(`🎯 Strategy recommendation for ${context.domain}:`, {
        strategy: recommendation.strategy,
        confidence: recommendation.confidence,
        riskLevel: recommendation.riskLevel
      });

      return recommendation;
    } catch (error) {
      console.warn('Failed to get LLM strategy recommendation, using fallback:', error);
      return this.getFallbackStrategy(context);
    }
  }

  async makeRealTimeDecision(
    context: ScrapingContext, 
    currentError?: string
  ): Promise<LLMDecision> {
    const prompt = this.buildDecisionPrompt(context, currentError);
    
    try {
      const response = await this.callLLM('fast', prompt, { maxTokens: 200, promptType: 'decision' });
      const decision = this.parseDecisionResponse(response);
      
      // Store decision in history
      this.decisionHistory.push({
        timestamp: Date.now(),
        context: { ...context },
        decision
      });

      // Keep only last 100 decisions
      if (this.decisionHistory.length > 100) {
        this.decisionHistory = this.decisionHistory.slice(-100);
      }

      console.log(`⚡ Real-time decision for ${context.domain}: ${decision.action} (${decision.confidence}% confidence)`);
      
      return decision;
    } catch (error) {
      console.warn('Failed to get LLM real-time decision, using fallback:', error);
      return {
        action: 'pause',
        confidence: 50,
        reasoning: 'LLM unavailable, using conservative fallback',
        nextReviewTime: Date.now() + 300000 // 5 minutes
      };
    }
  }

  private buildStrategyPrompt(context: ScrapingContext): string {
    return `You are an expert web scraping strategist. Analyze this scraping context and recommend the optimal strategy.

CONTEXT:
- Domain: ${context.domain}
- Target: ${context.target}
- Previous Attempts: ${context.previousAttempts}
- Success Rate: ${context.successRate}%
- Average Response Time: ${context.avgResponseTime}ms
- Recent Errors: ${context.blockingIndicators.join(', ')}
- Current Strategy: ${context.currentStrategy}

KNOWLEDGE BASE:
${this.knowledgeBase.slice(0, 2000)} // Truncate for token limits

REQUIREMENTS:
Respond with a JSON object containing:
{
  "strategy": "aggressive|moderate|strict",
  "confidence": 0-100,
  "reasoning": "detailed explanation",
  "specificTechniques": ["technique1", "technique2"],
  "expectedSuccessRate": 0-100,
  "riskLevel": "low|medium|high",
  "adjustments": {
    "delays": {"min": milliseconds, "max": milliseconds},
    "retries": number,
    "userAgentRotation": boolean,
    "proxyRotation": boolean,
    "humanBehavior": boolean
  }
}

Consider:
1. Domain-specific anti-bot measures
2. Current success rate trends
3. Error patterns and blocking indicators
4. Risk vs. efficiency trade-offs
5. Best practices from knowledge base`;
  }

  private buildDecisionPrompt(context: ScrapingContext, currentError?: string): string {
    const recentDecisions = this.decisionHistory
      .slice(-5)
      .map(d => `${new Date(d.timestamp).toISOString()}: ${d.decision.action} (${d.decision.reasoning})`)
      .join('\n');

    return `You are a real-time web scraping decision engine. Make an immediate decision based on the current situation.

CURRENT SITUATION:
- Domain: ${context.domain}
- Success Rate: ${context.successRate}%
- Recent Error: ${currentError || 'None'}
- Blocking Indicators: ${context.blockingIndicators.join(', ')}

RECENT DECISIONS:
${recentDecisions}

Respond with JSON:
{
  "action": "continue|pause|change_strategy|investigate|escalate",
  "confidence": 0-100,
  "reasoning": "brief explanation",
  "parameters": {},
  "nextReviewTime": timestamp_ms_optional
}

Actions:
- continue: Keep current approach
- pause: Temporary halt (specify duration)
- change_strategy: Switch strategy (specify new one)  
- investigate: Need more data/analysis
- escalate: Human intervention needed`;
  }

  private async callLLM(
    type: 'fast' | 'smart', 
    prompt: string, 
    options: { maxTokens?: number; promptType?: string } = {}
  ): Promise<string> {
    const provider = this.providers.get(this.primaryProvider);
    if (!provider?.apiKey) {
      throw new Error(`Provider ${this.primaryProvider} not configured`);
    }

    const model = provider.models[type];
    const maxTokens = options.maxTokens || (type === 'fast' ? 300 : 1000);
    const promptType = (options.promptType || 'generic') as 'strategy' | 'decision' | 'analysis' | 'research' | 'enhancement' | 'chat' | 'generic';

    // Try cache first
    const cachedResponse = await this.cacheManager.getCachedResponse(prompt, model, promptType);
    if (cachedResponse) {
      return cachedResponse;
    }

    try {
      const headers: Record<string, string> = {
        'Authorization': `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json'
      };

      // Add OpenRouter specific headers
      if (this.primaryProvider === 'openrouter') {
        headers['HTTP-Referer'] = 'https://github.com/davet/mcp-x-scraper';
        headers['X-Title'] = 'MCP X Scraper';
      }

      const response = await axios.post(
        `${provider.baseUrl}/chat/completions`,
        {
          model,
          messages: [
            {
              role: 'system',
              content: 'You are an expert web scraping engineer. Always respond with valid JSON when requested.'
            },
            {
              role: 'user', 
              content: prompt
            }
          ],
          max_tokens: maxTokens,
          temperature: 0.1, // Low temperature for consistent decisions
          ...(this.primaryProvider === 'openrouter' && { transforms: ['middle-out'] }) // OpenRouter optimization
        },
        {
          headers: {
            'Authorization': `Bearer ${provider.apiKey}`,
            'Content-Type': 'application/json',
            ...(this.primaryProvider === 'openrouter' && {
              'HTTP-Referer': 'https://mcp-x-scraper.local',
              'X-Title': 'MCP X Scraper'
            })
          },
          timeout: 30000
        }
      );

      const content = response.data.choices[0].message.content;
      const usage = response.data.usage;
      
      // Calculate cost (estimated based on token usage)
      const inputCost = (usage?.prompt_tokens || 0) * provider.costPerToken.input;
      const outputCost = (usage?.completion_tokens || 0) * provider.costPerToken.output;
      const totalCost = inputCost + outputCost;
      
      // Cache the response
      await this.cacheManager.cacheResponse(
        prompt,
        content,
        model,
        {
          promptTokens: usage?.prompt_tokens || 0,
          completionTokens: usage?.completion_tokens || 0,
          totalTokens: usage?.total_tokens || 0
        },
        totalCost,
        promptType
      );
      
      return content;
    } catch (error) {
      // Try fallback provider
      if (this.primaryProvider !== this.fallbackProvider) {
        console.warn(`Primary LLM provider failed, trying fallback: ${error}`);
        const fallbackProvider = this.providers.get(this.fallbackProvider);
        if (fallbackProvider?.apiKey) {
          const response = await axios.post(
            `${fallbackProvider.baseUrl}/chat/completions`,
            {
              model: fallbackProvider.models[type],
              messages: [
                { role: 'system', content: 'You are an expert web scraping engineer. Always respond with valid JSON when requested.' },
                { role: 'user', content: prompt }
              ],
              max_tokens: maxTokens,
              temperature: 0.1
            },
            {
              headers: {
                'Authorization': `Bearer ${fallbackProvider.apiKey}`,
                'Content-Type': 'application/json'
              },
              timeout: 30000
            }
          );
          
          const fallbackContent = response.data.choices[0].message.content;
          const fallbackUsage = response.data.usage;
          
          // Calculate fallback cost
          const fallbackInputCost = (fallbackUsage?.prompt_tokens || 0) * fallbackProvider.costPerToken.input;
          const fallbackOutputCost = (fallbackUsage?.completion_tokens || 0) * fallbackProvider.costPerToken.output;
          const fallbackTotalCost = fallbackInputCost + fallbackOutputCost;
          
          // Cache fallback response
          await this.cacheManager.cacheResponse(
            prompt,
            fallbackContent,
            fallbackProvider.models[type],
            {
              promptTokens: fallbackUsage?.prompt_tokens || 0,
              completionTokens: fallbackUsage?.completion_tokens || 0,
              totalTokens: fallbackUsage?.total_tokens || 0
            },
            fallbackTotalCost,
            promptType
          );
          
          return fallbackContent;
        }
      }
      throw error;
    }
  }

  private parseStrategyResponse(response: string): StrategyRecommendation {
    try {
      // Try to extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          strategy: parsed.strategy || 'moderate',
          confidence: Math.max(0, Math.min(100, parsed.confidence || 75)),
          reasoning: parsed.reasoning || 'LLM recommendation',
          specificTechniques: Array.isArray(parsed.specificTechniques) ? parsed.specificTechniques : [],
          expectedSuccessRate: Math.max(0, Math.min(100, parsed.expectedSuccessRate || 80)),
          riskLevel: ['low', 'medium', 'high'].includes(parsed.riskLevel) ? parsed.riskLevel : 'medium',
          adjustments: parsed.adjustments || {}
        };
      }
    } catch (error) {
      console.warn('Failed to parse LLM strategy response:', error);
    }

    // Fallback parsing
    return this.getFallbackStrategy();
  }

  private parseDecisionResponse(response: string): LLMDecision {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          action: ['continue', 'pause', 'change_strategy', 'investigate', 'escalate'].includes(parsed.action) 
            ? parsed.action : 'pause',
          confidence: Math.max(0, Math.min(100, parsed.confidence || 50)),
          reasoning: parsed.reasoning || 'LLM decision',
          parameters: parsed.parameters || {},
          nextReviewTime: parsed.nextReviewTime || undefined
        };
      }
    } catch (error) {
      console.warn('Failed to parse LLM decision response:', error);
    }

    return {
      action: 'pause',
      confidence: 50,
      reasoning: 'Failed to parse LLM response',
      nextReviewTime: Date.now() + 300000
    };
  }

  private getFallbackStrategy(context?: ScrapingContext): StrategyRecommendation {
    // Rule-based fallback strategy
    const successRate = context?.successRate || 50;
    const hasBlockingIndicators = context?.blockingIndicators.length || 0;

    let strategy: 'aggressive' | 'moderate' | 'strict' = 'moderate';
    let riskLevel: 'low' | 'medium' | 'high' = 'medium';

    if (successRate < 30 || hasBlockingIndicators > 2) {
      strategy = 'strict';
      riskLevel = 'high';
    } else if (successRate > 80 && hasBlockingIndicators === 0) {
      strategy = 'aggressive';
      riskLevel = 'low';
    }

    return {
      strategy,
      confidence: 60,
      reasoning: 'Rule-based fallback strategy based on success rate and error patterns',
      specificTechniques: ['proxy_rotation', 'user_agent_rotation', 'delay_randomization'],
      expectedSuccessRate: Math.max(50, successRate + 10),
      riskLevel,
      adjustments: {
        delays: strategy === 'strict' ? { min: 10000, max: 30000 } : { min: 2000, max: 8000 },
        retries: strategy === 'strict' ? 2 : 3,
        userAgentRotation: true,
        proxyRotation: hasBlockingIndicators > 0,
        humanBehavior: strategy === 'strict'
      }
    };
  }

  getDecisionHistory(): Array<{ timestamp: number; context: ScrapingContext; decision: LLMDecision }> {
    return [...this.decisionHistory];
  }

  clearDecisionHistory(): void {
    this.decisionHistory = [];
    console.log('🧹 LLM decision history cleared');
  }

  async saveKnowledgeBase(knowledge: string): Promise<void> {
    const knowledgeDir = join(process.cwd(), 'knowledge');
    const knowledgeFile = join(knowledgeDir, 'scraping-knowledge.md');
    
    try {
      // Ensure directory exists
      if (!existsSync(knowledgeDir)) {
        await import('fs').then(fs => fs.promises.mkdir(knowledgeDir, { recursive: true }));
      }
      
      writeFileSync(knowledgeFile, knowledge, 'utf-8');
      this.knowledgeBase = knowledge;
      console.log('💾 Scraping knowledge base updated');
    } catch (error) {
      console.error('Failed to save knowledge base:', error);
    }
  }

  getStats(): {
    providers: string[];
    decisions: number;
    contexts: number;
    avgConfidence: number;
  } {
    const avgConfidence = this.decisionHistory.length > 0
      ? this.decisionHistory.reduce((sum, d) => sum + d.decision.confidence, 0) / this.decisionHistory.length
      : 0;

    return {
      providers: Array.from(this.providers.keys()),
      decisions: this.decisionHistory.length,
      contexts: this.contextCache.size,
      avgConfidence: Math.round(avgConfidence)
    };
  }
  
  // Cache management methods
  getCacheStats(): ReturnType<LLMCacheManager['getStats']> {
    return this.cacheManager.getStats();
  }
  
  getCacheInfo(): ReturnType<LLMCacheManager['getCacheInfo']> {
    return this.cacheManager.getCacheInfo();
  }
  
  clearCache(promptType?: 'strategy' | 'decision' | 'analysis' | 'research' | 'enhancement' | 'chat' | 'generic'): number {
    return this.cacheManager.clearCache(promptType);
  }
  
  // Enhanced cost tracking
  getProviderStats(): {
    primary: { name: string; available: boolean };
    fallback: { name: string; available: boolean };
    cacheStats: ReturnType<LLMCacheManager['getStats']>;
    estimatedMonthlySavings: number;
  } {
    const primaryProvider = this.providers.get(this.primaryProvider);
    const fallbackProvider = this.providers.get(this.fallbackProvider);
    const cacheStats = this.cacheManager.getStats();
    
    // Estimate monthly savings based on current cache performance
    const daysElapsed = Math.max(1, (Date.now() - cacheStats.lastReset) / (24 * 60 * 60 * 1000));
    const dailySavings = cacheStats.totalCostSaved / daysElapsed;
    const estimatedMonthlySavings = dailySavings * 30;
    
    return {
      primary: {
        name: primaryProvider?.name || 'Unknown',
        available: !!(primaryProvider?.apiKey)
      },
      fallback: {
        name: fallbackProvider?.name || 'Unknown', 
        available: !!(fallbackProvider?.apiKey)
      },
      cacheStats,
      estimatedMonthlySavings
    };
  }
  
  // Cleanup method
  cleanup(): void {
    console.log('🧹 Cleaning up LLM Orchestrator...');
    this.cacheManager.cleanup();
    console.log('✅ LLM Orchestrator cleanup complete');
  }
}