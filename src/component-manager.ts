/**
 * Component Manager - Lazy Loading System
 * 
 * Manages lazy initialization of enhanced scraping components to improve:
 * - Startup performance (components only load when needed)
 * - Memory usage (no unused component overhead)
 * - Resource efficiency (heavy dependencies loaded on-demand)
 * - Error isolation (component failures don't affect startup)
 */

import { LLMOrchestrator } from './llm-orchestrator.js';
import { VisualScrapingEngine } from './visual-scraping-engine.js';
import { KPIMonitoringSystem } from './kpi-monitoring-system.js';
import { DataQualityAnalyzer } from './data-quality-analyzer.js';
import { EnhancedEvasionSystem } from './enhanced-evasion-system.js';
import { BlockAnalyzer } from './block-analyzer.js';
import { EnhancementEngine } from './enhancement-engine.js';
import { ResearchAgent } from './research-agent.js';
import { HealthReporter } from './health-reporter.js';
import { CommunicationLayer } from './communication-layer.js';

interface ComponentStatus {
  initialized: boolean;
  initializing: boolean;
  error?: Error;
  lastAccessed: number;
  initTime?: number;
}

export class ComponentManager {
  private static instance: ComponentManager;
  
  // Component instances (lazy initialized)
  private _llmOrchestrator: LLMOrchestrator | null = null;
  private _visualEngine: VisualScrapingEngine | null = null;
  private _kpiMonitor: KPIMonitoringSystem | null = null;
  private _qualityAnalyzer: DataQualityAnalyzer | null = null;
  private _evasionSystem: EnhancedEvasionSystem | null = null;
  private _blockAnalyzer: BlockAnalyzer | null = null;
  private _enhancementEngine: EnhancementEngine | null = null;
  private _researchAgent: ResearchAgent | null = null;
  private _healthReporter: HealthReporter | null = null;
  private _communicationLayer: CommunicationLayer | null = null;
  
  // Component status tracking
  private componentStatus: Map<string, ComponentStatus> = new Map();
  
  // Initialization locks to prevent race conditions
  private initPromises: Map<string, Promise<any>> = new Map();
  
  private constructor() {
    // Initialize status tracking for all components
    const components = [
      'llmOrchestrator', 'visualEngine', 'kpiMonitor', 'qualityAnalyzer',
      'evasionSystem', 'blockAnalyzer', 'enhancementEngine', 'researchAgent',
      'healthReporter', 'communicationLayer'
    ];
    
    components.forEach(component => {
      this.componentStatus.set(component, {
        initialized: false,
        initializing: false,
        lastAccessed: 0
      });
    });
    
    console.log('🔧 Component Manager initialized with lazy loading');
  }
  
  static getInstance(): ComponentManager {
    if (!ComponentManager.instance) {
      ComponentManager.instance = new ComponentManager();
    }
    return ComponentManager.instance;
  }
  
  // Core component getters with lazy initialization
  async getLLMOrchestrator(): Promise<LLMOrchestrator> {
    const componentName = 'llmOrchestrator';
    
    if (this._llmOrchestrator) {
      this.updateLastAccessed(componentName);
      return this._llmOrchestrator;
    }
    
    return this.initializeComponent(componentName, async () => {
      console.log('🧠 Initializing LLM Orchestrator...');
      this._llmOrchestrator = new LLMOrchestrator();
      return this._llmOrchestrator;
    });
  }
  
  async getVisualEngine(): Promise<VisualScrapingEngine> {
    const componentName = 'visualEngine';
    
    if (this._visualEngine) {
      this.updateLastAccessed(componentName);
      return this._visualEngine;
    }
    
    return this.initializeComponent(componentName, async () => {
      console.log('📸 Initializing Visual Scraping Engine...');
      this._visualEngine = new VisualScrapingEngine();
      // Initialize OCR on first use, not construction
      return this._visualEngine;
    });
  }
  
  async getKPIMonitor(): Promise<KPIMonitoringSystem> {
    const componentName = 'kpiMonitor';
    
    if (this._kpiMonitor) {
      this.updateLastAccessed(componentName);
      return this._kpiMonitor;
    }
    
    return this.initializeComponent(componentName, async () => {
      console.log('📊 Initializing KPI Monitoring System...');
      this._kpiMonitor = new KPIMonitoringSystem();
      return this._kpiMonitor;
    });
  }
  
  async getQualityAnalyzer(): Promise<DataQualityAnalyzer> {
    const componentName = 'qualityAnalyzer';
    
    if (this._qualityAnalyzer) {
      this.updateLastAccessed(componentName);
      return this._qualityAnalyzer;
    }
    
    return this.initializeComponent(componentName, async () => {
      console.log('🔍 Initializing Data Quality Analyzer...');
      this._qualityAnalyzer = new DataQualityAnalyzer();
      return this._qualityAnalyzer;
    });
  }
  
  async getEvasionSystem(): Promise<EnhancedEvasionSystem> {
    const componentName = 'evasionSystem';
    
    if (this._evasionSystem) {
      this.updateLastAccessed(componentName);
      return this._evasionSystem;
    }
    
    return this.initializeComponent(componentName, async () => {
      console.log('🛡️ Initializing Enhanced Evasion System...');
      this._evasionSystem = new EnhancedEvasionSystem();
      return this._evasionSystem;
    });
  }
  
  async getBlockAnalyzer(): Promise<BlockAnalyzer> {
    const componentName = 'blockAnalyzer';
    
    if (this._blockAnalyzer) {
      this.updateLastAccessed(componentName);
      return this._blockAnalyzer;
    }
    
    return this.initializeComponent(componentName, async () => {
      console.log('🚫 Initializing Block Analyzer...');
      // Lazy load dependencies
      const visualEngine = await this.getVisualEngine();
      const evasionSystem = await this.getEvasionSystem();
      const llmOrchestrator = await this.getLLMOrchestrator();
      
      this._blockAnalyzer = new BlockAnalyzer(visualEngine, evasionSystem, llmOrchestrator);
      return this._blockAnalyzer;
    });
  }
  
  async getEnhancementEngine(): Promise<EnhancementEngine> {
    const componentName = 'enhancementEngine';
    
    if (this._enhancementEngine) {
      this.updateLastAccessed(componentName);
      return this._enhancementEngine;
    }
    
    return this.initializeComponent(componentName, async () => {
      console.log('⚡ Initializing Enhancement Engine...');
      // Lazy load dependencies
      const kpiMonitor = await this.getKPIMonitor();
      const qualityAnalyzer = await this.getQualityAnalyzer();
      const llmOrchestrator = await this.getLLMOrchestrator();
      const blockAnalyzer = await this.getBlockAnalyzer();
      
      this._enhancementEngine = new EnhancementEngine(kpiMonitor, qualityAnalyzer, llmOrchestrator, blockAnalyzer);
      return this._enhancementEngine;
    });
  }
  
  async getResearchAgent(): Promise<ResearchAgent> {
    const componentName = 'researchAgent';
    
    if (this._researchAgent) {
      this.updateLastAccessed(componentName);
      return this._researchAgent;
    }
    
    return this.initializeComponent(componentName, async () => {
      console.log('🔬 Initializing Research Agent...');
      const llmOrchestrator = await this.getLLMOrchestrator();
      this._researchAgent = new ResearchAgent(llmOrchestrator);
      return this._researchAgent;
    });
  }
  
  async getHealthReporter(): Promise<HealthReporter> {
    const componentName = 'healthReporter';
    
    if (this._healthReporter) {
      this.updateLastAccessed(componentName);
      return this._healthReporter;
    }
    
    return this.initializeComponent(componentName, async () => {
      console.log('🏥 Initializing Health Reporter...');
      // Lazy load dependencies
      const kpiMonitor = await this.getKPIMonitor();
      const qualityAnalyzer = await this.getQualityAnalyzer();
      const blockAnalyzer = await this.getBlockAnalyzer();
      const enhancementEngine = await this.getEnhancementEngine();
      const researchAgent = await this.getResearchAgent();
      
      this._healthReporter = new HealthReporter(
        kpiMonitor, qualityAnalyzer, blockAnalyzer, 
        './data/health', enhancementEngine, researchAgent
      );
      return this._healthReporter;
    });
  }
  
  async getCommunicationLayer(): Promise<CommunicationLayer> {
    const componentName = 'communicationLayer';
    
    if (this._communicationLayer) {
      this.updateLastAccessed(componentName);
      return this._communicationLayer;
    }
    
    return this.initializeComponent(componentName, async () => {
      console.log('📡 Initializing Communication Layer...');
      // Lazy load dependencies
      const healthReporter = await this.getHealthReporter();
      const kpiMonitor = await this.getKPIMonitor();
      const enhancementEngine = await this.getEnhancementEngine();
      const researchAgent = await this.getResearchAgent();
      
      this._communicationLayer = new CommunicationLayer(
        healthReporter, kpiMonitor, './data/communication', 
        enhancementEngine, researchAgent
      );
      return this._communicationLayer;
    });
  }
  
  // Generic component initialization with race condition protection
  private async initializeComponent<T>(componentName: string, initializer: () => Promise<T>): Promise<T> {
    const status = this.componentStatus.get(componentName)!;
    
    // If already initializing, wait for the existing promise
    if (status.initializing && this.initPromises.has(componentName)) {
      return this.initPromises.get(componentName)! as Promise<T>;
    }
    
    // Mark as initializing
    status.initializing = true;
    status.lastAccessed = Date.now();
    const startTime = Date.now();
    
    const initPromise = initializer().then((component) => {
      // Mark as successfully initialized
      status.initialized = true;
      status.initializing = false;
      status.initTime = Date.now() - startTime;
      status.error = undefined;
      
      console.log(`✅ Component '${componentName}' initialized in ${status.initTime}ms`);
      return component;
    }).catch((error) => {
      // Mark as failed
      status.initialized = false;
      status.initializing = false;
      status.error = error;
      
      console.error(`❌ Component '${componentName}' initialization failed:`, error);
      throw error;
    }).finally(() => {
      // Clean up the init promise
      this.initPromises.delete(componentName);
    });
    
    this.initPromises.set(componentName, initPromise);
    return initPromise;
  }
  
  private updateLastAccessed(componentName: string): void {
    const status = this.componentStatus.get(componentName);
    if (status) {
      status.lastAccessed = Date.now();
    }
  }
  
  // Management methods
  getComponentStatus(componentName?: string): Map<string, ComponentStatus> | ComponentStatus | undefined {
    if (componentName) {
      return this.componentStatus.get(componentName);
    }
    return this.componentStatus;
  }
  
  getInitializedComponents(): string[] {
    return Array.from(this.componentStatus.entries())
      .filter(([_, status]) => status.initialized)
      .map(([name]) => name);
  }
  
  getComponentStats(): {
    total: number;
    initialized: number;
    initializing: number;
    failed: number;
    totalInitTime: number;
  } {
    const stats = {
      total: this.componentStatus.size,
      initialized: 0,
      initializing: 0,
      failed: 0,
      totalInitTime: 0
    };
    
    for (const status of this.componentStatus.values()) {
      if (status.initialized) {
        stats.initialized++;
        stats.totalInitTime += status.initTime || 0;
      } else if (status.initializing) {
        stats.initializing++;
      } else if (status.error) {
        stats.failed++;
      }
    }
    
    return stats;
  }
  
  // Cleanup method
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up Component Manager...');
    
    const cleanupPromises: Promise<void>[] = [];
    
    if (this._visualEngine) {
      cleanupPromises.push(this._visualEngine.cleanup());
    }
    if (this._healthReporter) {
      cleanupPromises.push(Promise.resolve(this._healthReporter.cleanup()));
    }
    if (this._enhancementEngine) {
      cleanupPromises.push(Promise.resolve(this._enhancementEngine.cleanup()));
    }
    if (this._researchAgent) {
      cleanupPromises.push(Promise.resolve(this._researchAgent.cleanup()));
    }
    if (this._blockAnalyzer) {
      cleanupPromises.push(Promise.resolve(this._blockAnalyzer.cleanup()));
    }
    if (this._communicationLayer) {
      cleanupPromises.push(Promise.resolve(this._communicationLayer.cleanup()));
    }
    
    await Promise.allSettled(cleanupPromises);
    
    // Clear all references
    this._llmOrchestrator = null;
    this._visualEngine = null;
    this._kpiMonitor = null;
    this._qualityAnalyzer = null;
    this._evasionSystem = null;
    this._blockAnalyzer = null;
    this._enhancementEngine = null;
    this._researchAgent = null;
    this._healthReporter = null;
    this._communicationLayer = null;
    
    // Reset status
    this.componentStatus.clear();
    this.initPromises.clear();
    
    console.log('✅ Component Manager cleanup complete');
  }
}