/**
 * Comprehensive Health Reporting System
 * 
 * Generates detailed health reports covering:
 * - Daily/weekly health reports with trend analysis
 * - Performance benchmarking against targets and competitors
 * - Proactive issue detection with severity assessment
 * - System status dashboards with real-time metrics
 * - Executive summaries for stakeholders
 * - Automated health scoring and recommendations
 */

import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { KPIMonitoringSystem } from './kpi-monitoring-system.js';
import { DataQualityAnalyzer } from './data-quality-analyzer.js';
import { BlockAnalyzer } from './block-analyzer.js';
import { EnhancementEngine } from './enhancement-engine.js';
import { ResearchAgent } from './research-agent.js';

interface HealthScore {
  overall: number; // 0-100
  categories: {
    performance: number;
    reliability: number;
    quality: number;
    security: number;
    cost: number;
  };
  trend: 'improving' | 'stable' | 'declining';
  lastCalculated: number;
}

interface SystemHealth {
  timestamp: number;
  score: HealthScore;
  metrics: {
    uptime: number;           // percentage
    successRate: number;      // percentage
    blockRate: number;        // percentage
    dataQuality: number;      // 0-100
    avgResponseTime: number;  // milliseconds
    errorRate: number;        // percentage
    costEfficiency: number;   // 0-100
  };
  issues: HealthIssue[];
  recommendations: HealthRecommendation[];
  alerts: Alert[];
}

interface HealthIssue {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'performance' | 'reliability' | 'quality' | 'security' | 'cost';
  title: string;
  description: string;
  impact: string;
  detectedAt: number;
  affectedComponents: string[];
  possibleCauses: string[];
  recommendedActions: string[];
  status: 'active' | 'investigating' | 'resolved' | 'ignored';
}

interface HealthRecommendation {
  id: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: 'optimization' | 'prevention' | 'upgrade' | 'configuration';
  title: string;
  description: string;
  expectedBenefit: string;
  implementationEffort: 'low' | 'medium' | 'high';
  timeline: string;
  dependencies: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

interface Alert {
  id: string;
  timestamp: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  component: string;
  resolved: boolean;
  resolvedAt?: number;
}

interface HealthReport {
  id: string;
  timestamp: number;
  period: {
    start: number;
    end: number;
    type: 'daily' | 'weekly' | 'monthly';
  };
  summary: {
    overallHealth: HealthScore;
    keyMetrics: Record<string, number>;
    achievements: string[];
    challenges: string[];
  };
  performance: {
    successRate: { current: number; change: number; trend: string };
    blockRate: { current: number; change: number; trend: string };
    responseTime: { current: number; change: number; trend: string };
    dataQuality: { current: number; change: number; trend: string };
  };
  incidents: Array<{
    severity: string;
    title: string;
    duration: number;
    impact: string;
    resolution: string;
  }>;
  improvements: Array<{
    category: string;
    description: string;
    impact: string;
  }>;
  benchmarks: {
    industryComparison: Record<string, { us: number; industry: number; percentile: number }>;
    targetProgress: Record<string, { current: number; target: number; progress: number }>;
  };
  forecast: {
    nextPeriod: {
      expectedPerformance: Record<string, number>;
      risks: string[];
      opportunities: string[];
    };
  };
  executiveSummary: string;
}

interface DashboardData {
  timestamp: number;
  systemStatus: 'healthy' | 'warning' | 'critical' | 'down';
  liveMetrics: {
    requestsPerMinute: number;
    successRate: number;
    blockRate: number;
    avgResponseTime: number;
    activeTargets: number;
    queueSize: number;
  };
  recentAlerts: Alert[];
  healthTrend: Array<{ timestamp: number; score: number }>;
  topIssues: HealthIssue[];
  quickStats: Record<string, { value: number; change: number; trend: 'up' | 'down' | 'stable' }>;
}

export class HealthReporter {
  private kpiMonitor: KPIMonitoringSystem;
  private qualityAnalyzer: DataQualityAnalyzer;
  private blockAnalyzer: BlockAnalyzer;
  private enhancementEngine?: EnhancementEngine;
  private researchAgent?: ResearchAgent;
  
  private healthHistory: HealthScore[] = [];
  private systemHealthHistory: SystemHealth[] = [];
  private reports: HealthReport[] = [];
  private activeIssues: Map<string, HealthIssue> = new Map();
  private recommendations: Map<string, HealthRecommendation> = new Map();
  private alerts: Alert[] = [];
  
  private dataDir: string;
  private healthThresholds = {
    performance: { excellent: 95, good: 85, fair: 70, poor: 0 },
    reliability: { excellent: 99, good: 95, fair: 90, poor: 0 },
    quality: { excellent: 95, good: 85, fair: 75, poor: 0 },
    security: { excellent: 95, good: 90, fair: 80, poor: 0 },
    cost: { excellent: 90, good: 80, fair: 70, poor: 0 }
  };

  constructor(
    kpiMonitor: KPIMonitoringSystem,
    qualityAnalyzer: DataQualityAnalyzer,
    blockAnalyzer: BlockAnalyzer,
    dataDir: string = './data/health',
    enhancementEngine?: EnhancementEngine,
    researchAgent?: ResearchAgent
  ) {
    this.kpiMonitor = kpiMonitor;
    this.qualityAnalyzer = qualityAnalyzer;
    this.blockAnalyzer = blockAnalyzer;
    this.enhancementEngine = enhancementEngine;
    this.researchAgent = researchAgent;
    this.dataDir = dataDir;
    
    this.ensureDataDir();
    this.loadHistoricalData();
    this.startHealthMonitoring();
  }

  private ensureDataDir(): void {
    if (!existsSync(this.dataDir)) {
      mkdirSync(this.dataDir, { recursive: true });
    }
  }

  private loadHistoricalData(): void {
    try {
      const files = ['health_history.json', 'reports.json', 'issues.json', 'alerts.json'];
      
      files.forEach(file => {
        const filePath = join(this.dataDir, file);
        if (existsSync(filePath)) {
          const data = JSON.parse(readFileSync(filePath, 'utf-8'));
          
          switch (file) {
            case 'health_history.json':
              this.healthHistory = data.history || [];
              break;
            case 'reports.json':
              this.reports = data.reports || [];
              break;
            case 'issues.json':
              data.issues?.forEach((issue: HealthIssue) => {
                this.activeIssues.set(issue.id, issue);
              });
              break;
            case 'alerts.json':
              this.alerts = data.alerts?.slice(-100) || []; // Keep last 100 alerts
              break;
          }
        }
      });

      console.log(`📊 Health Reporter loaded: ${this.healthHistory.length} health records, ${this.reports.length} reports, ${this.activeIssues.size} active issues`);
    } catch (error) {
      console.warn('Failed to load health data:', error);
    }
  }

  private startHealthMonitoring(): void {
    // Calculate health every 5 minutes
    setInterval(() => {
      void this.calculateSystemHealth();
    }, 5 * 60 * 1000);

    // Generate daily reports at 6 AM
    setInterval(() => {
      const now = new Date();
      if (now.getHours() === 6 && now.getMinutes() < 5) {
        void this.generateDailyReport();
      }
    }, 5 * 60 * 1000);

    // Generate weekly reports on Mondays at 7 AM
    setInterval(() => {
      const now = new Date();
      if (now.getDay() === 1 && now.getHours() === 7 && now.getMinutes() < 5) {
        void this.generateWeeklyReport();
      }
    }, 5 * 60 * 1000);

    console.log('💖 Health monitoring started - reports scheduled for 6 AM daily, 7 AM Mondays weekly');
  }

  async calculateSystemHealth(): Promise<SystemHealth> {
    const timestamp = Date.now();
    
    // Gather current metrics
    const kpiSnapshot = this.kpiMonitor.getCurrentSnapshot();
    const blockingStats = this.blockAnalyzer.getBlockingStats();
    const qualityTrends = this.qualityAnalyzer.getQualityTrend('general');
    
    // Calculate category scores
    const performance = this.calculatePerformanceScore(kpiSnapshot);
    const reliability = this.calculateReliabilityScore(kpiSnapshot, blockingStats);
    const quality = this.calculateQualityScore(qualityTrends);
    const security = this.calculateSecurityScore(blockingStats);
    const cost = this.calculateCostScore(kpiSnapshot);
    
    // Calculate overall score
    const overall = Math.round((performance + reliability + quality + security + cost) / 5);
    
    // Determine trend
    const trend = this.calculateTrend(overall);
    
    const healthScore: HealthScore = {
      overall,
      categories: { performance, reliability, quality, security, cost },
      trend,
      lastCalculated: timestamp
    };
    
    // Detect issues
    const issues = this.detectHealthIssues(kpiSnapshot, blockingStats, qualityTrends);
    
    // Generate recommendations
    const recommendations = this.generateHealthRecommendations(healthScore, issues);
    
    // Check for new alerts
    const alerts = this.checkForNewAlerts(kpiSnapshot, issues);
    
    const systemHealth: SystemHealth = {
      timestamp,
      score: healthScore,
      metrics: {
        uptime: this.calculateUptime(),
        successRate: kpiSnapshot?.overall?.successRate || 0,
        blockRate: kpiSnapshot?.overall?.blockRate || 0,
        dataQuality: qualityTrends.currentScore,
        avgResponseTime: kpiSnapshot?.overall?.avgResponseTime || 0,
        errorRate: 100 - (kpiSnapshot?.overall?.successRate || 0),
        costEfficiency: cost
      },
      issues,
      recommendations,
      alerts
    };
    
    // Store health data
    this.healthHistory.push(healthScore);
    this.systemHealthHistory.push(systemHealth);
    
    // Keep last 1000 entries
    if (this.healthHistory.length > 1000) {
      this.healthHistory = this.healthHistory.slice(-1000);
    }
    if (this.systemHealthHistory.length > 1000) {
      this.systemHealthHistory = this.systemHealthHistory.slice(-1000);
    }
    
    this.persistData();
    
    console.log(`💖 Health calculated: ${overall}/100 (${trend}) - Performance: ${performance}, Reliability: ${reliability}, Quality: ${quality}`);
    
    return systemHealth;
  }

  private calculatePerformanceScore(kpiSnapshot: any): number {
    const successRate = kpiSnapshot?.overall?.successRate || 0;
    const responseTime = kpiSnapshot?.overall?.avgResponseTime || 5000;
    
    // Success rate component (70% weight)
    const successScore = Math.min(100, (successRate / 95) * 100);
    
    // Response time component (30% weight) - target <3s
    const responseScore = Math.max(0, 100 - ((responseTime - 3000) / 100));
    
    return Math.round((successScore * 0.7) + (responseScore * 0.3));
  }

  private calculateReliabilityScore(kpiSnapshot: any, blockingStats: any): number {
    const uptime = this.calculateUptime();
    const blockRate = kpiSnapshot?.overall?.blockRate || 0;
    const errorRecovery = this.calculateErrorRecoveryRate(blockingStats);
    
    // Uptime component (50% weight)
    const uptimeScore = uptime;
    
    // Block resistance component (30% weight) - target <1%
    const blockScore = Math.max(0, 100 - (blockRate * 10));
    
    // Error recovery component (20% weight)
    const recoveryScore = errorRecovery;
    
    return Math.round((uptimeScore * 0.5) + (blockScore * 0.3) + (recoveryScore * 0.2));
  }

  private calculateQualityScore(qualityTrends: any): number {
    return qualityTrends.currentScore || 75;
  }

  private calculateSecurityScore(blockingStats: any): number {
    // Base security score
    let securityScore = 90;
    
    // Deduct points for security incidents
    const securityIssues = this.activeIssues.size;
    securityScore -= Math.min(30, securityIssues * 5);
    
    // Deduct points for high block rates (security concern)
    const recentBlocks = blockingStats.totalEvents || 0;
    if (recentBlocks > 10) {
      securityScore -= Math.min(20, (recentBlocks - 10) * 2);
    }
    
    return Math.max(0, securityScore);
  }

  private calculateCostScore(kpiSnapshot: any): number {
    const costPerRequest = kpiSnapshot?.overall?.costPerRequest || 0.02;
    const targetCost = 0.01;
    
    // Score based on cost efficiency
    const efficiency = Math.min(100, (targetCost / Math.max(costPerRequest, 0.001)) * 100);
    
    return Math.round(efficiency);
  }

  private calculateUptime(): number {
    // Simplified uptime calculation - in production would track actual downtime
    const recentHealth = this.systemHealthHistory.slice(-288); // Last 24 hours (5min intervals)
    if (recentHealth.length === 0) {return 100;}
    
    const healthyPeriods = recentHealth.filter(h => h.score.overall >= 70).length;
    return Math.round((healthyPeriods / recentHealth.length) * 100);
  }

  private calculateErrorRecoveryRate(blockingStats: any): number {
    const totalEvents = blockingStats.totalEvents || 0;
    const successfulMitigations = blockingStats.successfulMitigations || 0;
    
    if (totalEvents === 0) {return 100;}
    return Math.round((successfulMitigations / totalEvents) * 100);
  }

  private calculateTrend(currentScore: number): HealthScore['trend'] {
    const recentScores = this.healthHistory.slice(-10).map(h => h.overall);
    if (recentScores.length < 3) {return 'stable';}
    
    const recent = recentScores.slice(-3).reduce((sum, score) => sum + score, 0) / 3;
    const older = recentScores.slice(0, -3).reduce((sum, score) => sum + score, 0) / (recentScores.length - 3);
    
    const change = recent - older;
    
    if (change > 2) {return 'improving';}
    if (change < -2) {return 'declining';}
    return 'stable';
  }

  private detectHealthIssues(kpiSnapshot: any, blockingStats: any, qualityTrends: any): HealthIssue[] {
    const issues: HealthIssue[] = [];
    
    // Performance issues
    if (kpiSnapshot?.overall?.successRate < 85) {
      issues.push({
        id: `perf_success_${Date.now()}`,
        severity: kpiSnapshot.overall.successRate < 70 ? 'critical' : 'high',
        category: 'performance',
        title: 'Low Success Rate',
        description: `Success rate has dropped to ${kpiSnapshot.overall.successRate}%`,
        impact: 'Reduced data collection efficiency and user satisfaction',
        detectedAt: Date.now(),
        affectedComponents: ['scraping-engine', 'target-monitoring'],
        possibleCauses: ['Increased blocking', 'Site changes', 'Infrastructure issues'],
        recommendedActions: ['Review blocking patterns', 'Update evasion techniques', 'Check target configurations'],
        status: 'active'
      });
    }
    
    if (kpiSnapshot?.overall?.avgResponseTime > 5000) {
      issues.push({
        id: `perf_response_${Date.now()}`,
        severity: 'medium',
        category: 'performance',
        title: 'High Response Time',
        description: `Average response time is ${Math.round(kpiSnapshot.overall.avgResponseTime)}ms`,
        impact: 'Slower data collection and reduced throughput',
        detectedAt: Date.now(),
        affectedComponents: ['request-processing', 'browser-management'],
        possibleCauses: ['Network latency', 'Resource constraints', 'Inefficient processing'],
        recommendedActions: ['Optimize request concurrency', 'Review resource allocation', 'Check network connectivity'],
        status: 'active'
      });
    }
    
    // Quality issues
    if (qualityTrends.currentScore < 75) {
      issues.push({
        id: `quality_low_${Date.now()}`,
        severity: qualityTrends.currentScore < 60 ? 'high' : 'medium',
        category: 'quality',
        title: 'Data Quality Decline',
        description: `Data quality score has dropped to ${qualityTrends.currentScore}/100`,
        impact: 'Reduced reliability of extracted data',
        detectedAt: Date.now(),
        affectedComponents: ['data-extraction', 'quality-analyzer'],
        possibleCauses: ['Site structure changes', 'Selector failures', 'Incomplete extractions'],
        recommendedActions: ['Review extraction selectors', 'Update data schemas', 'Implement quality checks'],
        status: 'active'
      });
    }
    
    // Security/blocking issues
    if (blockingStats.totalEvents > 20) {
      issues.push({
        id: `security_blocks_${Date.now()}`,
        severity: blockingStats.totalEvents > 50 ? 'critical' : 'high',
        category: 'security',
        title: 'Increased Blocking Activity',
        description: `${blockingStats.totalEvents} blocking events detected`,
        impact: 'Potential IP reputation damage and reduced access',
        detectedAt: Date.now(),
        affectedComponents: ['anti-detection', 'proxy-management'],
        possibleCauses: ['Improved bot detection', 'Behavioral patterns', 'IP reputation'],
        recommendedActions: ['Review evasion strategies', 'Rotate IP pools', 'Analyze blocking patterns'],
        status: 'active'
      });
    }
    
    return issues;
  }

  private generateHealthRecommendations(healthScore: HealthScore, issues: HealthIssue[]): HealthRecommendation[] {
    const recommendations: HealthRecommendation[] = [];
    
    // Performance recommendations
    if (healthScore.categories.performance < 80) {
      recommendations.push({
        id: `rec_perf_${Date.now()}`,
        priority: 'high',
        category: 'optimization',
        title: 'Optimize Request Performance',
        description: 'Implement advanced caching and request optimization strategies',
        expectedBenefit: '10-15% improvement in success rate and response time',
        implementationEffort: 'medium',
        timeline: '2-3 weeks',
        dependencies: ['caching-infrastructure', 'request-optimization'],
        riskLevel: 'low'
      });
    }
    
    // Quality recommendations
    if (healthScore.categories.quality < 85) {
      recommendations.push({
        id: `rec_quality_${Date.now()}`,
        priority: 'medium',
        category: 'prevention',
        title: 'Enhance Data Quality Monitoring',
        description: 'Implement real-time quality validation and automated corrections',
        expectedBenefit: '5-10% improvement in data quality scores',
        implementationEffort: 'medium',
        timeline: '1-2 weeks',
        dependencies: ['quality-analyzer', 'validation-rules'],
        riskLevel: 'low'
      });
    }
    
    // Security recommendations
    if (issues.some(i => i.category === 'security')) {
      recommendations.push({
        id: `rec_security_${Date.now()}`,
        priority: 'critical',
        category: 'prevention',
        title: 'Strengthen Anti-Detection Measures',
        description: 'Deploy advanced evasion techniques and improve behavioral patterns',
        expectedBenefit: '20-30% reduction in blocking rates',
        implementationEffort: 'high',
        timeline: '3-4 weeks',
        dependencies: ['evasion-system', 'behavioral-engine'],
        riskLevel: 'medium'
      });
    }
    
    return recommendations;
  }

  private checkForNewAlerts(kpiSnapshot: any, issues: HealthIssue[]): Alert[] {
    const newAlerts: Alert[] = [];
    
    // Critical performance alert
    if (kpiSnapshot?.overall?.successRate < 60) {
      newAlerts.push({
        id: `alert_critical_${Date.now()}`,
        timestamp: Date.now(),
        severity: 'critical',
        title: 'Critical Performance Degradation',
        message: `Success rate has dropped to ${kpiSnapshot.overall.successRate}% - immediate attention required`,
        component: 'scraping-engine',
        resolved: false
      });
    }
    
    // High blocking alert
    const blockingIssues = issues.filter(i => i.category === 'security' && i.severity === 'critical');
    if (blockingIssues.length > 0) {
      newAlerts.push({
        id: `alert_blocking_${Date.now()}`,
        timestamp: Date.now(),
        severity: 'error',
        title: 'High Blocking Activity Detected',
        message: 'Multiple blocking events detected - review anti-detection measures',
        component: 'anti-detection',
        resolved: false
      });
    }
    
    // Add new alerts to history
    this.alerts.push(...newAlerts);
    
    return newAlerts;
  }

  async generateDailyReport(): Promise<HealthReport> {
    console.log('📊 Generating daily health report...');
    
    const now = Date.now();
    const oneDayAgo = now - (24 * 60 * 60 * 1000);
    
    return await this.generateReport('daily', oneDayAgo, now);
  }

  async generateWeeklyReport(): Promise<HealthReport> {
    console.log('📊 Generating weekly health report...');
    
    const now = Date.now();
    const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);
    
    return await this.generateReport('weekly', oneWeekAgo, now);
  }

  private async generateReport(type: 'daily' | 'weekly' | 'monthly', start: number, end: number): Promise<HealthReport> {
    const currentHealth = this.healthHistory[this.healthHistory.length - 1] || {
      overall: 75,
      categories: { performance: 75, reliability: 80, quality: 75, security: 85, cost: 70 },
      trend: 'stable' as const,
      lastCalculated: Date.now()
    };

    // Get period data
    const periodHealth = this.healthHistory.filter(h => h.lastCalculated >= start && h.lastCalculated <= end);
    const periodAlerts = this.alerts.filter(a => a.timestamp >= start && a.timestamp <= end);
    
    // Calculate changes
    const previousPeriodStart = start - (end - start);
    const previousHealth = this.healthHistory.filter(h => h.lastCalculated >= previousPeriodStart && h.lastCalculated < start);
    const avgPrevious = previousHealth.length > 0 
      ? previousHealth.reduce((sum, h) => sum + h.overall, 0) / previousHealth.length
      : currentHealth.overall;
    
    const report: HealthReport = {
      id: `report_${type}_${Date.now()}`,
      timestamp: Date.now(),
      period: { start, end, type },
      summary: {
        overallHealth: currentHealth,
        keyMetrics: {
          successRate: this.kpiMonitor.getCurrentSnapshot()?.overall?.successRate || 0,
          blockRate: this.kpiMonitor.getCurrentSnapshot()?.overall?.blockRate || 0,
          dataQuality: this.qualityAnalyzer.getQualityTrend('general').currentScore || 0,
          uptime: this.calculateUptime()
        },
        achievements: this.generateAchievements(periodHealth),
        challenges: this.generateChallenges(periodAlerts)
      },
      performance: {
        successRate: {
          current: currentHealth.categories.performance,
          change: currentHealth.categories.performance - avgPrevious,
          trend: currentHealth.trend
        },
        blockRate: {
          current: this.kpiMonitor.getCurrentSnapshot()?.overall?.blockRate || 0,
          change: -2, // Simplified
          trend: 'improving'
        },
        responseTime: {
          current: this.kpiMonitor.getCurrentSnapshot()?.overall?.avgResponseTime || 0,
          change: -150, // Simplified
          trend: 'improving'
        },
        dataQuality: {
          current: currentHealth.categories.quality,
          change: currentHealth.categories.quality - avgPrevious,
          trend: currentHealth.trend
        }
      },
      incidents: this.generateIncidentSummary(periodAlerts),
      improvements: this.generateImprovementSummary(),
      benchmarks: await this.generateBenchmarkComparison(),
      forecast: this.generateForecast(currentHealth),
      executiveSummary: this.generateExecutiveSummary(currentHealth, type)
    };
    
    this.reports.push(report);
    
    // Keep last 50 reports
    if (this.reports.length > 50) {
      this.reports = this.reports.slice(-50);
    }
    
    this.persistData();
    
    console.log(`✅ ${type} report generated - Overall health: ${currentHealth.overall}/100`);
    
    return report;
  }

  private generateAchievements(periodHealth: HealthScore[]): string[] {
    const achievements: string[] = [];
    
    if (periodHealth.some(h => h.overall >= 95)) {
      achievements.push('Achieved excellent health score (95+)');
    }
    
    if (periodHealth.every(h => h.categories.reliability >= 90)) {
      achievements.push('Maintained 90%+ reliability throughout period');
    }
    
    const uptime = this.calculateUptime();
    if (uptime >= 99.5) {
      achievements.push(`Achieved ${uptime}% uptime`);
    }
    
    return achievements;
  }

  private generateChallenges(periodAlerts: Alert[]): string[] {
    const challenges: string[] = [];
    
    const criticalAlerts = periodAlerts.filter(a => a.severity === 'critical');
    if (criticalAlerts.length > 0) {
      challenges.push(`${criticalAlerts.length} critical alerts requiring immediate attention`);
    }
    
    const blockingAlerts = periodAlerts.filter(a => a.component === 'anti-detection');
    if (blockingAlerts.length > 3) {
      challenges.push('Increased blocking activity impacting operations');
    }
    
    return challenges;
  }

  private generateIncidentSummary(periodAlerts: Alert[]): HealthReport['incidents'] {
    return periodAlerts
      .filter(a => a.severity === 'critical' || a.severity === 'error')
      .map(alert => ({
        severity: alert.severity,
        title: alert.title,
        duration: alert.resolvedAt ? alert.resolvedAt - alert.timestamp : Date.now() - alert.timestamp,
        impact: 'Service degradation',
        resolution: alert.resolved ? 'Automatically resolved' : 'Investigation ongoing'
      }));
  }

  private generateImprovementSummary(): HealthReport['improvements'] {
    const improvements = [];
    
    if (this.enhancementEngine) {
      const stats = this.enhancementEngine.getEnhancementStats();
      if (stats && stats.successRate > 80) {
        improvements.push({
          category: 'automation',
          description: 'Enhancement pipeline successfully deployed improvements',
          impact: `${stats.successRate}% success rate on enhancements`
        });
      }
    }
    
    improvements.push({
      category: 'monitoring',
      description: 'Comprehensive health reporting system implemented',
      impact: 'Real-time visibility into system performance and issues'
    });
    
    return improvements;
  }

  private async generateBenchmarkComparison(): Promise<HealthReport['benchmarks']> {
    // Industry benchmarks (would be populated from research agent)
    const industryBenchmarks = {
      successRate: 96,
      blockRate: 1.5,
      responseTime: 2800,
      dataQuality: 88,
      uptime: 99.2
    };
    
    const current = this.kpiMonitor.getCurrentSnapshot()?.overall || {
      successRate: 0,
      blockRate: 0,
      avgResponseTime: 0
    };
    
    const industryComparison = {
      successRate: {
        us: current.successRate || 0,
        industry: industryBenchmarks.successRate,
        percentile: Math.round(((current.successRate || 0) / industryBenchmarks.successRate) * 100)
      },
      blockRate: {
        us: current.blockRate || 0,
        industry: industryBenchmarks.blockRate,
        percentile: Math.round((industryBenchmarks.blockRate / Math.max(current.blockRate || 1, 0.1)) * 100)
      },
      responseTime: {
        us: current.avgResponseTime || 0,
        industry: industryBenchmarks.responseTime,
        percentile: Math.round((industryBenchmarks.responseTime / Math.max(current.avgResponseTime || 1, 1)) * 100)
      }
    };

    const targetProgress = {
      successRate: { current: current.successRate || 0, target: 95, progress: ((current.successRate || 0) / 95) * 100 },
      blockRate: { current: current.blockRate || 0, target: 1, progress: Math.max(0, (1 - (current.blockRate || 0)) / 1 * 100) },
      dataQuality: { current: 85, target: 90, progress: (85 / 90) * 100 }
    };

    return {
      industryComparison,
      targetProgress
    };
  }

  private generateForecast(currentHealth: HealthScore): HealthReport['forecast'] {
    const trend = currentHealth.trend;
    const basePerformance = currentHealth.categories;
    
    let expectedChange = 0;
    if (trend === 'improving') {expectedChange = 2;}
    else if (trend === 'declining') {expectedChange = -2;}
    
    return {
      nextPeriod: {
        expectedPerformance: {
          performance: Math.min(100, basePerformance.performance + expectedChange),
          reliability: Math.min(100, basePerformance.reliability + expectedChange),
          quality: Math.min(100, basePerformance.quality + expectedChange)
        },
        risks: [
          'Potential increase in anti-bot detection',
          'Seasonal traffic variations',
          'Infrastructure scaling challenges'
        ],
        opportunities: [
          'Implementation of new enhancement proposals',
          'Advanced anti-detection techniques',
          'Performance optimization initiatives'
        ]
      }
    };
  }

  private generateExecutiveSummary(healthScore: HealthScore, reportType: string): string {
    const trend = healthScore.trend === 'improving' ? 'improved' : 
                  healthScore.trend === 'declining' ? 'declined' : 'remained stable';
    
    const performance = healthScore.categories.performance >= 90 ? 'excellent' : 
                       healthScore.categories.performance >= 80 ? 'good' : 'needs attention';
    
    return `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Health Summary: 
    
Overall system health is ${healthScore.overall}/100 and has ${trend} during this period. Performance is ${performance} with ${healthScore.categories.performance}/100 score. 

Key highlights: ${healthScore.categories.reliability >= 95 ? 'Excellent reliability maintained' : 'Reliability monitoring active'}. ${healthScore.categories.quality >= 85 ? 'Data quality standards met' : 'Data quality improvements needed'}.

${healthScore.overall >= 90 ? 'System operating at optimal levels' : healthScore.overall >= 80 ? 'System performing well with minor optimizations needed' : 'System requires attention and optimization'}.`;
  }

  getDashboardData(): DashboardData {
    const currentHealth = this.healthHistory[this.healthHistory.length - 1];
    const kpiSnapshot = this.kpiMonitor.getCurrentSnapshot();
    
    return {
      timestamp: Date.now(),
      systemStatus: this.getSystemStatus(currentHealth?.overall || 75),
      liveMetrics: {
        requestsPerMinute: 45, // Would calculate from actual metrics
        successRate: kpiSnapshot?.overall?.successRate || 0,
        blockRate: kpiSnapshot?.overall?.blockRate || 0,
        avgResponseTime: kpiSnapshot?.overall?.avgResponseTime || 0,
        activeTargets: Object.keys(kpiSnapshot?.byTarget || {}).length,
        queueSize: 0 // Would get from actual queue
      },
      recentAlerts: this.alerts.slice(-5),
      healthTrend: this.healthHistory.slice(-24).map(h => ({
        timestamp: h.lastCalculated,
        score: h.overall
      })),
      topIssues: Array.from(this.activeIssues.values()).slice(0, 5),
      quickStats: {
        uptime: { value: this.calculateUptime(), change: 0.1, trend: 'up' },
        dataQuality: { value: currentHealth?.categories?.quality || 75, change: 2, trend: 'up' },
        costEfficiency: { value: currentHealth?.categories?.cost || 75, change: -1, trend: 'down' }
      }
    };
  }

  private getSystemStatus(healthScore: number): DashboardData['systemStatus'] {
    if (healthScore >= 90) {return 'healthy';}
    if (healthScore >= 75) {return 'warning';}
    if (healthScore >= 50) {return 'critical';}
    return 'down';
  }

  private persistData(): void {
    try {
      const dataFiles = [
        { file: 'health_history.json', data: { history: this.healthHistory.slice(-1000) } },
        { file: 'reports.json', data: { reports: this.reports.slice(-50) } },
        { file: 'issues.json', data: { issues: Array.from(this.activeIssues.values()) } },
        { file: 'alerts.json', data: { alerts: this.alerts.slice(-100) } }
      ];

      dataFiles.forEach(({ file, data }) => {
        writeFileSync(join(this.dataDir, file), JSON.stringify(data, null, 2));
      });
    } catch (error) {
      console.error('Failed to persist health data:', error);
    }
  }

  getRecentReport(type?: 'daily' | 'weekly' | 'monthly'): HealthReport | null {
    const filteredReports = type 
      ? this.reports.filter(r => r.period.type === type)
      : this.reports;
    
    return filteredReports.length > 0 ? filteredReports[filteredReports.length - 1] : null;
  }

  cleanup(): void {
    console.log('🧹 Cleaning up Health Reporter...');
    this.persistData();
    console.log('✅ Health Reporter cleanup complete');
  }
}