import json
from typing import List, Dict, Any
from datetime import datetime
from jinja2 import Template
import markdown

from .models import (
    Report, ScrapingRequest, ContentItem, TrendAnalysis, SourceType
)

class ReportGenerator:
    """Generates comprehensive reports from scraped data and analyses"""
    
    def __init__(self):
        self.report_templates = {
            "executive": self._get_executive_template(),
            "detailed": self._get_detailed_template(),
            "summary": self._get_summary_template()
        }
    
    async def generate_report(
        self,
        scraping_request: ScrapingRequest,
        content_items: List[ContentItem],
        analyses: List[TrendAnalysis],
        report_type: str = "detailed"
    ) -> Report:
        """Generate a comprehensive report"""
        
        # Create report title
        keywords_str = ", ".join(scraping_request.keywords)
        title = f"Social Media Analysis Report: {keywords_str}"
        
        # Generate executive summary
        executive_summary = self._generate_executive_summary(
            scraping_request, content_items, analyses
        )
        
        # Extract top content
        top_content = sorted(
            content_items,
            key=lambda x: sum(x.engagement.values()),
            reverse=True
        )[:20]
        
        # Generate insights
        insights = self._generate_insights(content_items, analyses)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(analyses, content_items)
        
        # Create raw data summary
        raw_data_summary = self._create_raw_data_summary(content_items)
        
        report = Report(
            title=title,
            query=scraping_request,
            executive_summary=executive_summary,
            trend_analyses=analyses,
            top_content=top_content,
            insights=insights,
            recommendations=recommendations,
            raw_data_summary=raw_data_summary
        )
        
        return report
    
    def _generate_executive_summary(
        self,
        request: ScrapingRequest,
        content_items: List[ContentItem],
        analyses: List[TrendAnalysis]
    ) -> str:
        """Generate executive summary"""
        
        total_items = len(content_items)
        keywords = ", ".join(request.keywords)
        timeframe = request.time_range
        
        # Source breakdown
        source_counts = {}
        for item in content_items:
            source_counts[item.source] = source_counts.get(item.source, 0) + 1
        
        # Overall sentiment
        positive_count = sum(1 for analysis in analyses 
                           for _ in range(analysis.sentiment_summary.get("positive", 0)))
        negative_count = sum(1 for analysis in analyses 
                           for _ in range(analysis.sentiment_summary.get("negative", 0)))
        
        # Top performing content
        if content_items:
            top_item = max(content_items, key=lambda x: sum(x.engagement.values()))
            top_engagement = sum(top_item.engagement.values())
        else:
            top_engagement = 0
        
        summary = f"""
**Executive Summary**

This report analyzes social media conversations around {keywords} over the past {timeframe}.
Our analysis covered {total_items:,} pieces of content across multiple platforms.

**Key Findings:**
- Total content analyzed: {total_items:,} items
- Platform distribution: {', '.join([f'{k}: {v}' for k, v in source_counts.items()])}
- Sentiment trends: {'Positive' if positive_count > negative_count else 'Mixed' if positive_count == negative_count else 'Negative'} overall sentiment
- Peak engagement: {top_engagement:,} interactions on top-performing content
- Trending topics show {'high' if len(analyses) > 3 else 'moderate'} discussion volume

**Strategic Implications:**
The data reveals significant social media activity around these topics, with clear patterns
in user engagement and sentiment that provide actionable insights for content strategy
and community engagement.
        """.strip()
        
        return summary
    
    def _generate_insights(
        self,
        content_items: List[ContentItem],
        analyses: List[TrendAnalysis]
    ) -> List[str]:
        """Generate key insights from the data"""
        insights = []
        
        if not content_items or not analyses:
            return ["Limited data available for insight generation."]
        
        # Engagement patterns
        avg_engagement = sum(sum(item.engagement.values()) for item in content_items) / len(content_items)
        high_engagement_items = [item for item in content_items if sum(item.engagement.values()) > avg_engagement * 2]
        
        if high_engagement_items:
            insights.append(
                f"High-engagement content ({len(high_engagement_items)} items) significantly outperforms "
                f"average engagement by {(len(high_engagement_items) / len(content_items) * 100):.1f}%"
            )
        
        # Sentiment insights
        for analysis in analyses:
            sentiment = analysis.sentiment_summary
            if sentiment.get("positive", 0) > sentiment.get("negative", 0) * 2:
                insights.append(
                    f"'{analysis.keyword}' shows strongly positive sentiment with "
                    f"{sentiment.get('positive', 0)} positive vs {sentiment.get('negative', 0)} negative mentions"
                )
            elif sentiment.get("negative", 0) > sentiment.get("positive", 0) * 1.5:
                insights.append(
                    f"'{analysis.keyword}' faces sentiment challenges with "
                    f"{sentiment.get('negative', 0)} negative vs {sentiment.get('positive', 0)} positive mentions"
                )
        
        # Timing insights
        peak_hours = []
        for analysis in analyses:
            if analysis.peak_times:
                peak_hours.extend([pt["hour"] for pt in analysis.peak_times[:2]])
        
        if peak_hours:
            common_hour = max(set(peak_hours), key=peak_hours.count)
            insights.append(
                f"Peak engagement occurs around {common_hour}:00, suggesting optimal posting times"
            )
        
        # Source-specific insights
        source_performance = {}
        for item in content_items:
            if item.source not in source_performance:
                source_performance[item.source] = []
            source_performance[item.source].append(sum(item.engagement.values()))
        
        for source, engagements in source_performance.items():
            avg_source_engagement = sum(engagements) / len(engagements)
            if avg_source_engagement > avg_engagement * 1.2:
                insights.append(
                    f"{source.title()} shows {(avg_source_engagement / avg_engagement - 1) * 100:.1f}% "
                    f"higher engagement than platform average"
                )
        
        # Contributor insights
        for analysis in analyses:
            if analysis.top_contributors:
                top_contributor = analysis.top_contributors[0]
                insights.append(
                    f"@{top_contributor['username']} is a key influencer for '{analysis.keyword}' "
                    f"with {top_contributor['count']} mentions and high engagement"
                )
        
        return insights[:10]  # Limit to top 10 insights
    
    def _generate_recommendations(
        self,
        analyses: List[TrendAnalysis],
        content_items: List[ContentItem]
    ) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        if not analyses:
            return ["Insufficient data for generating recommendations."]
        
        # Content strategy recommendations
        high_engagement_keywords = [
            analysis.keyword for analysis in analyses
            if analysis.sentiment_summary.get("positive", 0) > analysis.sentiment_summary.get("negative", 0)
        ]
        
        if high_engagement_keywords:
            recommendations.append(
                f"Focus content creation on positive-sentiment keywords: {', '.join(high_engagement_keywords[:3])}"
            )
        
        # Timing recommendations
        all_peak_times = []
        for analysis in analyses:
            all_peak_times.extend(analysis.peak_times)
        
        if all_peak_times:
            # Find most common peak hours
            hour_counts = {}
            for pt in all_peak_times:
                hour = pt["hour"]
                hour_counts[hour] = hour_counts.get(hour, 0) + pt["count"]
            
            top_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)[:3]
            recommendations.append(
                f"Optimize posting schedule for peak hours: {', '.join([f'{h}:00' for h, _ in top_hours])}"
            )
        
        # Influencer engagement recommendations
        top_influencers = []
        for analysis in analyses:
            if analysis.top_contributors:
                top_influencers.extend(analysis.top_contributors[:2])
        
        if top_influencers:
            unique_influencers = {inf["username"]: inf for inf in top_influencers}
            top_3 = list(unique_influencers.keys())[:3]
            recommendations.append(
                f"Consider engaging with key influencers: @{', @'.join(top_3)}"
            )
        
        # Content format recommendations
        avg_engagement = sum(sum(item.engagement.values()) for item in content_items) / len(content_items)
        high_performing = [item for item in content_items if sum(item.engagement.values()) > avg_engagement * 1.5]
        
        if high_performing:
            # Analyze high-performing content characteristics
            avg_length = sum(len(item.content) for item in high_performing) / len(high_performing)
            if avg_length > 200:
                recommendations.append(
                    "Long-form content (200+ characters) shows higher engagement rates"
                )
            else:
                recommendations.append(
                    "Concise content performs better - keep posts under 200 characters"
                )
        
        # Platform-specific recommendations
        source_engagement = {}
        for item in content_items:
            if item.source not in source_engagement:
                source_engagement[item.source] = []
            source_engagement[item.source].append(sum(item.engagement.values()))
        
        best_platform = max(source_engagement.keys(), 
                          key=lambda k: sum(source_engagement[k]) / len(source_engagement[k]))
        recommendations.append(
            f"Prioritize {best_platform.title()} for maximum engagement potential"
        )
        
        # Sentiment management recommendations
        negative_sentiment_keywords = [
            analysis.keyword for analysis in analyses
            if analysis.sentiment_summary.get("negative", 0) > analysis.sentiment_summary.get("positive", 0)
        ]
        
        if negative_sentiment_keywords:
            recommendations.append(
                f"Monitor and address negative sentiment around: {', '.join(negative_sentiment_keywords[:2])}"
            )
        
        return recommendations[:8]  # Limit to top 8 recommendations
    
    def _create_raw_data_summary(self, content_items: List[ContentItem]) -> Dict[str, Any]:
        """Create summary statistics of raw data"""
        if not content_items:
            return {}
        
        # Basic counts
        total_items = len(content_items)
        sources = list(set(item.source for item in content_items))
        authors = list(set(item.author for item in content_items))
        
        # Engagement statistics
        all_engagements = [sum(item.engagement.values()) for item in content_items]
        avg_engagement = sum(all_engagements) / len(all_engagements)
        max_engagement = max(all_engagements)
        min_engagement = min(all_engagements)
        
        # Time distribution
        timestamps = [item.timestamp for item in content_items]
        oldest = min(timestamps)
        newest = max(timestamps)
        
        # Content length statistics
        content_lengths = [len(item.content) for item in content_items]
        avg_length = sum(content_lengths) / len(content_lengths)
        
        return {
            "total_items": total_items,
            "unique_sources": len(sources),
            "unique_authors": len(authors),
            "engagement_stats": {
                "average": round(avg_engagement, 2),
                "maximum": max_engagement,
                "minimum": min_engagement
            },
            "time_range": {
                "oldest": oldest.isoformat(),
                "newest": newest.isoformat(),
                "span_hours": (newest - oldest).total_seconds() / 3600
            },
            "content_stats": {
                "average_length": round(avg_length, 2),
                "total_characters": sum(content_lengths)
            },
            "source_distribution": {
                source: len([item for item in content_items if item.source == source])
                for source in sources
            }
        }
    
    def export_report(self, report: Report, format: str = "json") -> str:
        """Export report in specified format"""
        if format == "json":
            return json.dumps(report.dict(), indent=2, default=str)
        elif format == "markdown":
            return self._export_as_markdown(report)
        elif format == "html":
            markdown_content = self._export_as_markdown(report)
            return markdown.markdown(markdown_content, extensions=['tables'])
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def _export_as_markdown(self, report: Report) -> str:
        """Export report as Markdown"""
        md_content = f"""# {report.title}

*Generated on {report.created_at.strftime('%Y-%m-%d %H:%M:%S')}*

## Executive Summary

{report.executive_summary}

## Key Insights

{chr(10).join([f'- {insight}' for insight in report.insights])}

## Recommendations

{chr(10).join([f'- {rec}' for rec in report.recommendations])}

## Trend Analysis

"""
        
        for analysis in report.trend_analyses:
            md_content += f"""### {analysis.keyword}

- **Total Mentions:** {analysis.total_mentions:,}
- **Sentiment:** {analysis.sentiment_summary.get('average', 0):.2f}
- **Top Contributor:** @{analysis.top_contributors[0]['username'] if analysis.top_contributors else 'N/A'}

"""
        
        md_content += """## Top Content

| Author | Content Preview | Engagement |
|--------|----------------|------------|
"""
        
        for item in report.top_content[:10]:
            preview = item.content[:50] + "..." if len(item.content) > 50 else item.content
            engagement = sum(item.engagement.values())
            md_content += f"| @{item.author} | {preview} | {engagement:,} |\n"
        
        return md_content
    
    def _get_executive_template(self) -> str:
        return """Executive Summary Template"""
    
    def _get_detailed_template(self) -> str:
        return """Detailed Report Template"""
    
    def _get_summary_template(self) -> str:
        return """Summary Report Template"""