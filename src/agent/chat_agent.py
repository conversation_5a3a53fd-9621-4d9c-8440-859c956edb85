import os
import json
import async<PERSON>
from typing import List, Dict, Any, Optional
from datetime import datetime
import openai
from pydantic import ValidationError

from .models import (
    ChatSession, ChatMessage, Report, ScrapingRequest,
    TrendAnalysis, ContentItem, QueryIntent, SourceType,
    AgentConfig, TrendingKeyword
)
from .scraping_coordinator import ScrapingCoordinator
from .report_generator import ReportGenerator
from .fabric_integration import FabricIntegration

class ChatAgent:
    def __init__(self, config: AgentConfig):
        self.config = config
        self.client = openai.AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.scraping_coordinator = ScrapingCoordinator()
        self.report_generator = ReportGenerator()
        self.fabric_integration = FabricIntegration()
        self.sessions: Dict[str, ChatSession] = {}
        
    async def create_session(self) -> ChatSession:
        """Create a new chat session"""
        session = ChatSession()
        session.messages.append(ChatMessage(
            role="system",
            content=self._get_system_prompt()
        ))
        self.sessions[session.id] = session
        return session
    
    def _get_system_prompt(self) -> str:
        return """You are an AI agent specialized in analyzing social media trends across X (Twitter) and Reddit. 
        You can:
        1. Scrape content from X and Reddit based on keywords
        2. Analyze trending topics and sentiment
        3. Generate comprehensive reports
        4. Answer questions about the data
        5. Identify key contributors and influencers
        6. Track trending keywords across platforms
        
        You also have access to Fabric patterns - proven AI prompts for specific analysis tasks.
        Use Fabric patterns when they're appropriate for the user's request, especially for:
        - Deep content analysis
        - Sentiment analysis  
        - Trend extraction
        - Cross-platform comparisons
        - Social media strategy generation
        
        Always be helpful, accurate, and provide actionable insights. When asked to analyze trends,
        consider engagement metrics, sentiment, timing patterns, and cross-platform differences."""
    
    async def parse_user_intent(self, message: str) -> QueryIntent:
        """Parse user message to understand intent and extract parameters"""
        intent_prompt = f"""Analyze this user message and extract the intent and parameters:
        
        Message: {message}
        
        Respond with a JSON object containing:
        - intent_type: one of ["scrape", "analyze", "report", "question", "trending"]
        - keywords: list of keywords to search for
        - sources: list of sources ["twitter", "reddit", "both"]
        - time_range: optional time range (hour, day, week, month)
        - specific_subreddits: optional list of subreddit names
        - specific_users: optional list of Twitter usernames
        - analysis_depth: one of ["basic", "detailed", "comprehensive"]
        """
        
        response = await self.client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": intent_prompt}],
            response_format={"type": "json_object"}
        )
        
        intent_data = json.loads(response.choices[0].message.content)
        return QueryIntent(**intent_data)
    
    async def process_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """Process a user message and return response"""
        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")
        
        # Add user message
        user_msg = ChatMessage(role="user", content=message)
        session.messages.append(user_msg)
        
        # Parse intent
        intent = await self.parse_user_intent(message)
        
        # Process based on intent
        if intent.intent_type == "scrape":
            response = await self._handle_scrape_request(session, intent)
        elif intent.intent_type == "analyze":
            response = await self._handle_analyze_request(session, intent)
        elif intent.intent_type == "report":
            response = await self._handle_report_request(session, intent)
        elif intent.intent_type == "trending":
            response = await self._handle_trending_request(session, intent)
        else:
            response = await self._handle_question(session, intent)
        
        # Add assistant response
        assistant_msg = ChatMessage(
            role="assistant",
            content=response["message"],
            metadata=response.get("metadata")
        )
        session.messages.append(assistant_msg)
        
        return response
    
    async def _handle_scrape_request(self, session: ChatSession, intent: QueryIntent) -> Dict[str, Any]:
        """Handle content scraping request"""
        scraping_request = ScrapingRequest(
            keywords=intent.keywords,
            sources=intent.sources,
            time_range=intent.time_range or "day",
            include_sentiment=True,
            include_comments=intent.analysis_depth in ["detailed", "comprehensive"]
        )
        
        # Execute scraping
        results = await self.scraping_coordinator.scrape_multiple_sources(scraping_request)
        
        # Store in session context
        session.context["last_scrape_results"] = results
        session.context["last_scrape_request"] = scraping_request.dict()
        
        # Generate summary
        summary = self._summarize_scrape_results(results)
        
        return {
            "message": summary,
            "metadata": {
                "items_scraped": len(results),
                "sources": list(set(item.source for item in results)),
                "keywords": intent.keywords
            }
        }
    
    async def _handle_analyze_request(self, session: ChatSession, intent: QueryIntent) -> Dict[str, Any]:
        """Handle trend analysis request"""
        # Get data to analyze
        if "last_scrape_results" not in session.context:
            # Need to scrape first
            scraping_request = ScrapingRequest(
                keywords=intent.keywords,
                sources=intent.sources,
                time_range=intent.time_range or "day"
            )
            results = await self.scraping_coordinator.scrape_multiple_sources(scraping_request)
            session.context["last_scrape_results"] = results
        else:
            results = session.context["last_scrape_results"]
        
        # Perform analysis
        analyses = []
        for keyword in intent.keywords:
            analysis = await self._analyze_keyword_trend(keyword, results)
            analyses.append(analysis)
        
        # Store analyses
        session.context["last_analyses"] = analyses
        
        # Generate summary
        summary = self._summarize_trend_analyses(analyses)
        
        return {
            "message": summary,
            "metadata": {
                "keywords_analyzed": intent.keywords,
                "total_content_analyzed": len(results)
            }
        }
    
    async def _handle_report_request(self, session: ChatSession, intent: QueryIntent) -> Dict[str, Any]:
        """Handle report generation request"""
        # Ensure we have data
        if "last_scrape_results" not in session.context:
            scraping_request = ScrapingRequest(
                keywords=intent.keywords,
                sources=intent.sources,
                time_range=intent.time_range or "week"
            )
            results = await self.scraping_coordinator.scrape_multiple_sources(scraping_request)
            session.context["last_scrape_results"] = results
        
        # Generate report
        report = await self.report_generator.generate_report(
            scraping_request=session.context.get("last_scrape_request", ScrapingRequest(keywords=intent.keywords)),
            content_items=session.context["last_scrape_results"],
            analyses=session.context.get("last_analyses", [])
        )
        
        # Store report
        session.active_report = report
        session.context["last_report"] = report.dict()
        
        return {
            "message": f"Generated comprehensive report: {report.title}\n\n{report.executive_summary}",
            "metadata": {
                "report_id": report.id,
                "sections": ["executive_summary", "trend_analyses", "insights", "recommendations"]
            }
        }
    
    async def _handle_trending_request(self, session: ChatSession, intent: QueryIntent) -> Dict[str, Any]:
        """Handle trending keywords discovery request"""
        # Discover trending keywords
        trending = await self.scraping_coordinator.discover_trending_keywords(
            sources=intent.sources,
            limit=10
        )
        
        # Store in context
        session.context["trending_keywords"] = [t.dict() for t in trending]
        
        # Format response
        trending_summary = self._format_trending_keywords(trending)
        
        return {
            "message": trending_summary,
            "metadata": {
                "trending_count": len(trending),
                "sources_checked": intent.sources
            }
        }
    
    async def _handle_question(self, session: ChatSession, intent: QueryIntent) -> Dict[str, Any]:
        """Handle general questions about the data"""
        # Build context from session
        context = self._build_question_context(session)
        
        # Generate response using LLM
        messages = [
            {"role": "system", "content": self._get_system_prompt()},
            {"role": "user", "content": f"Context: {json.dumps(context)}\n\nQuestion: {intent.keywords[0] if intent.keywords else 'general question'}"}
        ]
        
        response = await self.client.chat.completions.create(
            model=self.config.model,
            messages=messages,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens
        )
        
        return {
            "message": response.choices[0].message.content,
            "metadata": {
                "context_used": list(context.keys())
            }
        }
    
    async def _analyze_keyword_trend(self, keyword: str, content_items: List[ContentItem]) -> TrendAnalysis:
        """Analyze trend for a specific keyword"""
        relevant_items = [item for item in content_items if keyword.lower() in item.content.lower()]
        
        # Calculate metrics
        sources_breakdown = {}
        sentiment_scores = []
        contributors = {}
        timestamps = []
        
        for item in relevant_items:
            # Source breakdown
            sources_breakdown[item.source] = sources_breakdown.get(item.source, 0) + 1
            
            # Sentiment
            if item.sentiment:
                sentiment_scores.append(item.sentiment.get("score", 0))
            
            # Contributors
            if item.author not in contributors:
                contributors[item.author] = {"count": 0, "total_engagement": 0}
            contributors[item.author]["count"] += 1
            contributors[item.author]["total_engagement"] += sum(item.engagement.values())
            
            # Timestamps
            timestamps.append(item.timestamp)
        
        # Calculate sentiment summary
        sentiment_summary = {
            "positive": len([s for s in sentiment_scores if s > 0.3]),
            "neutral": len([s for s in sentiment_scores if -0.3 <= s <= 0.3]),
            "negative": len([s for s in sentiment_scores if s < -0.3]),
            "average": sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0
        }
        
        # Top contributors
        top_contributors = sorted(
            contributors.items(),
            key=lambda x: x[1]["total_engagement"],
            reverse=True
        )[:10]
        
        # Peak times analysis
        hour_counts = {}
        for ts in timestamps:
            hour = ts.hour
            hour_counts[hour] = hour_counts.get(hour, 0) + 1
        
        peak_times = sorted(
            [{"hour": h, "count": c} for h, c in hour_counts.items()],
            key=lambda x: x["count"],
            reverse=True
        )[:5]
        
        return TrendAnalysis(
            keyword=keyword,
            total_mentions=len(relevant_items),
            sources_breakdown=sources_breakdown,
            sentiment_summary=sentiment_summary,
            top_contributors=[{"username": k, **v} for k, v in top_contributors],
            peak_times=peak_times,
            related_keywords=[]  # Would need more sophisticated analysis
        )
    
    def _summarize_scrape_results(self, results: List[ContentItem]) -> str:
        """Generate summary of scraping results"""
        total = len(results)
        sources = {}
        for item in results:
            sources[item.source] = sources.get(item.source, 0) + 1
        
        summary = f"Successfully scraped {total} items:\n"
        for source, count in sources.items():
            summary += f"- {source}: {count} items\n"
        
        # Add top content preview
        if results:
            top_items = sorted(results, key=lambda x: sum(x.engagement.values()), reverse=True)[:3]
            summary += "\nTop content by engagement:\n"
            for i, item in enumerate(top_items, 1):
                summary += f"{i}. {item.content[:100]}... (by @{item.author})\n"
        
        return summary
    
    def _summarize_trend_analyses(self, analyses: List[TrendAnalysis]) -> str:
        """Generate summary of trend analyses"""
        summary = "Trend Analysis Summary:\n\n"
        
        for analysis in analyses:
            summary += f"**{analysis.keyword}**\n"
            summary += f"- Total mentions: {analysis.total_mentions}\n"
            summary += f"- Sentiment: {analysis.sentiment_summary['average']:.2f} "
            summary += f"({analysis.sentiment_summary['positive']} positive, "
            summary += f"{analysis.sentiment_summary['negative']} negative)\n"
            summary += f"- Top contributor: @{analysis.top_contributors[0]['username'] if analysis.top_contributors else 'N/A'}\n"
            summary += f"- Peak time: {analysis.peak_times[0]['hour'] if analysis.peak_times else 'N/A'}:00\n\n"
        
        return summary
    
    def _format_trending_keywords(self, trending: List[TrendingKeyword]) -> str:
        """Format trending keywords for display"""
        summary = "Current Trending Keywords:\n\n"
        
        for i, trend in enumerate(trending[:10], 1):
            summary += f"{i}. **{trend.keyword}**\n"
            summary += f"   - Source: {trend.source}\n"
            summary += f"   - Volume: {trend.volume:,}\n"
            if trend.sentiment is not None:
                summary += f"   - Sentiment: {trend.sentiment:.2f}\n"
            if trend.context:
                summary += f"   - Context: {trend.context}\n"
            summary += "\n"
        
        return summary
    
    def _build_question_context(self, session: ChatSession) -> Dict[str, Any]:
        """Build context for answering questions"""
        context = {}
        
        if "last_scrape_results" in session.context:
            context["scraped_items_count"] = len(session.context["last_scrape_results"])
            
        if "last_analyses" in session.context:
            context["analyses"] = [a.dict() for a in session.context["last_analyses"]]
            
        if "trending_keywords" in session.context:
            context["trending"] = session.context["trending_keywords"]
            
        if session.active_report:
            context["report_summary"] = {
                "title": session.active_report.title,
                "insights": session.active_report.insights,
                "recommendations": session.active_report.recommendations
            }
        
        return context