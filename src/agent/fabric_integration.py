import subprocess
import json
import os
import asyncio
from typing import Dict, List, Any, Optional
from .models import ContentItem, TrendAnalysis

class FabricIntegration:
    """Integration with Fabric for enhanced content analysis"""
    
    def __init__(self):
        self.fabric_path = self._find_fabric_binary()
        self.available_patterns = [
            'analyze_social_sentiment',
            'extract_trending_insights', 
            'compare_platform_sentiment',
            'generate_social_strategy',
            'extract_wisdom',
            'analyze_claims',
            'summarize'
        ]
    
    def _find_fabric_binary(self) -> str:
        """Find the Fabric binary location"""
        possible_paths = [
            os.path.join(os.path.expanduser("~"), "go", "bin", "fabric"),
            "/usr/local/bin/fabric",
            "/opt/homebrew/bin/fabric",
            "fabric"
        ]
        
        for path in possible_paths:
            try:
                if os.path.exists(path):
                    return path
            except:
                continue
        
        return os.path.join(os.path.expanduser("~"), "go", "bin", "fabric")
    
    async def apply_pattern(self, pattern_name: str, content: str, model: str = "gpt-4o") -> Dict[str, Any]:
        """Apply a Fabric pattern to content"""
        try:
            # Run Fabric pattern
            process = await asyncio.create_subprocess_exec(
                self.fabric_path,
                "--pattern", pattern_name,
                "--model", model,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate(content.encode())
            
            if process.returncode == 0:
                return {
                    "success": True,
                    "output": stdout.decode(),
                    "pattern": pattern_name,
                    "error": None
                }
            else:
                return {
                    "success": False,
                    "output": None,
                    "pattern": pattern_name,
                    "error": stderr.decode()
                }
                
        except Exception as e:
            return {
                "success": False,
                "output": None,
                "pattern": pattern_name,
                "error": str(e)
            }
    
    async def analyze_social_sentiment(self, content: str) -> Dict[str, Any]:
        """Analyze social media sentiment using custom Fabric pattern"""
        return await self.apply_pattern("analyze_social_sentiment", content)
    
    async def extract_trending_insights(self, content: str) -> Dict[str, Any]:
        """Extract trending insights from social media content"""
        return await self.apply_pattern("extract_trending_insights", content)
    
    async def compare_platform_sentiment(self, twitter_content: str, reddit_content: str) -> Dict[str, Any]:
        """Compare sentiment between Twitter and Reddit content"""
        combined_content = f"""TWITTER/X CONTENT:
{twitter_content}

REDDIT CONTENT:
{reddit_content}
"""
        return await self.apply_pattern("compare_platform_sentiment", combined_content)
    
    async def generate_social_strategy(self, analysis_data: str) -> Dict[str, Any]:
        """Generate social media strategy from analysis data"""
        return await self.apply_pattern("generate_social_strategy", analysis_data)
    
    async def enhance_content_analysis(self, content_items: List[ContentItem]) -> List[Dict[str, Any]]:
        """Enhanced analysis of content items using Fabric patterns"""
        enhanced_items = []
        
        for item in content_items[:10]:  # Limit to avoid overwhelming
            content_text = f"Author: {item.author}\nContent: {item.content}\nEngagement: {item.engagement}"
            
            # Apply sentiment analysis
            sentiment_result = await self.analyze_social_sentiment(content_text)
            
            enhanced_item = {
                "original_item": item.dict(),
                "fabric_analysis": sentiment_result,
                "enhanced_insights": {}
            }
            
            # If sentiment analysis succeeded, add insights
            if sentiment_result.get("success"):
                enhanced_item["enhanced_insights"]["sentiment_analysis"] = True
                enhanced_item["enhanced_insights"]["analysis_quality"] = "fabric_enhanced"
            
            enhanced_items.append(enhanced_item)
        
        return enhanced_items
    
    async def create_comprehensive_report(self, content_items: List[ContentItem], analyses: List[TrendAnalysis]) -> Dict[str, Any]:
        """Create a comprehensive report using Fabric patterns"""
        
        # Prepare content for analysis
        content_summary = self._prepare_content_summary(content_items)
        analysis_summary = self._prepare_analysis_summary(analyses)
        
        combined_data = f"""CONTENT ANALYSIS SUMMARY:
{content_summary}

TREND ANALYSIS SUMMARY:
{analysis_summary}
"""
        
        # Generate insights and strategy
        insights_result = await self.extract_trending_insights(combined_data)
        strategy_result = await self.generate_social_strategy(combined_data)
        
        return {
            "fabric_insights": insights_result,
            "fabric_strategy": strategy_result,
            "enhancement_applied": True,
            "patterns_used": ["extract_trending_insights", "generate_social_strategy"]
        }
    
    def _prepare_content_summary(self, content_items: List[ContentItem]) -> str:
        """Prepare content summary for Fabric analysis"""
        summary = f"Total items analyzed: {len(content_items)}\n\n"
        
        # Top content by engagement
        sorted_items = sorted(content_items, key=lambda x: sum(x.engagement.values()), reverse=True)
        
        summary += "TOP PERFORMING CONTENT:\n"
        for i, item in enumerate(sorted_items[:5], 1):
            engagement = sum(item.engagement.values())
            summary += f"{i}. @{item.author}: {item.content[:100]}... (Engagement: {engagement})\n"
        
        # Platform distribution
        platforms = {}
        for item in content_items:
            platforms[item.source] = platforms.get(item.source, 0) + 1
        
        summary += f"\nPLATFORMS: {dict(platforms)}\n"
        
        # Sentiment overview (if available)
        positive_sentiment = sum(1 for item in content_items if item.sentiment and item.sentiment.get("overall") == "positive")
        negative_sentiment = sum(1 for item in content_items if item.sentiment and item.sentiment.get("overall") == "negative")
        
        summary += f"SENTIMENT OVERVIEW: {positive_sentiment} positive, {negative_sentiment} negative\n"
        
        return summary
    
    def _prepare_analysis_summary(self, analyses: List[TrendAnalysis]) -> str:
        """Prepare trend analysis summary for Fabric processing"""
        if not analyses:
            return "No trend analyses available."
        
        summary = f"TREND ANALYSES FOR {len(analyses)} KEYWORDS:\n\n"
        
        for analysis in analyses:
            summary += f"KEYWORD: {analysis.keyword}\n"
            summary += f"- Total mentions: {analysis.total_mentions}\n"
            summary += f"- Sentiment: {analysis.sentiment_summary}\n"
            summary += f"- Top contributor: {analysis.top_contributors[0]['username'] if analysis.top_contributors else 'N/A'}\n"
            summary += f"- Peak times: {analysis.peak_times[:2] if analysis.peak_times else []}\n\n"
        
        return summary
    
    async def intelligent_pattern_selection(self, user_request: str, content_type: str) -> List[str]:
        """Intelligently select appropriate Fabric patterns based on user request"""
        patterns = []
        
        request_lower = user_request.lower()
        
        # Sentiment analysis patterns
        if any(word in request_lower for word in ["sentiment", "emotion", "feeling", "mood"]):
            patterns.append("analyze_social_sentiment")
        
        # Trending analysis patterns  
        if any(word in request_lower for word in ["trend", "trending", "viral", "popular"]):
            patterns.append("extract_trending_insights")
        
        # Comparison patterns
        if any(word in request_lower for word in ["compare", "difference", "vs", "versus"]):
            patterns.append("compare_platform_sentiment")
        
        # Strategy patterns
        if any(word in request_lower for word in ["strategy", "recommend", "advice", "plan"]):
            patterns.append("generate_social_strategy")
        
        # Wisdom extraction patterns
        if any(word in request_lower for word in ["insight", "wisdom", "learn", "takeaway"]):
            patterns.append("extract_wisdom")
        
        # Default patterns if none match
        if not patterns:
            if content_type == "analysis":
                patterns = ["analyze_social_sentiment", "extract_trending_insights"]
            else:
                patterns = ["extract_wisdom"]
        
        return patterns
    
    def get_available_patterns(self) -> List[str]:
        """Get list of available Fabric patterns"""
        return self.available_patterns.copy()