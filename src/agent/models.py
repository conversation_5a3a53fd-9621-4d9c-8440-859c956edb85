from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Literal, Any
from datetime import datetime
from enum import Enum

class SourceType(str, Enum):
    TWITTER = "twitter"
    REDDIT = "reddit"
    BOTH = "both"

class TrendingKeyword(BaseModel):
    keyword: str
    source: SourceType
    volume: int
    sentiment: Optional[float] = None
    context: Optional[str] = None

class ScrapingRequest(BaseModel):
    keywords: List[str] = Field(..., description="Keywords to search for")
    sources: List[SourceType] = Field(default=[SourceType.BOTH])
    limit_per_source: int = Field(default=50, description="Max items per source")
    time_range: Literal["hour", "day", "week", "month"] = Field(default="day")
    include_sentiment: bool = Field(default=True)
    include_comments: bool = Field(default=False)

class ContentItem(BaseModel):
    id: str
    source: SourceType
    author: str
    content: str
    url: str
    timestamp: datetime
    engagement: Dict[str, int] = Field(default_factory=dict)
    sentiment: Optional[Dict[str, Any]] = None
    keywords_matched: List[str] = Field(default_factory=list)

class TrendAnalysis(BaseModel):
    keyword: str
    total_mentions: int
    sources_breakdown: Dict[SourceType, int]
    sentiment_summary: Dict[str, float]
    top_contributors: List[Dict[str, Any]]
    peak_times: List[Dict[str, Any]]
    related_keywords: List[str]

class Report(BaseModel):
    id: str = Field(default_factory=lambda: f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    title: str
    created_at: datetime = Field(default_factory=datetime.now)
    query: ScrapingRequest
    executive_summary: str
    trend_analyses: List[TrendAnalysis]
    top_content: List[ContentItem]
    insights: List[str]
    recommendations: List[str]
    raw_data_summary: Dict[str, Any]

class ChatMessage(BaseModel):
    role: Literal["user", "assistant", "system"]
    content: str
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = None

class ChatSession(BaseModel):
    id: str = Field(default_factory=lambda: f"chat_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    messages: List[ChatMessage] = Field(default_factory=list)
    context: Dict[str, Any] = Field(default_factory=dict)
    active_report: Optional[Report] = None

class AgentCapability(BaseModel):
    name: str
    description: str
    parameters: Dict[str, Any]

class AgentConfig(BaseModel):
    model: str = Field(default="gpt-4-turbo-preview")
    temperature: float = Field(default=0.7, ge=0, le=2)
    max_tokens: int = Field(default=4000)
    capabilities: List[AgentCapability] = Field(default_factory=list)
    system_prompt: str = Field(default="")

class QueryIntent(BaseModel):
    intent_type: Literal["scrape", "analyze", "report", "question", "trending"]
    keywords: List[str]
    sources: List[SourceType]
    time_range: Optional[str] = None
    specific_subreddits: Optional[List[str]] = None
    specific_users: Optional[List[str]] = None
    analysis_depth: Literal["basic", "detailed", "comprehensive"] = "detailed"