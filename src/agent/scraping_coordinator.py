import asyncio
import j<PERSON>
from typing import List, Dict, Any
from datetime import datetime, timedelta

from .models import (
    ScrapingRequest, ContentItem, SourceType, TrendingKeyword
)

class ScrapingCoordinator:
    """Coordinates scraping across multiple sources"""
    
    def __init__(self):
        self.trending_cache = {}
        self.cache_timeout = timedelta(minutes=15)
    
    async def scrape_multiple_sources(self, request: ScrapingRequest) -> List[ContentItem]:
        """Scrape content from multiple sources based on request"""
        tasks = []
        
        for source in request.sources:
            if source in [SourceType.TWITTER, SourceType.BOTH]:
                tasks.append(self._scrape_twitter(request))
            if source in [SourceType.REDDIT, SourceType.BOTH]:
                tasks.append(self._scrape_reddit(request))
        
        # Execute scraping tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Flatten results and filter out exceptions
        all_items = []
        for result in results:
            if isinstance(result, list):
                all_items.extend(result)
            elif isinstance(result, Exception):
                print(f"Scraping error: {result}")
        
        # Sort by engagement and limit total results
        all_items.sort(key=lambda x: sum(x.engagement.values()), reverse=True)
        return all_items[:request.limit_per_source * len(request.sources)]
    
    async def _scrape_twitter(self, request: ScrapingRequest) -> List[ContentItem]:
        """Scrape Twitter/X for keywords"""
        items = []
        
        # Simulate Twitter API calls (would use actual Twitter API in production)
        for keyword in request.keywords:
            # This would call the X scraper from the main MCP server
            mock_tweets = self._generate_mock_twitter_data(keyword, request)
            items.extend(mock_tweets)
        
        return items
    
    async def _scrape_reddit(self, request: ScrapingRequest) -> List[ContentItem]:
        """Scrape Reddit for keywords"""
        items = []
        
        # Simulate Reddit API calls (would use actual Reddit scraper in production)
        for keyword in request.keywords:
            mock_posts = self._generate_mock_reddit_data(keyword, request)
            items.extend(mock_posts)
        
        return items
    
    async def discover_trending_keywords(self, sources: List[SourceType], limit: int = 10) -> List[TrendingKeyword]:
        """Discover trending keywords across sources"""
        cache_key = f"trending_{'-'.join(sources)}_{limit}"
        
        # Check cache
        if cache_key in self.trending_cache:
            cached_time, cached_data = self.trending_cache[cache_key]
            if datetime.now() - cached_time < self.cache_timeout:
                return cached_data
        
        trending = []
        
        # Discover from each source
        for source in sources:
            if source in [SourceType.TWITTER, SourceType.BOTH]:
                twitter_trends = await self._discover_twitter_trending()
                trending.extend(twitter_trends)
            
            if source in [SourceType.REDDIT, SourceType.BOTH]:
                reddit_trends = await self._discover_reddit_trending()
                trending.extend(reddit_trends)
        
        # Remove duplicates and sort by volume
        unique_trends = {}
        for trend in trending:
            if trend.keyword not in unique_trends or trend.volume > unique_trends[trend.keyword].volume:
                unique_trends[trend.keyword] = trend
        
        result = sorted(unique_trends.values(), key=lambda x: x.volume, reverse=True)[:limit]
        
        # Cache results
        self.trending_cache[cache_key] = (datetime.now(), result)
        
        return result
    
    async def _discover_twitter_trending(self) -> List[TrendingKeyword]:
        """Discover trending keywords from Twitter"""
        # Mock trending data (would use actual Twitter trends API)
        mock_trends = [
            TrendingKeyword(keyword="AI", source=SourceType.TWITTER, volume=15000, sentiment=0.3),
            TrendingKeyword(keyword="Bitcoin", source=SourceType.TWITTER, volume=12000, sentiment=-0.1),
            TrendingKeyword(keyword="Climate", source=SourceType.TWITTER, volume=8000, sentiment=-0.4),
            TrendingKeyword(keyword="Tech", source=SourceType.TWITTER, volume=6000, sentiment=0.2),
        ]
        return mock_trends
    
    async def _discover_reddit_trending(self) -> List[TrendingKeyword]:
        """Discover trending keywords from Reddit"""
        # Mock trending data (would use actual Reddit trending analysis)
        mock_trends = [
            TrendingKeyword(keyword="Programming", source=SourceType.REDDIT, volume=8000, sentiment=0.4),
            TrendingKeyword(keyword="Gaming", source=SourceType.REDDIT, volume=7500, sentiment=0.1),
            TrendingKeyword(keyword="Investing", source=SourceType.REDDIT, volume=6000, sentiment=-0.2),
            TrendingKeyword(keyword="Science", source=SourceType.REDDIT, volume=4500, sentiment=0.3),
        ]
        return mock_trends
    
    def _generate_mock_twitter_data(self, keyword: str, request: ScrapingRequest) -> List[ContentItem]:
        """Generate mock Twitter data for testing"""
        items = []
        base_time = datetime.now()
        
        for i in range(min(25, request.limit_per_source)):
            item = ContentItem(
                id=f"twitter_{keyword}_{i}",
                source=SourceType.TWITTER,
                author=f"user{i % 10}",
                content=f"This is a tweet about {keyword} with some interesting insights and opinions. #trending",
                url=f"https://x.com/user{i % 10}/status/{12345 + i}",
                timestamp=base_time - timedelta(hours=i),
                engagement={
                    "likes": 50 + i * 10,
                    "retweets": 20 + i * 5,
                    "replies": 10 + i * 2
                },
                keywords_matched=[keyword]
            )
            
            if request.include_sentiment:
                item.sentiment = {
                    "overall": "positive" if i % 3 == 0 else ("negative" if i % 3 == 1 else "neutral"),
                    "score": (i % 3 - 1) * 0.3,
                    "keywords": [keyword, "trending"]
                }
            
            items.append(item)
        
        return items
    
    def _generate_mock_reddit_data(self, keyword: str, request: ScrapingRequest) -> List[ContentItem]:
        """Generate mock Reddit data for testing"""
        items = []
        base_time = datetime.now()
        subreddits = ["technology", "news", "science", "programming", "worldnews"]
        
        for i in range(min(25, request.limit_per_source)):
            item = ContentItem(
                id=f"reddit_{keyword}_{i}",
                source=SourceType.REDDIT,
                author=f"redditor{i % 15}",
                content=f"Discussion about {keyword} and its implications for the future. Very interesting topic that deserves more attention.",
                url=f"https://reddit.com/r/{subreddits[i % len(subreddits)]}/comments/{54321 + i}",
                timestamp=base_time - timedelta(hours=i * 2),
                engagement={
                    "upvotes": 100 + i * 20,
                    "downvotes": 5 + i,
                    "comments": 25 + i * 3
                },
                keywords_matched=[keyword]
            )
            
            if request.include_sentiment:
                item.sentiment = {
                    "overall": "positive" if i % 4 == 0 else ("negative" if i % 4 == 1 else "neutral"),
                    "score": ((i % 4) - 1.5) * 0.4,
                    "keywords": [keyword, "discussion"]
                }
            
            items.append(item)
        
        return items
    
    async def get_keyword_statistics(self, keywords: List[str], sources: List[SourceType]) -> Dict[str, Any]:
        """Get comprehensive statistics for keywords across sources"""
        stats = {}
        
        for keyword in keywords:
            request = ScrapingRequest(
                keywords=[keyword],
                sources=sources,
                limit_per_source=100,
                include_sentiment=True
            )
            
            items = await self.scrape_multiple_sources(request)
            
            # Calculate statistics
            total_items = len(items)
            source_breakdown = {}
            sentiment_breakdown = {"positive": 0, "negative": 0, "neutral": 0}
            engagement_total = 0
            
            for item in items:
                # Source breakdown
                source_breakdown[item.source] = source_breakdown.get(item.source, 0) + 1
                
                # Sentiment breakdown
                if item.sentiment:
                    sentiment_breakdown[item.sentiment["overall"]] += 1
                
                # Total engagement
                engagement_total += sum(item.engagement.values())
            
            stats[keyword] = {
                "total_mentions": total_items,
                "source_breakdown": source_breakdown,
                "sentiment_breakdown": sentiment_breakdown,
                "average_engagement": engagement_total / total_items if total_items > 0 else 0,
                "timeframe": request.time_range
            }
        
        return stats