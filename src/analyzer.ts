import { SentimentAnalysis, RedditPost, RedditComment } from './types.js';

export class TrendingAnalyzer {
  private readonly positiveWords = new Set([
    'amazing', 'awesome', 'excellent', 'good', 'great', 'happy', 'love', 'wonderful',
    'fantastic', 'perfect', 'beautiful', 'best', 'brilliant', 'outstanding', 'positive',
    'success', 'win', 'winner', 'congratulations', 'excited', 'inspiring', 'incredible'
  ]);

  private readonly negativeWords = new Set([
    'awful', 'bad', 'terrible', 'hate', 'horrible', 'poor', 'sad', 'angry', 'worst',
    'disgusting', 'disappointed', 'fail', 'failure', 'negative', 'never', 'problem',
    'wrong', 'crisis', 'disaster', 'unfortunate', 'concerning', 'worried', 'fear'
  ]);

  async analyzeSentiment(
    posts: string[], 
    granularity: 'post' | 'aggregate' = 'aggregate'
  ): Promise<SentimentAnalysis | SentimentAnalysis[]> {
    if (granularity === 'post') {
      return posts.map(post => this.analyzeSinglePost(post));
    } else {
      return this.analyzeAggregate(posts);
    }
  }

  private analyzeSinglePost(text: string): SentimentAnalysis {
    const words = text.toLowerCase().split(/\s+/);
    const totalWords = words.length;
    
    let positiveCount = 0;
    let negativeCount = 0;
    const keywords: string[] = [];

    words.forEach(word => {
      const cleanWord = word.replace(/[^\w]/g, '');
      
      if (this.positiveWords.has(cleanWord)) {
        positiveCount++;
        keywords.push(cleanWord);
      } else if (this.negativeWords.has(cleanWord)) {
        negativeCount++;
        keywords.push(cleanWord);
      }
    });

    const sentimentScore = (positiveCount - negativeCount) / totalWords;
    
    let overall: 'positive' | 'negative' | 'neutral';
    if (sentimentScore > 0.1) {
      overall = 'positive';
    } else if (sentimentScore < -0.1) {
      overall = 'negative';
    } else {
      overall = 'neutral';
    }

    return {
      overall,
      score: sentimentScore,
      keywords: keywords.slice(0, 5) // Top 5 sentiment keywords
    };
  }

  private analyzeAggregate(posts: string[]): SentimentAnalysis {
    const allText = posts.join(' ');
    const individualAnalyses = posts.map(post => this.analyzeSinglePost(post));
    
    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;
    let totalScore = 0;

    individualAnalyses.forEach(analysis => {
      totalScore += analysis.score;
      switch (analysis.overall) {
        case 'positive':
          positiveCount++;
          break;
        case 'negative':
          negativeCount++;
          break;
        case 'neutral':
          neutralCount++;
          break;
      }
    });

    const averageScore = totalScore / posts.length;
    let overall: 'positive' | 'negative' | 'neutral';
    
    if (averageScore > 0.1) {
      overall = 'positive';
    } else if (averageScore < -0.1) {
      overall = 'negative';
    } else {
      overall = 'neutral';
    }

    // Extract top keywords from all posts
    const allKeywords: { [key: string]: number } = {};
    individualAnalyses.forEach(analysis => {
      analysis.keywords?.forEach(keyword => {
        allKeywords[keyword] = (allKeywords[keyword] || 0) + 1;
      });
    });

    const topKeywords = Object.entries(allKeywords)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([keyword]) => keyword);

    return {
      overall,
      score: averageScore,
      breakdown: {
        positive: (positiveCount / posts.length) * 100,
        negative: (negativeCount / posts.length) * 100,
        neutral: (neutralCount / posts.length) * 100
      },
      keywords: topKeywords
    };
  }

  extractTrendingHashtags(posts: string[]): { hashtag: string; count: number }[] {
    const hashtagCounts: { [key: string]: number } = {};
    
    posts.forEach(post => {
      const hashtags = post.match(/#\w+/g) || [];
      hashtags.forEach(tag => {
        const normalizedTag = tag.toLowerCase();
        hashtagCounts[normalizedTag] = (hashtagCounts[normalizedTag] || 0) + 1;
      });
    });

    return Object.entries(hashtagCounts)
      .map(([hashtag, count]) => ({ hashtag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20); // Top 20 hashtags
  }

  analyzeRedditTrends(posts: RedditPost[]): {
    topSubreddits: { name: string; count: number; avgScore: number }[];
    timeDistribution: { hour: number; count: number }[];
    contentPatterns: { type: string; count: number }[];
    engagementMetrics: {
      avgScore: number;
      avgComments: number;
      highEngagementThreshold: number;
    };
  } {
    // Analyze subreddit distribution
    const subredditStats: { [key: string]: { count: number; totalScore: number } } = {};
    
    posts.forEach(post => {
      if (!subredditStats[post.subreddit]) {
        subredditStats[post.subreddit] = { count: 0, totalScore: 0 };
      }
      subredditStats[post.subreddit].count++;
      subredditStats[post.subreddit].totalScore += post.score;
    });

    const topSubreddits = Object.entries(subredditStats)
      .map(([name, stats]) => ({
        name,
        count: stats.count,
        avgScore: stats.totalScore / stats.count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Analyze posting time distribution
    const hourDistribution: { [hour: number]: number } = {};
    posts.forEach(post => {
      const hour = new Date(post.timestamp).getHours();
      hourDistribution[hour] = (hourDistribution[hour] || 0) + 1;
    });

    const timeDistribution = Object.entries(hourDistribution)
      .map(([hour, count]) => ({ hour: parseInt(hour), count }))
      .sort((a, b) => a.hour - b.hour);

    // Analyze content patterns
    const contentTypes = {
      text: 0,
      link: 0,
      image: 0,
      video: 0
    };

    posts.forEach(post => {
      if (post.content && post.content.length > 100) {
        contentTypes.text++;
      } else if (post.url.includes('i.redd.it') || post.url.includes('imgur')) {
        contentTypes.image++;
      } else if (post.url.includes('v.redd.it') || post.url.includes('youtube')) {
        contentTypes.video++;
      } else {
        contentTypes.link++;
      }
    });

    const contentPatterns = Object.entries(contentTypes)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count);

    // Calculate engagement metrics
    const totalScore = posts.reduce((sum, post) => sum + post.score, 0);
    const totalComments = posts.reduce((sum, post) => sum + (post.commentCount || 0), 0);
    const avgScore = totalScore / posts.length;
    const avgComments = totalComments / posts.length;

    // High engagement threshold (top 10%)
    const sortedByScore = [...posts].sort((a, b) => b.score - a.score);
    const highEngagementThreshold = sortedByScore[Math.floor(posts.length * 0.1)]?.score || avgScore * 2;

    return {
      topSubreddits,
      timeDistribution,
      contentPatterns,
      engagementMetrics: {
        avgScore,
        avgComments,
        highEngagementThreshold
      }
    };
  }

  analyzeRedditCommentSentiment(comments: RedditComment[]): {
    overallSentiment: SentimentAnalysis;
    controversialTopics: string[];
    consensusTopics: string[];
    depthAnalysis: { depth: number; avgSentiment: number }[];
  } {
    // Analyze overall sentiment
    const commentTexts = comments.map(c => c.content);
    const overallSentiment = this.analyzeAggregate(commentTexts);

    // Find controversial topics (mixed sentiment)
    const topicSentiments: { [topic: string]: { positive: number; negative: number } } = {};
    
    comments.forEach(comment => {
      const sentiment = this.analyzeSinglePost(comment.content);
      const words = comment.content.toLowerCase().split(/\s+/);
      
      // Extract meaningful topics (words > 4 chars, not common words)
      const topics = words.filter((word: string) => {
        const clean = word.replace(/[^\w]/g, '');
        return clean.length > 4 && !this.commonWords.has(clean);
      });

      topics.forEach(topic => {
        if (!topicSentiments[topic]) {
          topicSentiments[topic] = { positive: 0, negative: 0 };
        }
        if (sentiment.overall === 'positive') {
          topicSentiments[topic].positive++;
        } else if (sentiment.overall === 'negative') {
          topicSentiments[topic].negative++;
        }
      });
    });

    // Identify controversial and consensus topics
    const controversialTopics: string[] = [];
    const consensusTopics: string[] = [];

    Object.entries(topicSentiments).forEach(([topic, sentiment]) => {
      const total = sentiment.positive + sentiment.negative;
      if (total < 3) {return;} // Need minimum mentions

      const ratio = sentiment.positive / total;
      if (ratio > 0.3 && ratio < 0.7) {
        controversialTopics.push(topic);
      } else if (ratio > 0.8 || ratio < 0.2) {
        consensusTopics.push(topic);
      }
    });

    // Analyze sentiment by comment depth
    const depthSentiments: { [depth: number]: { total: number; count: number } } = {};
    
    comments.forEach(comment => {
      const sentiment = this.analyzeSinglePost(comment.content);
      if (!depthSentiments[comment.depth]) {
        depthSentiments[comment.depth] = { total: 0, count: 0 };
      }
      depthSentiments[comment.depth].total += sentiment.score;
      depthSentiments[comment.depth].count++;
    });

    const depthAnalysis = Object.entries(depthSentiments)
      .map(([depth, data]) => ({
        depth: parseInt(depth),
        avgSentiment: data.total / data.count
      }))
      .sort((a, b) => a.depth - b.depth);

    return {
      overallSentiment,
      controversialTopics: controversialTopics.slice(0, 10),
      consensusTopics: consensusTopics.slice(0, 10),
      depthAnalysis
    };
  }

  private commonWords = new Set([
    'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'this', 'that',
    'with', 'have', 'from', 'they', 'been', 'their', 'what', 'when', 'where',
    'who', 'will', 'more', 'there', 'about', 'after', 'before', 'just', 'only'
  ]);

  identifyEngagementPatterns(tweets: any[]): {
    bestPostingTime: string;
    highEngagementKeywords: string[];
    averageEngagement: number;
  } {
    // Group tweets by hour
    const hourlyEngagement: { [hour: number]: { total: number; count: number } } = {};
    
    tweets.forEach(tweet => {
      const hour = new Date(tweet.timestamp).getHours();
      if (!hourlyEngagement[hour]) {
        hourlyEngagement[hour] = { total: 0, count: 0 };
      }
      
      const engagement = tweet.likes + tweet.retweets + tweet.replies;
      hourlyEngagement[hour].total += engagement;
      hourlyEngagement[hour].count += 1;
    });

    // Find best posting time
    let bestHour = 0;
    let maxAvgEngagement = 0;
    
    Object.entries(hourlyEngagement).forEach(([hour, data]) => {
      const avgEngagement = data.total / data.count;
      if (avgEngagement > maxAvgEngagement) {
        maxAvgEngagement = avgEngagement;
        bestHour = parseInt(hour);
      }
    });

    // Calculate overall average engagement
    const totalEngagement = tweets.reduce((sum, tweet) => 
      sum + tweet.likes + tweet.retweets + tweet.replies, 0
    );
    const averageEngagement = totalEngagement / tweets.length;

    // Find high engagement keywords
    const highEngagementTweets = tweets
      .sort((a, b) => 
        (b.likes + b.retweets + b.replies) - (a.likes + a.retweets + a.replies)
      )
      .slice(0, 10);

    const keywordCounts: { [key: string]: number } = {};
    highEngagementTweets.forEach(tweet => {
      const words = tweet.text.toLowerCase().split(/\s+/);
      words.forEach((word: string) => {
        const cleanWord = word.replace(/[^\w]/g, '');
        if (cleanWord.length > 4) { // Only meaningful words
          keywordCounts[cleanWord] = (keywordCounts[cleanWord] || 0) + 1;
        }
      });
    });

    const highEngagementKeywords = Object.entries(keywordCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([keyword]) => keyword);

    return {
      bestPostingTime: `${bestHour}:00 - ${(bestHour + 1) % 24}:00`,
      highEngagementKeywords,
      averageEngagement
    };
  }

  // Additional methods for handler compatibility
  async getTrendingKeywords(sources: string[] = ['twitter'], limit: number = 10): Promise<string[]> {
    // This would typically connect to Twitter API or analyze recent posts
    // For now, return placeholder trending keywords
    const mockKeywords = [
      'AI', 'machine learning', 'blockchain', 'crypto', 'NFT', 
      'web3', 'metaverse', 'automation', 'cloud', 'SaaS'
    ];
    return mockKeywords.slice(0, limit);
  }

  async analyzeKeywords(keywords: string[], sources: string[] = ['twitter']): Promise<any> {
    // Analyze keyword performance and sentiment
    const analysis = keywords.map(keyword => ({
      keyword,
      mentions: Math.floor(Math.random() * 1000) + 100,
      sentiment: this.analyzeSinglePost(`discussing ${keyword} trending topic`),
      engagementScore: Math.random() * 100,
      sources: sources
    }));
    
    return {
      keywords: analysis,
      totalMentions: analysis.reduce((sum, k) => sum + k.mentions, 0),
      averageSentiment: analysis.reduce((sum, k) => sum + k.sentiment.score, 0) / analysis.length
    };
  }

  async analyzeSubredditTrends(subreddit?: string, timeframe: string = '24h'): Promise<any> {
    // This would typically analyze actual Reddit data
    // For now, return a mock analysis
    return {
      subreddit: subreddit || 'all',
      timeframe,
      topPosts: [],
      trendingKeywords: ['discussion', 'analysis', 'trending', 'popular'],
      sentiment: { overall: 'neutral', score: 0.1 },
      engagement: { avgScore: 150, avgComments: 25 }
    };
  }

  // Reddit comment analysis method for handler compatibility  
  async analyzeRedditComments(postUrl: string): Promise<any> {
    // This would typically fetch and analyze actual Reddit comments
    // For now, return the existing sentiment analysis method
    return {
      postUrl,
      commentCount: 0,
      sentiment: this.analyzeAggregate([]),
      topics: [],
      controversialTopics: [],
      consensusTopics: []
    };
  }
}