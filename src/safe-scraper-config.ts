// Safe Scraping Configuration - IP Protection & Rate Limiting

export interface ScrapingConfig {
  // Rate Limiting
  minDelay: number; // Minimum delay between requests (ms)
  maxDelay: number; // Maximum delay between requests (ms)
  maxConcurrent: number; // Max concurrent requests per domain
  dailyRequestLimit: number; // Max requests per day per target
  hourlyRequestLimit: number; // Max requests per hour per target

  // Retry & Backoff
  maxRetries: number;
  baseBackoffMs: number;
  maxBackoffMs: number;
  jitterFactor: number;

  // Detection Avoidance
  userAgentRotation: boolean;
  randomViewport: boolean;
  humanLikeTiming: boolean;
  sessionPersistence: boolean;

  // Enhanced Stealth Options
  stealthMode: boolean; // Enable puppeteer-extra stealth plugin
  proxyRotation: boolean; // Enable proxy rotation

  // Error Handling
  errorThreshold: number; // Auto-pause after this % of errors
  cooldownPeriod: number; // Cooldown time after errors (ms)
  circuitBreakerThreshold: number; // Failures before circuit opens

  // Monitoring
  logAllRequests: boolean;
  trackResponseTimes: boolean;
  detectBlocking: boolean;

  // Safety Mode
  safetyMode: 'strict' | 'moderate' | 'aggressive';
}

export const SAFE_SCRAPING_CONFIGS: Record<string, ScrapingConfig> = {
  strict: {
    minDelay: 10000, // 10 seconds minimum
    maxDelay: 30000, // 30 seconds maximum
    maxConcurrent: 1, // One request at a time
    dailyRequestLimit: 50, // Very conservative daily limit
    hourlyRequestLimit: 5, // Very conservative hourly limit

    maxRetries: 2,
    baseBackoffMs: 5000,
    maxBackoffMs: 60000,
    jitterFactor: 0.3,

    userAgentRotation: true,
    randomViewport: true,
    humanLikeTiming: true,
    sessionPersistence: true,

    stealthMode: true, // Always use stealth in strict mode
    proxyRotation: true, // Rotate proxies if available

    errorThreshold: 10, // Stop after 10% errors
    cooldownPeriod: 300000, // 5 minute cooldown
    circuitBreakerThreshold: 3,

    logAllRequests: true,
    trackResponseTimes: true,
    detectBlocking: true,

    safetyMode: 'strict',
  },

  moderate: {
    minDelay: 5000, // 5 seconds minimum
    maxDelay: 15000, // 15 seconds maximum
    maxConcurrent: 2, // Two concurrent requests
    dailyRequestLimit: 200, // Moderate daily limit
    hourlyRequestLimit: 20, // Moderate hourly limit

    maxRetries: 3,
    baseBackoffMs: 3000,
    maxBackoffMs: 45000,
    jitterFactor: 0.2,

    userAgentRotation: true,
    randomViewport: true,
    humanLikeTiming: true,
    sessionPersistence: true,

    stealthMode: true, // Use stealth in moderate mode
    proxyRotation: false, // Optional proxy rotation

    errorThreshold: 20, // Stop after 20% errors
    cooldownPeriod: 180000, // 3 minute cooldown
    circuitBreakerThreshold: 5,

    logAllRequests: true,
    trackResponseTimes: true,
    detectBlocking: true,

    safetyMode: 'moderate',
  },

  aggressive: {
    minDelay: 2000, // 2 seconds minimum
    maxDelay: 8000, // 8 seconds maximum
    maxConcurrent: 3, // Three concurrent requests
    dailyRequestLimit: 500, // Higher daily limit
    hourlyRequestLimit: 50, // Higher hourly limit

    maxRetries: 5,
    baseBackoffMs: 2000,
    maxBackoffMs: 30000,
    jitterFactor: 0.1,

    userAgentRotation: true,
    randomViewport: true,
    humanLikeTiming: false, // Less human-like for speed
    sessionPersistence: true,

    stealthMode: false, // Optional stealth in aggressive mode
    proxyRotation: false, // Manual proxy management

    errorThreshold: 30, // Stop after 30% errors
    cooldownPeriod: 120000, // 2 minute cooldown
    circuitBreakerThreshold: 8,

    logAllRequests: false, // Less logging for performance
    trackResponseTimes: true,
    detectBlocking: true,

    safetyMode: 'aggressive',
  },
};

// User Agents for Rotation (Real browser strings)
export const USER_AGENTS = [
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
];

// Viewport Sizes for Rotation
export const VIEWPORT_SIZES = [
  { width: 1920, height: 1080 },
  { width: 1366, height: 768 },
  { width: 1440, height: 900 },
  { width: 1536, height: 864 },
  { width: 1280, height: 720 },
];

// Known blocking response patterns
export const BLOCKING_INDICATORS = [
  'rate limit',
  'too many requests',
  'blocked',
  'captcha',
  'unusual traffic',
  'suspended',
  'temporarily unavailable',
  'access denied',
  'forbidden',
  '429',
  '403',
  '503',
];

export function getRandomDelay(minMs: number, maxMs: number): number {
  return Math.floor(Math.random() * (maxMs - minMs + 1)) + minMs;
}

export function getRandomUserAgent(): string {
  return USER_AGENTS[Math.floor(Math.random() * USER_AGENTS.length)];
}

export function getRandomViewport(): { width: number; height: number } {
  return VIEWPORT_SIZES[Math.floor(Math.random() * VIEWPORT_SIZES.length)];
}

export function addJitter(delayMs: number, jitterFactor: number): number {
  const jitterRange = delayMs * jitterFactor;
  const jitter = (Math.random() - 0.5) * 2 * jitterRange;
  return Math.max(1000, Math.floor(delayMs + jitter)); // Minimum 1 second
}
