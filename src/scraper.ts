import { Page } from 'puppeteer';
import Node<PERSON>ache from 'node-cache';
import { Tweet, Contributor, TrendingTopic } from './types.js';
import { BrowserPoolManager } from './browser-pool-manager.js';

export class XScraper {
  private cache: NodeCache;
  private browserPool: BrowserPoolManager;
  private currentPage: Page | null = null;

  constructor() {
    this.cache = new NodeCache({ stdTTL: 600 }); // Cache for 10 minutes
    this.browserPool = new BrowserPoolManager({
      minBrowsers: 2,
      maxBrowsers: 4,
      maxPagesPerBrowser: 5,
      maxRequestsPerBrowser: 50,
      enableStealth: true
    });
    
    console.log('🐦 X Scraper initialized with browser pool management');
  }

  private async getBrowserAndPage(): Promise<{ browser: any; page: Page; cleanup: () => Promise<void> }> {
    const browserInstance = await this.browserPool.getBrowser();
    const page = await this.browserPool.getPage(browserInstance);
    
    const cleanup = async () => {
      try {
        await page.close();
        await this.browserPool.releaseBrowser(browserInstance);
      } catch (error) {
        console.warn('Error during browser cleanup:', error);
      }
    };
    
    return { browser: browserInstance.browser, page, cleanup };
  }

  async getComments(username: string, limit: number = 20, includeReplies: boolean = true): Promise<Tweet[]> {
    const cacheKey = `comments_${username}_${limit}_${includeReplies}`;
    const cached = this.cache.get<Tweet[]>(cacheKey);
    if (cached) {return cached;}

    const { page, cleanup } = await this.getBrowserAndPage();
    
    try {
      await page.goto(`https://x.com/${username}`, { waitUntil: 'networkidle2' });
      
      // Wait for tweets to load
      await page.waitForSelector('article[data-testid="tweet"]', { timeout: 10000 });

      // Scroll to load more tweets
      await this.autoScroll(page, limit);

      const tweets = await page.evaluate((maxTweets, includeReplies) => {
        const tweetElements = document.querySelectorAll('article[data-testid="tweet"]');
        const tweets: any[] = [];

        for (let i = 0; i < Math.min(tweetElements.length, maxTweets); i++) {
          const element = tweetElements[i];
          
          // Extract tweet text
          const textElement = element.querySelector('[data-testid="tweetText"]');
          const text = textElement ? textElement.textContent || '' : '';
          
          // Skip if it's a reply and we don't want replies
          const isReply = element.querySelector('svg[data-testid="socialContext"]') !== null;
          if (!includeReplies && isReply) {continue;}

          // Extract metadata
          const timeElement = element.querySelector('time');
          const timestamp = timeElement ? timeElement.getAttribute('datetime') || '' : '';
          
          // Extract engagement metrics
          const likes = this.extractMetric(element, 'like');
          const retweets = this.extractMetric(element, 'retweet');
          const replies = this.extractMetric(element, 'reply');

          tweets.push({
            id: `tweet_${i}`,
            text,
            timestamp,
            author: username,
            likes,
            retweets,
            replies,
            isReply
          });
        }

        return tweets;
      }, limit, includeReplies);

      this.cache.set(cacheKey, tweets);
      return tweets;
    } finally {
      await cleanup();
    }
  }

  async getKeyContributors(topic: string, limit: number = 10): Promise<Contributor[]> {
    const cacheKey = `contributors_${topic}_${limit}`;
    const cached = this.cache.get<Contributor[]>(cacheKey);
    if (cached) {return cached;}

    const { browser, page, cleanup } = await this.getBrowserAndPage();
    
    try {
      // Search for the topic
      await page.goto(`https://x.com/search?q=${encodeURIComponent(topic)}&src=typed_query&f=user`, { 
        waitUntil: 'networkidle2' 
      });

      await page.waitForSelector('[data-testid="UserCell"]', { timeout: 10000 });

      const contributors = await page.evaluate((maxContributors) => {
        const userCells = document.querySelectorAll('[data-testid="UserCell"]');
        const contributors: any[] = [];

        for (let i = 0; i < Math.min(userCells.length, maxContributors); i++) {
          const cell = userCells[i];
          
          // Extract username
          const usernameElement = cell.querySelector('a[href^="/"]');
          const username = usernameElement ? 
            usernameElement.getAttribute('href')?.substring(1) || '' : '';
          
          // Extract display name
          const displayNameElement = cell.querySelector('[dir="ltr"] span');
          const displayName = displayNameElement ? displayNameElement.textContent || '' : '';
          
          // Extract bio
          const bioElement = cell.querySelector('[data-testid="UserDescription"]');
          const bio = bioElement ? bioElement.textContent || '' : '';
          
          // Extract follower count
          const followerElement = Array.from(cell.querySelectorAll('span')).find(
            (el: Element) => el.textContent?.includes('followers')
          );
          const followers = followerElement ? 
            parseInt((followerElement as Element).textContent?.replace(/[^0-9]/g, '') || '0') || 0 : 0;

          contributors.push({
            username,
            displayName,
            bio,
            followers,
            relevanceScore: 1 - (i / maxContributors), // Simple relevance based on order
            verifiedStatus: cell.querySelector('[data-testid="icon-verified"]') !== null
          });
        }

        return contributors;
      }, limit);

      this.cache.set(cacheKey, contributors);
      return contributors;
    } finally {
      await cleanup();
    }
  }

  async getTrendingTopics(category?: string, location: string = 'worldwide'): Promise<TrendingTopic[]> {
    const cacheKey = `trends_${category}_${location}`;
    const cached = this.cache.get<TrendingTopic[]>(cacheKey);
    if (cached) {return cached;}

    const { browser, page, cleanup } = await this.getBrowserAndPage();
    
    try {
      await page.goto('https://x.com/explore/tabs/trending', { waitUntil: 'networkidle2' });
      
      await page.waitForSelector('[data-testid="trend"]', { timeout: 10000 });

      const trends = await page.evaluate((filterCategory) => {
        const trendElements = document.querySelectorAll('[data-testid="trend"]');
        const trends: any[] = [];

        trendElements.forEach((element: Element, index: number) => {
          // Extract trend name
          const nameElement = element.querySelector('span[dir="ltr"]');
          const name = nameElement ? nameElement.textContent || '' : '';
          
          // Extract tweet count
          const countElement = Array.from(element.querySelectorAll('span')).find(
            (el: Element) => el.textContent?.includes('posts')
          );
          const tweetCount = countElement ? 
            parseInt((countElement as Element).textContent?.replace(/[^0-9]/g, '') || '0') || 0 : 0;
          
          // Extract category (if visible)
          const categoryElement = element.querySelector('[data-testid="pillLabel"]');
          const trendCategory = categoryElement ? categoryElement.textContent || '' : 'general';
          
          // Filter by category if specified
          if (filterCategory && trendCategory.toLowerCase() !== filterCategory.toLowerCase()) {
            return;
          }

          trends.push({
            name,
            tweetCount,
            category: trendCategory,
            rank: index + 1,
            timestamp: new Date().toISOString()
          });
        });

        return trends;
      }, category);

      this.cache.set(cacheKey, trends);
      return trends;
    } finally {
      await cleanup();
    }
  }

  private async autoScroll(page: Page, maxScrolls: number = 5): Promise<void> {
    await page.evaluate(async (maxScrolls) => {
      await new Promise<void>((resolve) => {
        let totalHeight = 0;
        let scrolls = 0;
        const distance = 100;
        const timer = setInterval(() => {
          const scrollHeight = document.body.scrollHeight;
          window.scrollBy(0, distance);
          totalHeight += distance;
          scrolls++;

          if (totalHeight >= scrollHeight - window.innerHeight || scrolls >= maxScrolls) {
            clearInterval(timer);
            resolve();
          }
        }, 100);
      });
    }, maxScrolls);
  }

  private extractMetric(element: any, type: string): number {
    const metricElement = element.querySelector(`[data-testid="${type}"]`);
    if (!metricElement) {return 0;}
    
    const span = metricElement.querySelector('span');
    if (!span?.textContent) {return 0;}
    
    const text = span.textContent;
    if (text.includes('K')) {
      return parseFloat(text) * 1000;
    } else if (text.includes('M')) {
      return parseFloat(text) * 1000000;
    }
    
    return parseInt(text) || 0;
  }

  async navigate(url: string): Promise<void> {
    const { browser, page, cleanup } = await this.getBrowserAndPage();
    try {
      await page.goto(url, { waitUntil: 'networkidle2' });
      // Store the current page for other methods to use
      this.currentPage = page;
    } catch (error) {
      await cleanup();
      throw error;
    }
    // Note: page stays open for subsequent operations like screenshot, click, etc.
  }

  async screenshot(name: string, selector?: string): Promise<string> {
    if (!this.currentPage) {
      throw new Error('No page available. Please navigate to a URL first.');
    }
    
    const page = this.currentPage;
    const screenshotPath = `screenshots/${name}_${Date.now()}.png`;
    
    if (selector) {
      const element = await page.$(selector);
      if (element) {
        await element.screenshot({ path: screenshotPath });
      } else {
        throw new Error(`Element not found: ${selector}`);
      }
    } else {
      await page.screenshot({ path: screenshotPath, fullPage: true });
    }
    
    return screenshotPath;
  }

  async click(selector: string): Promise<void> {
    if (!this.currentPage) {
      throw new Error('No page available. Please navigate to a URL first.');
    }
    
    const page = this.currentPage;
    await page.waitForSelector(selector, { timeout: 5000 });
    await page.click(selector);
  }

  async fill(selector: string, value: string): Promise<void> {
    if (!this.currentPage) {
      throw new Error('No page available. Please navigate to a URL first.');
    }
    
    const page = this.currentPage;
    await page.waitForSelector(selector, { timeout: 5000 });
    await page.type(selector, value);
  }

  async scroll(direction: 'up' | 'down', amount: number = 500): Promise<void> {
    if (!this.currentPage) {
      throw new Error('No page available. Please navigate to a URL first.');
    }
    
    const page = this.currentPage;
    await page.evaluate((dir, px) => {
      if (dir === 'down') {
        window.scrollBy(0, px);
      } else {
        window.scrollBy(0, -px);
      }
    }, direction, amount);
  }

  // Get browser pool stats
  getStats(): any {
    return this.browserPool.getStats();
  }
  
  getBrowserInfo(): any {
    return this.browserPool.getBrowserInfo();
  }
  
  // Cleanup method
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up X Scraper...');
    
    // Close current page if it exists
    if (this.currentPage) {
      try {
        await this.currentPage.close();
      } catch (error) {
        console.warn('Error closing current page:', error);
      }
      this.currentPage = null;
    }
    
    // Cleanup browser pool
    await this.browserPool.cleanup();
    
    console.log('✅ X Scraper cleanup complete');
  }
}