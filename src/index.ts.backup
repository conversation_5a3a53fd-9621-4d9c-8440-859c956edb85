#!/usr/bin/env node
import 'dotenv/config';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import { XScraper } from './scraper.js';
import { RedditScraper } from './reddit-scraper.js';
import { TrendingAnalyzer } from './analyzer.js';
import { z } from 'zod';
import { getAgentBridge } from './agent-bridge.js';
import { getFabricBridge } from './fabric-bridge.js';
import { TargetManager } from './target-manager.js';
import { AITargetDiscovery } from './ai-discovery.js';
import { ComponentManager } from './component-manager.js';

const scraper = new XScraper();
const redditScraper = new RedditScraper();
const analyzer = new TrendingAnalyzer();
const agentBridge = getAgentBridge();
const fabricBridge = getFabricBridge();
const targetManager = new TargetManager();
const aiDiscovery = new AITargetDiscovery();

// Enhanced Scraping Engine Components - Lazy Loaded
const componentManager = ComponentManager.getInstance();

// Default session management for @chat shorthand
let defaultChatSession: string | null = null;

const GetCommentsSchema = z.object({
  username: z.string().describe('X/Twitter username to scrape comments from'),
  limit: z.number().optional().default(20).describe('Maximum number of comments to retrieve'),
  includeReplies: z.boolean().optional().default(true).describe('Include reply threads')
});

const GetKeyContributorsSchema = z.object({
  topic: z.string().describe('Topic or hashtag to find key contributors for'),
  limit: z.number().optional().default(10).describe('Number of top contributors to return')
});

const GetTrendingTopicsSchema = z.object({
  category: z.string().optional().describe('Category to filter trends (e.g., "tech", "politics", "sports")'),
  location: z.string().optional().default('worldwide').describe('Location for trends')
});

const NavigateSchema = z.object({
  url: z.string().describe('URL to navigate to')
});

const ScreenshotSchema = z.object({
  name: z.string().describe('Name for the screenshot'),
  selector: z.string().optional().describe('CSS selector to capture (captures full page if not provided)')
});

const ClickSchema = z.object({
  selector: z.string().describe('CSS selector of element to click')
});

const FillSchema = z.object({
  selector: z.string().describe('CSS selector of input element'),
  value: z.string().describe('Value to fill in the input')
});

const ScrollSchema = z.object({
  direction: z.enum(['up', 'down']).describe('Direction to scroll'),
  amount: z.number().optional().default(500).describe('Pixels to scroll')
});

// Reddit schemas
const GetSubredditPostsSchema = z.object({
  subreddit: z.string().describe('Name of the subreddit (without r/)'),
  sort: z.enum(['hot', 'new', 'top', 'rising']).optional().default('hot').describe('Sort order for posts'),
  limit: z.number().optional().default(25).describe('Number of posts to retrieve')
});

const GetRedditCommentsSchema = z.object({
  postUrl: z.string().describe('Full URL of the Reddit post'),
  limit: z.number().optional().default(50).describe('Number of comments to retrieve'),
  sortBy: z.enum(['best', 'top', 'new', 'controversial']).optional().default('best')
});

const GetRedditTrendingSchema = z.object({
  category: z.enum(['all', 'gaming', 'sports', 'news', 'entertainment', 'technology']).optional().default('all'),
  limit: z.number().optional().default(10).describe('Number of trending subreddits to return')
});

const SearchRedditSchema = z.object({
  query: z.string().describe('Search query'),
  subreddit: z.string().optional().describe('Specific subreddit to search in'),
  sort: z.enum(['relevance', 'hot', 'top', 'new', 'comments']).optional().default('relevance'),
  timeframe: z.enum(['all', 'year', 'month', 'week', 'day', 'hour']).optional().default('all'),
  limit: z.number().optional().default(25)
});

const AnalyzeRedditTrendsSchema = z.object({
  subreddit: z.string().optional().describe('Analyze trends for a specific subreddit'),
  timeframe: z.enum(['day', 'week', 'month']).optional().default('day')
});

const AnalyzeRedditCommentsSchema = z.object({
  postUrl: z.string().describe('Reddit post URL to analyze comments from')
});

// AI Agent schemas
const CreateChatSessionSchema = z.object({
  // No parameters needed
});

const ChatWithAgentSchema = z.object({
  sessionId: z.string().optional().describe('Chat session ID (will create new if not provided)'),
  message: z.string().describe('Message to send to the AI agent')
});

const ChatShorthandSchema = z.object({
  message: z.string().describe('Your message to the AI agent - simple @chat interface')
});

const GetSessionInfoSchema = z.object({
  sessionId: z.string().describe('Chat session ID')
});

const ExportReportSchema = z.object({
  sessionId: z.string().describe('Chat session ID'),
  format: z.enum(['json', 'markdown', 'html']).optional().default('json')
});

const GetTrendingKeywordsSchema = z.object({
  sources: z.array(z.enum(['twitter', 'reddit', 'both'])).optional().default(['both']),
  limit: z.number().optional().default(10)
});

const AnalyzeKeywordsSchema = z.object({
  keywords: z.array(z.string()).describe('Keywords to analyze'),
  sources: z.array(z.enum(['twitter', 'reddit', 'both'])).optional().default(['both'])
});

// Fabric schemas
const ListFabricPatternsSchema = z.object({
  category: z.string().optional().describe('Filter patterns by category')
});

const ApplyFabricPatternSchema = z.object({
  patternName: z.string().describe('Name of the Fabric pattern to apply'),
  content: z.string().describe('Content to process with the pattern'),
  model: z.string().optional().describe('AI model to use (e.g., gpt-4, claude-3)'),
  temperature: z.number().optional().describe('Temperature for AI generation (0-2)')
});

const ChainFabricPatternsSchema = z.object({
  patterns: z.array(z.string()).describe('Array of pattern names to chain'),
  content: z.string().describe('Initial content to process'),
  model: z.string().optional().describe('AI model to use')
});

const CreateCustomPatternSchema = z.object({
  name: z.string().describe('Name for the custom pattern'),
  systemPrompt: z.string().describe('System prompt for the pattern'),
  description: z.string().describe('Description of what the pattern does')
});

const AnalyzeWithFabricSchema = z.object({
  content: z.string().describe('Content to analyze'),
  analysisType: z.enum(['insights', 'summary', 'claims']).optional().default('insights')
});

const BatchApplyPatternSchema = z.object({
  patternName: z.string().describe('Name of the pattern to apply'),
  contentArray: z.array(z.string()).describe('Array of content items to process'),
  model: z.string().optional().describe('AI model to use')
});

const AnalyzeSentimentSchema = z.object({
  posts: z.array(z.string()).describe('Array of post texts to analyze sentiment'),
  granularity: z.enum(['post', 'aggregate']).optional().default('aggregate')
});

// Target Management Schemas
const ListTargetsSchema = z.object({
  type: z.enum(['twitter', 'reddit', 'both']).optional().default('both').describe('Type of targets to list'),
  category: z.string().optional().describe('Specific category to filter by')
});

const GetTargetCategoriesSchema = z.object({});

const AddInfluencerSchema = z.object({
  category: z.string().describe('Category to add influencer to'),
  username: z.string().describe('Twitter username'),
  followers: z.string().optional().describe('Follower count (e.g., "1M+")'),
  relevance_score: z.number().optional().default(0.8).describe('Relevance score (0-1)'),
  topics: z.array(z.string()).optional().describe('Topics they discuss')
});

const AddSubredditSchema = z.object({
  category: z.string().describe('Category to add subreddit to'),
  name: z.string().describe('Subreddit name (without r/)'),
  subscribers: z.string().optional().describe('Subscriber count (e.g., "500K")'),
  activity_score: z.number().optional().default(0.8).describe('Activity score (0-1)')
});

const RemoveTargetSchema = z.object({
  type: z.enum(['twitter', 'reddit']).describe('Type of target to remove'),
  category: z.string().describe('Category the target is in'),
  identifier: z.string().describe('Username (Twitter) or subreddit name (Reddit)')
});

const GetTopTargetsSchema = z.object({
  limit: z.number().optional().default(10).describe('Number of top targets to return')
});

const BatchAnalyzeTargetsSchema = z.object({
  twitter_categories: z.array(z.string()).optional().default([]).describe('Twitter categories to analyze'),
  reddit_categories: z.array(z.string()).optional().default([]).describe('Reddit categories to analyze'),
  analysis_type: z.enum(['sentiment', 'trending', 'full']).optional().default('full').describe('Type of analysis to perform')
});

const DiscoverNewTargetsSchema = z.object({
  category: z.string().describe('Category to discover targets for'),
  keywords: z.array(z.string()).optional().describe('Keywords to search for (uses defaults if not provided)'),
  limit: z.number().optional().default(5).describe('Maximum number of new targets to discover')
});

const GetRecentDiscoveriesSchema = z.object({});

const PromoteDiscoveredTargetSchema = z.object({
  type: z.enum(['twitter', 'reddit']).describe('Type of target to promote'),
  category: z.string().describe('Category the discovered target is in'),
  identifier: z.string().describe('Username (Twitter) or subreddit name (Reddit) to promote')
});

const UpdateTargetRelevanceSchema = z.object({
  type: z.enum(['twitter', 'reddit']).describe('Type of target to update'),
  category: z.string().describe('Category the target is in'),
  identifier: z.string().describe('Username (Twitter) or subreddit name (Reddit) to update'),
  check_current_metrics: z.boolean().optional().default(true).describe('Whether to fetch current metrics for scoring')
});

// Enhanced Scraping Engine Schemas
const GetSystemHealthSchema = z.object({
  includeMetrics: z.boolean().optional().default(true).describe('Include detailed metrics in response')
});

const GetKPISnapshotSchema = z.object({
  domain: z.string().optional().describe('Get KPIs for specific domain'),
  timeRange: z.enum(['hour', 'day', 'week', 'month']).optional().default('day').describe('Time range for metrics')
});

const AnalyzePageVisuallySchema = z.object({
  url: z.string().describe('URL to analyze with visual scraping'),
  options: z.object({
    detectBlocking: z.boolean().optional().default(true),
    detectCaptcha: z.boolean().optional().default(true),
    extractText: z.boolean().optional().default(true)
  }).optional()
});

const GetBlockingStatsSchema = z.object({
  domain: z.string().optional().describe('Get blocking stats for specific domain')
});

const GetEnhancementStatsSchema = z.object({});

const GenerateEnhancementProposalSchema = z.object({});

const GetResearchSummarySchema = z.object({
  days: z.number().optional().default(30).describe('Number of days to analyze')
});

const SendNotificationSchema = z.object({
  type: z.enum(['health_alert', 'performance_degradation', 'custom']).describe('Type of notification'),
  severity: z.enum(['info', 'warning', 'error', 'critical']).describe('Severity level'),
  title: z.string().describe('Notification title'),
  message: z.string().describe('Notification message'),
  domain: z.string().optional().describe('Related domain if applicable')
});

const GetDashboardDataSchema = z.object({});

const TestNotificationChannelSchema = z.object({
  channelName: z.string().describe('Name of the notification channel to test')
});

const server = new Server(
  {
    name: 'x-scraper',
    version: '0.1.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

server.setRequestHandler(ListToolsRequestSchema, async () => ({
  tools: [
    {
      name: 'get_comments',
      description: 'Scrape comments from a specific X/Twitter user',
      inputSchema: GetCommentsSchema,
    },
    {
      name: 'get_key_contributors',
      description: 'Find key contributors for a specific topic or hashtag',
      inputSchema: GetKeyContributorsSchema,
    },
    {
      name: 'get_trending_topics',
      description: 'Get current trending topics with optional category filter',
      inputSchema: GetTrendingTopicsSchema,
    },
    {
      name: 'analyze_sentiment',
      description: 'Analyze sentiment of posts or comments',
      inputSchema: AnalyzeSentimentSchema,
    },
    {
      name: 'navigate',
      description: 'Navigate browser to a specific URL',
      inputSchema: NavigateSchema,
    },
    {
      name: 'screenshot',
      description: 'Take a screenshot of the current page or specific element',
      inputSchema: ScreenshotSchema,
    },
    {
      name: 'click',
      description: 'Click on an element using CSS selector',
      inputSchema: ClickSchema,
    },
    {
      name: 'fill',
      description: 'Fill an input field with text',
      inputSchema: FillSchema,
    },
    {
      name: 'scroll',
      description: 'Scroll the page up or down',
      inputSchema: ScrollSchema,
    },
    // Reddit tools
    {
      name: 'get_subreddit_posts',
      description: 'Get posts from a specific subreddit',
      inputSchema: GetSubredditPostsSchema,
    },
    {
      name: 'get_reddit_comments',
      description: 'Get comments from a Reddit post',
      inputSchema: GetRedditCommentsSchema,
    },
    {
      name: 'get_reddit_trending',
      description: 'Get trending subreddits and topics',
      inputSchema: GetRedditTrendingSchema,
    },
    {
      name: 'search_reddit',
      description: 'Search Reddit posts across all subreddits or within a specific one',
      inputSchema: SearchRedditSchema,
    },
    {
      name: 'analyze_reddit_trends',
      description: 'Analyze trending patterns and metrics from Reddit posts',
      inputSchema: AnalyzeRedditTrendsSchema,
    },
    {
      name: 'analyze_reddit_comments',
      description: 'Analyze sentiment and patterns in Reddit comment threads',
      inputSchema: AnalyzeRedditCommentsSchema,
    },
    // AI Agent tools
    {
      name: 'create_chat_session',
      description: 'Create a new AI chat session for analysis and reporting',
      inputSchema: CreateChatSessionSchema,
    },
    {
      name: 'chat_with_agent',
      description: 'Send a message to the AI agent for analysis, questions, or report generation',
      inputSchema: ChatWithAgentSchema,
    },
    {
      name: 'chat',
      description: 'Simple chat interface - just use @chat followed by your message',
      inputSchema: ChatShorthandSchema,
    },
    {
      name: 'ai',
      description: 'Direct AI chat - alternative to @chat command',
      inputSchema: ChatShorthandSchema,
    },
    {
      name: 'ask',
      description: 'Ask the AI a question - simple interface',
      inputSchema: ChatShorthandSchema,
    },
    {
      name: 'talk',
      description: 'Talk to the AI assistant directly',
      inputSchema: ChatShorthandSchema,
    },
    {
      name: 'get_session_info',
      description: 'Get information about a chat session',
      inputSchema: GetSessionInfoSchema,
    },
    {
      name: 'export_report',
      description: 'Export generated report in various formats',
      inputSchema: ExportReportSchema,
    },
    {
      name: 'get_trending_keywords',
      description: 'Get trending keywords discovered by the AI agent',
      inputSchema: GetTrendingKeywordsSchema,
    },
    {
      name: 'analyze_keywords_with_agent',
      description: 'Analyze keywords using the AI agent\'s advanced capabilities',
      inputSchema: AnalyzeKeywordsSchema,
    },
    // Fabric tools
    {
      name: 'list_fabric_patterns',
      description: 'List all available Fabric patterns with optional category filter',
      inputSchema: ListFabricPatternsSchema,
    },
    {
      name: 'apply_fabric_pattern',
      description: 'Apply a specific Fabric pattern to content',
      inputSchema: ApplyFabricPatternSchema,
    },
    {
      name: 'chain_fabric_patterns',
      description: 'Chain multiple Fabric patterns for complex analysis',
      inputSchema: ChainFabricPatternsSchema,
    },
    {
      name: 'create_custom_pattern',
      description: 'Create a custom Fabric pattern',
      inputSchema: CreateCustomPatternSchema,
    },
    {
      name: 'analyze_with_fabric',
      description: 'Intelligently analyze content using best-fit Fabric patterns',
      inputSchema: AnalyzeWithFabricSchema,
    },
    {
      name: 'batch_apply_pattern',
      description: 'Apply a pattern to multiple content items in batch',
      inputSchema: BatchApplyPatternSchema,
    },
    // Target Management Tools
    {
      name: 'list_targets',
      description: 'List current Twitter influencers and Reddit subreddits by category',
      inputSchema: ListTargetsSchema,
    },
    {
      name: 'get_target_categories',
      description: 'Get all available target categories with descriptions',
      inputSchema: GetTargetCategoriesSchema,
    },
    {
      name: 'add_influencer',
      description: 'Add a new Twitter influencer to a category',
      inputSchema: AddInfluencerSchema,
    },
    {
      name: 'add_subreddit',
      description: 'Add a new Reddit subreddit to a category',
      inputSchema: AddSubredditSchema,
    },
    {
      name: 'remove_target',
      description: 'Remove an influencer or subreddit from tracking',
      inputSchema: RemoveTargetSchema,
    },
    {
      name: 'get_top_targets',
      description: 'Get top-ranked influencers and subreddits by relevance/activity',
      inputSchema: GetTopTargetsSchema,
    },
    {
      name: 'batch_analyze_targets',
      description: 'Analyze multiple categories of targets in batch',
      inputSchema: BatchAnalyzeTargetsSchema,
    },
    {
      name: 'discover_new_targets',
      description: 'AI-powered discovery of new influencers and subreddits',
      inputSchema: DiscoverNewTargetsSchema,
    },
    {
      name: 'get_recent_discoveries',
      description: 'Get recently discovered targets awaiting approval',
      inputSchema: GetRecentDiscoveriesSchema,
    },
    {
      name: 'promote_discovered_target',
      description: 'Promote a discovered target from recent discoveries to main target list',
      inputSchema: PromoteDiscoveredTargetSchema,
    },
    {
      name: 'update_target_relevance',
      description: 'Update relevance scores of existing targets with current metrics',
      inputSchema: UpdateTargetRelevanceSchema,
    },
    // Enhanced Scraping Engine Tools
    {
      name: 'get_system_health',
      description: 'Get comprehensive system health metrics and status',
      inputSchema: GetSystemHealthSchema,
    },
    {
      name: 'get_kpi_snapshot',
      description: 'Get current KPI metrics and performance data',
      inputSchema: GetKPISnapshotSchema,
    },
    {
      name: 'analyze_page_visually',
      description: 'Analyze a webpage using visual scraping with OCR and blocking detection',
      inputSchema: AnalyzePageVisuallySchema,
    },
    {
      name: 'get_blocking_stats',
      description: 'Get statistics on blocking events and mitigation effectiveness',
      inputSchema: GetBlockingStatsSchema,
    },
    {
      name: 'get_enhancement_stats',
      description: 'Get statistics on automated enhancements and success rates',
      inputSchema: GetEnhancementStatsSchema,
    },
    {
      name: 'generate_enhancement_proposal',
      description: 'Generate AI-powered enhancement proposal for system improvements',
      inputSchema: GenerateEnhancementProposalSchema,
    },
    {
      name: 'get_research_summary',
      description: 'Get research agent findings and technology trends summary',
      inputSchema: GetResearchSummarySchema,
    },
    {
      name: 'send_notification',
      description: 'Send notification through configured communication channels',
      inputSchema: SendNotificationSchema,
    },
    {
      name: 'get_dashboard_data',
      description: 'Get real-time dashboard data for system monitoring',
      inputSchema: GetDashboardDataSchema,
    },
    {
      name: 'test_notification_channel',
      description: 'Test a notification channel configuration',
      inputSchema: TestNotificationChannelSchema,
    }
  ],
}));

server.setRequestHandler(CallToolRequestSchema, async (request) => {
  try {
    const { name, arguments: args } = request.params;

    switch (name) {
      case 'get_comments': {
        const params = GetCommentsSchema.parse(args);
        const comments = await scraper.getComments(
          params.username,
          params.limit,
          params.includeReplies
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(comments, null, 2),
            },
          ],
        };
      }

      case 'get_key_contributors': {
        const params = GetKeyContributorsSchema.parse(args);
        const contributors = await scraper.getKeyContributors(
          params.topic,
          params.limit
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(contributors, null, 2),
            },
          ],
        };
      }

      case 'get_trending_topics': {
        const params = GetTrendingTopicsSchema.parse(args);
        const trends = await scraper.getTrendingTopics(
          params.category,
          params.location
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(trends, null, 2),
            },
          ],
        };
      }

      case 'analyze_sentiment': {
        const params = AnalyzeSentimentSchema.parse(args);
        const analysis = await analyzer.analyzeSentiment(
          params.posts,
          params.granularity
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(analysis, null, 2),
            },
          ],
        };
      }

      case 'navigate': {
        const params = NavigateSchema.parse(args);
        const result = await scraper.navigate(params.url);
        return {
          content: [
            {
              type: 'text',
              text: `Navigated to ${params.url}`,
            },
          ],
        };
      }

      case 'screenshot': {
        const params = ScreenshotSchema.parse(args);
        const screenshotPath = await scraper.screenshot(params.name, params.selector);
        return {
          content: [
            {
              type: 'text',
              text: `Screenshot saved to ${screenshotPath}`,
            },
          ],
        };
      }

      case 'click': {
        const params = ClickSchema.parse(args);
        await scraper.click(params.selector);
        return {
          content: [
            {
              type: 'text',
              text: `Clicked element: ${params.selector}`,
            },
          ],
        };
      }

      case 'fill': {
        const params = FillSchema.parse(args);
        await scraper.fill(params.selector, params.value);
        return {
          content: [
            {
              type: 'text',
              text: `Filled ${params.selector} with value`,
            },
          ],
        };
      }

      case 'scroll': {
        const params = ScrollSchema.parse(args);
        await scraper.scroll(params.direction, params.amount);
        return {
          content: [
            {
              type: 'text',
              text: `Scrolled ${params.direction} by ${params.amount}px`,
            },
          ],
        };
      }

      // Reddit cases
      case 'get_subreddit_posts': {
        const params = GetSubredditPostsSchema.parse(args);
        const posts = await redditScraper.getSubredditPosts(
          params.subreddit,
          params.sort,
          params.limit
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(posts, null, 2),
            },
          ],
        };
      }

      case 'get_reddit_comments': {
        const params = GetRedditCommentsSchema.parse(args);
        const comments = await redditScraper.getPostComments(
          params.postUrl,
          params.limit,
          params.sortBy
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(comments, null, 2),
            },
          ],
        };
      }

      case 'get_reddit_trending': {
        const params = GetRedditTrendingSchema.parse(args);
        const trending = await redditScraper.getTrendingSubreddits(
          params.category,
          params.limit
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(trending, null, 2),
            },
          ],
        };
      }

      case 'search_reddit': {
        const params = SearchRedditSchema.parse(args);
        const results = await redditScraper.searchReddit(
          params.query,
          params.subreddit,
          params.sort,
          params.timeframe,
          params.limit
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(results, null, 2),
            },
          ],
        };
      }

      case 'analyze_reddit_trends': {
        const params = AnalyzeRedditTrendsSchema.parse(args);
        let posts;
        
        if (params.subreddit) {
          posts = await redditScraper.getSubredditPosts(
            params.subreddit,
            'top',
            50
          );
        } else {
          posts = await redditScraper.searchReddit(
            '*',
            undefined,
            'top',
            params.timeframe,
            50
          );
        }
        
        const analysis = analyzer.analyzeRedditTrends(posts);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(analysis, null, 2),
            },
          ],
        };
      }

      case 'analyze_reddit_comments': {
        const params = AnalyzeRedditCommentsSchema.parse(args);
        const comments = await redditScraper.getPostComments(
          params.postUrl,
          100,
          'best'
        );
        
        const analysis = analyzer.analyzeRedditCommentSentiment(comments);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(analysis, null, 2),
            },
          ],
        };
      }

      // AI Agent cases
      case 'create_chat_session': {
        const sessionId = await agentBridge.createChatSession();
        return {
          content: [
            {
              type: 'text',
              text: `Created new chat session: ${sessionId}`,
            },
          ],
        };
      }

      case 'chat_with_agent': {
        const params = ChatWithAgentSchema.parse(args);
        let sessionId = params.sessionId;
        
        // Create session if not provided
        if (!sessionId) {
          sessionId = await agentBridge.createChatSession();
        }
        
        const response = await agentBridge.chat(sessionId, params.message);
        
        return {
          content: [
            {
              type: 'text',
              text: `**Session:** ${response.session_id}\n\n**Response:** ${response.response}\n\n**Metadata:** ${JSON.stringify(response.metadata, null, 2)}`,
            },
          ],
        };
      }

      case 'chat':
      case 'ai':
      case 'ask':
      case 'talk': {
        const params = ChatShorthandSchema.parse(args);
        
        try {
          // Simple OpenAI integration bypassing complex Python validation
          const openaiApiKey = process.env.OPENAI_API_KEY;
          if (!openaiApiKey) {
            return {
              content: [
                {
                  type: 'text',
                  text: 'Error: OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.',
                },
              ],
            };
          }

          const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${openaiApiKey}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: 'gpt-4',
              messages: [
                {
                  role: 'system',
                  content: 'You are a helpful AI assistant integrated into an MCP server for social media analysis. Provide concise, helpful responses.'
                },
                {
                  role: 'user',
                  content: params.message
                }
              ],
              max_tokens: 1000,
              temperature: 0.7
            })
          });

          if (!response.ok) {
            const error = await response.text();
            return {
              content: [
                {
                  type: 'text',
                  text: `OpenAI API Error: ${response.status} - ${error}`,
                },
              ],
            };
          }

          const data = await response.json();
          const aiResponse = data.choices?.[0]?.message?.content || 'No response generated';
          
          return {
            content: [
              {
                type: 'text',
                text: aiResponse,
              },
            ],
          };
        } catch (error) {
          return {
            content: [
              {
                type: 'text',
                text: `Chat Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
              },
            ],
          };
        }
      }

      case 'get_session_info': {
        const params = GetSessionInfoSchema.parse(args);
        const info = await agentBridge.getSessionInfo(params.sessionId);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(info, null, 2),
            },
          ],
        };
      }

      case 'export_report': {
        const params = ExportReportSchema.parse(args);
        const report = await agentBridge.exportReport(params.sessionId, params.format);
        
        return {
          content: [
            {
              type: 'text',
              text: params.format === 'json' ? report : `\`\`\`${params.format}\n${report}\n\`\`\``,
            },
          ],
        };
      }

      case 'get_trending_keywords': {
        const params = GetTrendingKeywordsSchema.parse(args);
        const trending = await agentBridge.getTrendingKeywords(params.sources, params.limit);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(trending, null, 2),
            },
          ],
        };
      }

      case 'analyze_keywords_with_agent': {
        const params = AnalyzeKeywordsSchema.parse(args);
        const analysis = await agentBridge.analyzeKeywords(params.keywords, params.sources);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(analysis, null, 2),
            },
          ],
        };
      }

      // Fabric cases
      case 'list_fabric_patterns': {
        const params = ListFabricPatternsSchema.parse(args);
        const patterns = await fabricBridge.listPatterns(params.category);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(patterns, null, 2),
            },
          ],
        };
      }

      case 'apply_fabric_pattern': {
        const params = ApplyFabricPatternSchema.parse(args);
        const result = await fabricBridge.applyPattern(
          params.patternName,
          params.content,
          params.model,
          params.temperature
        );
        
        return {
          content: [
            {
              type: 'text',
              text: result.success ? 
                `**Pattern:** ${result.pattern}\n**Execution Time:** ${result.executionTime}ms\n\n**Result:**\n${result.output}` :
                `**Error applying pattern ${result.pattern}:**\n${result.error}`,
            },
          ],
        };
      }

      case 'chain_fabric_patterns': {
        const params = ChainFabricPatternsSchema.parse(args);
        const results = await fabricBridge.chainPatterns(
          params.patterns,
          params.content,
          params.model
        );
        
        let output = `**Pattern Chain Results:**\n\n`;
        results.forEach((result, index) => {
          output += `**Step ${index + 1}: ${result.pattern}**\n`;
          output += result.success ? 
            `✅ Success (${result.executionTime}ms)\n${result.output}\n\n` :
            `❌ Failed: ${result.error}\n\n`;
        });
        
        return {
          content: [
            {
              type: 'text',
              text: output,
            },
          ],
        };
      }

      case 'create_custom_pattern': {
        const params = CreateCustomPatternSchema.parse(args);
        const success = await fabricBridge.createCustomPattern(
          params.name,
          params.systemPrompt,
          params.description
        );
        
        return {
          content: [
            {
              type: 'text',
              text: success ? 
                `✅ Successfully created custom pattern: ${params.name}` :
                `❌ Failed to create custom pattern: ${params.name}`,
            },
          ],
        };
      }

      case 'analyze_with_fabric': {
        const params = AnalyzeWithFabricSchema.parse(args);
        let result;
        
        switch (params.analysisType) {
          case 'insights':
            result = await fabricBridge.extractInsights(params.content);
            break;
          case 'summary':
            result = await fabricBridge.summarizeContent(params.content);
            break;
          case 'claims':
            result = await fabricBridge.analyzeContent(params.content);
            break;
          default:
            result = await fabricBridge.analyzeContent(params.content);
        }
        
        return {
          content: [
            {
              type: 'text',
              text: result.success ? 
                `**${params.analysisType.toUpperCase()} Analysis:**\n\n${result.output}` :
                `**Error:** ${result.error}`,
            },
          ],
        };
      }

      case 'batch_apply_pattern': {
        const params = BatchApplyPatternSchema.parse(args);
        const results = await fabricBridge.applyPatternToMultipleInputs(
          params.patternName,
          params.contentArray,
          params.model
        );
        
        let output = `**Batch Processing Results for ${params.patternName}:**\n\n`;
        results.forEach((result, index) => {
          output += `**Item ${index + 1}:**\n`;
          output += result.success ? 
            `✅ Success (${result.executionTime}ms)\n${result.output?.substring(0, 200)}...\n\n` :
            `❌ Failed: ${result.error}\n\n`;
        });
        
        const successCount = results.filter(r => r.success).length;
        output += `**Summary:** ${successCount}/${results.length} items processed successfully`;
        
        return {
          content: [
            {
              type: 'text',
              text: output,
            },
          ],
        };
      }

      // Target Management Tool Handlers
      case 'list_targets': {
        const params = ListTargetsSchema.parse(args);
        const result: any = {};
        
        if (params.type === 'twitter' || params.type === 'both') {
          result.twitter_influencers = targetManager.getInfluencers(params.category);
        }
        
        if (params.type === 'reddit' || params.type === 'both') {
          result.reddit_subreddits = targetManager.getSubreddits(params.category);
        }
        
        result.stats = targetManager.getStats();
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
        };
      }

      case 'get_target_categories': {
        const categories = targetManager.getCategories();
        const stats = targetManager.getStats();
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                categories,
                stats,
                last_updated: stats.last_updated
              }, null, 2),
            },
          ],
        };
      }

      case 'add_influencer': {
        const params = AddInfluencerSchema.parse(args);
        
        const influencer = {
          username: params.username,
          followers: params.followers || 'Unknown',
          relevance_score: params.relevance_score,
          last_verified: new Date().toISOString().split('T')[0],
          topics: params.topics
        };
        
        targetManager.addInfluencer(params.category, influencer);
        
        return {
          content: [
            {
              type: 'text',
              text: `✅ Added influencer @${params.username} to category '${params.category}'`,
            },
          ],
        };
      }

      case 'add_subreddit': {
        const params = AddSubredditSchema.parse(args);
        
        const subreddit = {
          name: params.name,
          subscribers: params.subscribers || 'Unknown',
          activity_score: params.activity_score,
          last_verified: new Date().toISOString().split('T')[0]
        };
        
        targetManager.addSubreddit(params.category, subreddit);
        
        return {
          content: [
            {
              type: 'text',
              text: `✅ Added subreddit r/${params.name} to category '${params.category}'`,
            },
          ],
        };
      }

      case 'remove_target': {
        const params = RemoveTargetSchema.parse(args);
        
        let success = false;
        if (params.type === 'twitter') {
          success = targetManager.removeInfluencer(params.category, params.identifier);
        } else {
          success = targetManager.removeSubreddit(params.category, params.identifier);
        }
        
        const message = success 
          ? `✅ Removed ${params.type} target '${params.identifier}' from category '${params.category}'`
          : `❌ Target '${params.identifier}' not found in category '${params.category}'`;
          
        return {
          content: [
            {
              type: 'text',
              text: message,
            },
          ],
        };
      }

      case 'get_top_targets': {
        const params = GetTopTargetsSchema.parse(args);
        const topTargets = targetManager.getTopTargets(params.limit);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(topTargets, null, 2),
            },
          ],
        };
      }

      case 'batch_analyze_targets': {
        const params = BatchAnalyzeTargetsSchema.parse(args);
        const targets = targetManager.getBatchTargets(params.twitter_categories, params.reddit_categories);
        
        let analysis = `**Batch Target Analysis**\n\n`;
        
        // Analyze Twitter targets
        if (targets.twitter && Object.keys(targets.twitter).length > 0) {
          analysis += `## Twitter Influencers Analysis\n`;
          for (const [category, influencers] of Object.entries(targets.twitter)) {
            analysis += `\n### ${category} (${influencers.length} influencers)\n`;
            for (const influencer of influencers.slice(0, 5)) {
              analysis += `- @${influencer.username} (Score: ${influencer.relevance_score})\n`;
            }
          }
        }
        
        // Analyze Reddit targets  
        if (targets.reddit && Object.keys(targets.reddit).length > 0) {
          analysis += `\n## Reddit Communities Analysis\n`;
          for (const [category, subreddits] of Object.entries(targets.reddit)) {
            analysis += `\n### ${category} (${subreddits.length} subreddits)\n`;
            for (const subreddit of subreddits.slice(0, 5)) {
              analysis += `- r/${subreddit.name} (Score: ${subreddit.activity_score})\n`;
            }
          }
        }
        
        return {
          content: [
            {
              type: 'text',
              text: analysis,
            },
          ],
        };
      }

      case 'discover_new_targets': {
        const params = DiscoverNewTargetsSchema.parse(args);
        
        // Use discovery keywords or provided keywords
        const allKeywords = targetManager.getDiscoveryKeywords();
        const keywords = params.keywords || allKeywords[params.category] || [];
        
        if (keywords.length === 0) {
          return {
            content: [
              {
                type: 'text',
                text: `❌ No discovery keywords available for category '${params.category}'. Please provide keywords or add them to the configuration.`,
              },
            ],
          };
        }
        
        try {
          // Run AI discovery for both Twitter and Reddit
          const [twitterResult, redditResult] = await Promise.all([
            aiDiscovery.discoverTwitterInfluencers(params.category, keywords, Math.ceil(params.limit / 2)),
            aiDiscovery.discoverRedditCommunities(params.category, keywords, Math.ceil(params.limit / 2))
          ]);
          
          // Add discovered targets to recent discoveries
          let twitterMessage = '';
          let redditMessage = '';
          
          if (twitterResult.targets.length > 0) {
            const addResult = await aiDiscovery.addDiscoveredTargetsToConfig(twitterResult);
            twitterMessage = `**Twitter Discovery:**\n${addResult}\n\nFound targets:\n` +
              twitterResult.targets.map(t => `- @${t.identifier} (Score: ${t.relevance_score.toFixed(2)})`).join('\n');
          }
          
          if (redditResult.targets.length > 0) {
            const addResult = await aiDiscovery.addDiscoveredTargetsToConfig(redditResult);
            redditMessage = `\n\n**Reddit Discovery:**\n${addResult}\n\nFound targets:\n` +
              redditResult.targets.map(t => `- r/${t.identifier} (Score: ${t.relevance_score.toFixed(2)})`).join('\n');
          }
          
          const discoveryMessage = `🔍 **AI-Powered Target Discovery Results**\n\n` +
            `Category: ${params.category}\n` +
            `Keywords: ${keywords.join(', ')}\n` +
            `Discovery completed at: ${new Date().toLocaleString()}\n\n` +
            twitterMessage + redditMessage +
            `\n\n📋 **Next Steps:**\n` +
            `1. Review discovered targets with 'get_recent_discoveries'\n` +
            `2. Use target management tools to add approved targets to main lists\n` +
            `3. Remove any irrelevant discoveries`;
          
          return {
            content: [
              {
                type: 'text',
                text: discoveryMessage,
              },
            ],
          };
          
        } catch (error) {
          return {
            content: [
              {
                type: 'text',
                text: `❌ Discovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
              },
            ],
          };
        }
      }

      case 'get_recent_discoveries': {
        const discoveries = targetManager.getRecentDiscoveries();
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(discoveries, null, 2),
            },
          ],
        };
      }

      case 'promote_discovered_target': {
        const params = PromoteDiscoveredTargetSchema.parse(args);
        
        const success = targetManager.promoteDiscoveredTarget(
          params.type,
          params.category,
          params.identifier
        );
        
        const message = success 
          ? `✅ Promoted ${params.type} target '${params.identifier}' from discoveries to main ${params.category} list`
          : `❌ Target '${params.identifier}' not found in recent discoveries for category '${params.category}'`;
          
        return {
          content: [
            {
              type: 'text',
              text: message,
            },
          ],
        };
      }

      case 'update_target_relevance': {
        const params = UpdateTargetRelevanceSchema.parse(args);
        
        try {
          let updateMessage = `🔄 **Updating relevance for ${params.type} target: ${params.identifier}**\n\n`;
          
          if (params.check_current_metrics) {
            if (params.type === 'twitter') {
              // Get current follower metrics and engagement
              const contributors = await scraper.getKeyContributors(params.identifier, 1);
              const matchingContributor = contributors.find(c => 
                c.username.toLowerCase() === params.identifier.toLowerCase()
              );
              
              if (matchingContributor) {
                const currentInfluencer = {
                  username: params.identifier,
                  followers: matchingContributor.followers ? `${matchingContributor.followers}+` : 'Unknown',
                  relevance_score: matchingContributor.followers > 1000000 ? 0.95 : 
                                 matchingContributor.followers > 100000 ? 0.85 : 0.75,
                  last_verified: new Date().toISOString().split('T')[0],
                  topics: [params.category]
                };
                
                targetManager.addInfluencer(params.category, currentInfluencer);
                updateMessage += `✅ Updated Twitter influencer with current metrics\n`;
                updateMessage += `- Followers: ${currentInfluencer.followers}\n`;
                updateMessage += `- New relevance score: ${currentInfluencer.relevance_score}\n`;
              } else {
                updateMessage += `⚠️  Could not fetch current metrics for @${params.identifier}\n`;
              }
            } else {
              // Update Reddit subreddit metrics
              const posts = await redditScraper.getSubredditPosts(params.identifier, 'hot', 25);
              
              if (posts.length > 0) {
                const totalUpvotes = posts.reduce((sum, post) => sum + (post.score || 0), 0);
                const avgUpvotes = Math.round(totalUpvotes / posts.length);
                const activityScore = avgUpvotes > 500 ? 0.95 : avgUpvotes > 100 ? 0.85 : 0.75;
                
                const currentSubreddit = {
                  name: params.identifier,
                  subscribers: `${posts.length} recent posts`,
                  activity_score: activityScore,
                  last_verified: new Date().toISOString().split('T')[0]
                };
                
                targetManager.addSubreddit(params.category, currentSubreddit);
                updateMessage += `✅ Updated Reddit subreddit with current metrics\n`;
                updateMessage += `- Recent posts: ${posts.length}\n`;
                updateMessage += `- Average upvotes: ${avgUpvotes}\n`;
                updateMessage += `- New activity score: ${activityScore}\n`;
              } else {
                updateMessage += `⚠️  Could not fetch current metrics for r/${params.identifier}\n`;
              }
            }
          } else {
            updateMessage += `📝 Relevance update requested without metric check\n`;
            updateMessage += `Use 'check_current_metrics: true' to fetch latest data\n`;
          }
          
          updateMessage += `\n🕒 Last updated: ${new Date().toLocaleString()}`;
          
          return {
            content: [
              {
                type: 'text',
                text: updateMessage,
              },
            ],
          };
          
        } catch (error) {
          return {
            content: [
              {
                type: 'text',
                text: `❌ Update failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
              },
            ],
          };
        }
      }

      // Enhanced Scraping Engine Tool Handlers
      case 'get_system_health': {
        const params = GetSystemHealthSchema.parse(args);
        const healthReporter = await componentManager.getHealthReporter();
        const systemHealth = await healthReporter.calculateSystemHealth();
        const dashboardData = healthReporter.getDashboardData();
        
        const response = {
          systemHealth,
          dashboardData: params.includeMetrics ? dashboardData : undefined,
          componentStats: componentManager.getComponentStats(),
          initializedComponents: componentManager.getInitializedComponents(),
          timestamp: Date.now()
        };
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(response, null, 2),
            },
          ],
        };
      }

      case 'get_kpi_snapshot': {
        const params = GetKPISnapshotSchema.parse(args);
        const kpiMonitor = await componentManager.getKPIMonitor();
        const snapshot = kpiMonitor.getCurrentSnapshot();
        const domainSnapshot = params.domain ? null : null; // Domain-specific metrics would be implemented separately
        
        const response = {
          overall: snapshot,
          domain: domainSnapshot,
          timeRange: params.timeRange,
          timestamp: Date.now()
        };
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(response, null, 2),
            },
          ],
        };
      }

      case 'analyze_page_visually': {
        const params = AnalyzePageVisuallySchema.parse(args);
        
        // Navigate to the URL first
        await scraper.navigate(params.url);
        const page = (scraper as any).page; // Access the page instance
        
        if (!page) {
          return {
            content: [
              {
                type: 'text',
                text: 'Error: Browser page not available. Please navigate to a page first.',
              },
            ],
          };
        }
        
        // Lazy load visual engine
        const visualEngine = await componentManager.getVisualEngine();
        
        // Initialize OCR if needed
        if (!visualEngine.getStats().initialized) {
          await visualEngine.initialize();
        }
        
        const analysis = await visualEngine.analyzeScreenshot(page, {
          name: `visual-analysis-${Date.now()}`,
          detectBlocking: params.options?.detectBlocking ?? true,
          detectCaptcha: params.options?.detectCaptcha ?? true,
          extractText: params.options?.extractText ?? true
        });
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                url: params.url,
                analysis,
                timestamp: Date.now()
              }, null, 2),
            },
          ],
        };
      }

      case 'get_blocking_stats': {
        const params = GetBlockingStatsSchema.parse(args);
        const blockAnalyzer = await componentManager.getBlockAnalyzer();
        const stats = blockAnalyzer.getBlockingStats();
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                domain: params.domain || 'all',
                stats,
                timestamp: Date.now()
              }, null, 2),
            },
          ],
        };
      }

      case 'get_enhancement_stats': {
        const enhancementEngine = await componentManager.getEnhancementEngine();
        const stats = enhancementEngine.getEnhancementStats();
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                enhancementStats: stats,
                timestamp: Date.now()
              }, null, 2),
            },
          ],
        };
      }

      case 'generate_enhancement_proposal': {
        const enhancementEngine = await componentManager.getEnhancementEngine();
        const proposal = await enhancementEngine.generateWeeklyEnhancements();
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                proposal,
                generatedAt: Date.now()
              }, null, 2),
            },
          ],
        };
      }

      case 'get_research_summary': {
        const params = GetResearchSummarySchema.parse(args);
        const researchAgent = await componentManager.getResearchAgent();
        const summary = researchAgent.getResearchSummary(params.days);
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                researchSummary: summary,
                timeframe: `${params.days} days`,
                timestamp: Date.now()
              }, null, 2),
            },
          ],
        };
      }

      case 'send_notification': {
        const params = SendNotificationSchema.parse(args);
        const communicationLayer = await componentManager.getCommunicationLayer();
        
        communicationLayer.emit('notification', {
          type: params.type,
          severity: params.severity,
          title: params.title,
          message: params.message,
          domain: params.domain,
          timestamp: Date.now()
        });
        
        return {
          content: [
            {
              type: 'text',
              text: `✅ Notification sent: ${params.title}`,
            },
          ],
        };
      }

      case 'get_dashboard_data': {
        // Lazy load only what we need for dashboard
        const healthReporter = await componentManager.getHealthReporter();
        const dashboardData = healthReporter.getDashboardData();
        
        // Only load communication and visual if they're already initialized
        const componentStats = componentManager.getComponentStats();
        const initializedComponents = componentManager.getInitializedComponents();
        
        const response: any = {
          health: dashboardData,
          components: {
            stats: componentStats,
            initialized: initializedComponents
          },
          timestamp: Date.now()
        };
        
        // Only include stats from initialized components to avoid lazy loading overhead
        if (initializedComponents.includes('communicationLayer')) {
          const communicationLayer = await componentManager.getCommunicationLayer();
          response.communication = communicationLayer.getStats();
        }
        
        if (initializedComponents.includes('visualEngine')) {
          const visualEngine = await componentManager.getVisualEngine();
          response.visual = visualEngine.getStats();
        }
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(response, null, 2),
            },
          ],
        };
      }

      case 'test_notification_channel': {
        const params = TestNotificationChannelSchema.parse(args);
        const communicationLayer = await componentManager.getCommunicationLayer();
        const success = await communicationLayer.testNotification(params.channelName);
        
        return {
          content: [
            {
              type: 'text',
              text: success ? 
                `✅ Test notification sent successfully to ${params.channelName}` :
                `❌ Failed to send test notification to ${params.channelName}`,
            },
          ],
        };
      }

      default:
        throw new McpError(
          ErrorCode.MethodNotFound,
          `Unknown tool: ${name}`
        );
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid parameters: ${error.errors.map(e => e.message).join(', ')}`
      );
    }
    throw error;
  }
});

// Cleanup function for graceful shutdown
async function cleanup() {
  console.error('🧹 Starting graceful shutdown...');
  
  try {
    // Cleanup component manager (handles all enhanced components)
    await componentManager.cleanup();
    
    // Cleanup existing components if cleanup methods exist
    if (typeof (scraper as any).cleanup === 'function') {
      await (scraper as any).cleanup();
    }
    if (typeof (redditScraper as any).cleanup === 'function') {
      await (redditScraper as any).cleanup();
    }
    
    console.error('✅ Cleanup completed successfully');
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  }
}

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  
  console.error('🚀 Enhanced MCP X-Reddit Scraper server running on stdio');
  console.error('📊 Comprehensive monitoring and enhancement systems active');
  
  // Handle shutdown signals
  process.on('SIGINT', async () => {
    console.error('🛑 Received SIGINT, shutting down...');
    await cleanup();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.error('🛑 Received SIGTERM, shutting down...');
    await cleanup();
    process.exit(0);
  });
}

main().catch(async (error) => {
  console.error('Fatal error:', error);
  await cleanup();
  process.exit(1);
});