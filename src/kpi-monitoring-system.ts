/**
 * KPI Monitoring System
 * 
 * Comprehensive monitoring and analytics for scraping performance:
 * - Success rates by domain, target, and time period
 * - Block detection accuracy and response times
 * - Data quality scores and completeness metrics
 * - Cost tracking per successful request
 * - Real-time alerts and trend analysis
 * - Performance benchmarking against targets
 */

import { writeFileSync, existsSync, readFileSync, mkdirSync } from 'fs';
import { join } from 'path';
import { EventEmitter } from 'events';

interface KPIMetric {
  timestamp: number;
  domain: string;
  target: string;
  metric: string;
  value: number;
  metadata?: Record<string, unknown>;
}

interface PerformanceSnapshot {
  timestamp: number;
  overall: {
    totalRequests: number;
    successfulRequests: number;
    blockedRequests: number;
    errorRequests: number;
    successRate: number;
    blockRate: number;
    avgResponseTime: number;
    costPerRequest: number;
  };
  byDomain: Record<string, DomainMetrics>;
  byTarget: Record<string, TargetMetrics>;
  trends: {
    successRate: TrendData;
    responseTime: TrendData;
    blockRate: TrendData;
    dataQuality: TrendData;
  };
}

interface DomainMetrics {
  domain: string;
  requests: number;
  successes: number;
  blocks: number;
  errors: number;
  avgResponseTime: number;
  successRate: number;
  blockRate: number;
  dataQualityScore: number;
  lastActivity: number;
  status: 'healthy' | 'degraded' | 'blocked' | 'error';
}

interface TargetMetrics {
  target: string;
  domain: string;
  requests: number;
  successes: number;
  dataPoints: number;
  completeness: number;
  freshness: number;
  qualityScore: number;
  costEfficiency: number;
  lastSuccess: number;
  trending: 'up' | 'down' | 'stable';
}

interface TrendData {
  current: number;
  previous: number;
  change: number;
  trend: 'improving' | 'degrading' | 'stable';
  dataPoints: Array<{ timestamp: number; value: number }>;
}

interface Alert {
  id: string;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: 'performance' | 'blocking' | 'data_quality' | 'cost' | 'availability';
  title: string;
  message: string;
  domain: string;
  target?: string;
  metric: string;
  value: number;
  threshold: number;
  resolved: boolean;
  resolvedAt?: number;
}

export class KPIMonitoringSystem extends EventEmitter {
  private metricsHistory: KPIMetric[] = [];
  private snapshots: PerformanceSnapshot[] = [];
  private activeAlerts: Map<string, Alert> = new Map();
  private alertHistory: Alert[] = [];
  private dataDir: string;
  private lastSnapshotTime = 0;
  private snapshotInterval = 300000; // 5 minutes
  
  // Configurable thresholds for alerts
  private thresholds = {
    successRate: {
      warning: 85,
      critical: 70
    },
    blockRate: {
      warning: 5,
      critical: 15
    },
    responseTime: {
      warning: 5000,  // 5 seconds
      critical: 10000 // 10 seconds
    },
    dataQuality: {
      warning: 85,
      critical: 70
    },
    costPerRequest: {
      warning: 0.02,
      critical: 0.05
    }
  };

  constructor(dataDir: string = './data/kpi') {
    super();
    this.dataDir = dataDir;
    this.ensureDataDir();
    this.loadHistoricalData();
    this.startPeriodicSnapshots();
  }

  private ensureDataDir(): void {
    if (!existsSync(this.dataDir)) {
      mkdirSync(this.dataDir, { recursive: true });
    }
  }

  private loadHistoricalData(): void {
    try {
      const metricsFile = join(this.dataDir, 'metrics.json');
      const snapshotsFile = join(this.dataDir, 'snapshots.json');
      const alertsFile = join(this.dataDir, 'alerts.json');

      if (existsSync(metricsFile)) {
        const data = JSON.parse(readFileSync(metricsFile, 'utf-8'));
        this.metricsHistory = data.metrics || [];
      }

      if (existsSync(snapshotsFile)) {
        const data = JSON.parse(readFileSync(snapshotsFile, 'utf-8'));
        this.snapshots = data.snapshots || [];
      }

      if (existsSync(alertsFile)) {
        const data = JSON.parse(readFileSync(alertsFile, 'utf-8'));
        this.alertHistory = data.alerts || [];
        
        // Restore active alerts
        this.alertHistory
          .filter(alert => !alert.resolved)
          .forEach(alert => this.activeAlerts.set(alert.id, alert));
      }

      console.log(`📊 KPI Monitoring loaded: ${this.metricsHistory.length} metrics, ${this.snapshots.length} snapshots, ${this.alertHistory.length} alerts`);
    } catch (error) {
      console.warn('Failed to load historical KPI data:', error);
    }
  }

  private persistData(): void {
    try {
      // Keep only last 10,000 metrics and 1,000 snapshots for performance
      const recentMetrics = this.metricsHistory.slice(-10000);
      const recentSnapshots = this.snapshots.slice(-1000);

      writeFileSync(
        join(this.dataDir, 'metrics.json'),
        JSON.stringify({ metrics: recentMetrics }, null, 2)
      );

      writeFileSync(
        join(this.dataDir, 'snapshots.json'),
        JSON.stringify({ snapshots: recentSnapshots }, null, 2)
      );

      writeFileSync(
        join(this.dataDir, 'alerts.json'),
        JSON.stringify({ alerts: this.alertHistory.slice(-500) }, null, 2)
      );
    } catch (error) {
      console.error('Failed to persist KPI data:', error);
    }
  }

  private startPeriodicSnapshots(): void {
    setInterval(() => {
      this.generateSnapshot();
    }, this.snapshotInterval);
  }

  recordMetric(
    domain: string,
    target: string,
    metric: string,
    value: number,
    metadata?: Record<string, unknown>
  ): void {
    const kpiMetric: KPIMetric = {
      timestamp: Date.now(),
      domain,
      target,
      metric,
      value,
      metadata
    };

    this.metricsHistory.push(kpiMetric);
    this.checkThresholds(kpiMetric);

    // Persist periodically
    if (this.metricsHistory.length % 100 === 0) {
      this.persistData();
    }
  }

  recordRequest(
    domain: string,
    target: string,
    success: boolean,
    responseTime: number,
    blocked: boolean = false,
    dataQuality: number = 100,
    cost: number = 0
  ): void {
    const timestamp = Date.now();

    // Record individual metrics
    this.recordMetric(domain, target, 'request_total', 1);
    this.recordMetric(domain, target, 'response_time', responseTime);
    this.recordMetric(domain, target, 'data_quality', dataQuality);
    this.recordMetric(domain, target, 'cost', cost);

    if (success) {
      this.recordMetric(domain, target, 'request_success', 1);
    } else {
      this.recordMetric(domain, target, 'request_error', 1);
    }

    if (blocked) {
      this.recordMetric(domain, target, 'request_blocked', 1);
    }

    // Emit event for real-time monitoring
    this.emit('request_recorded', {
      domain,
      target,
      success,
      responseTime,
      blocked,
      dataQuality,
      cost,
      timestamp
    });
  }

  generateSnapshot(): PerformanceSnapshot {
    const now = Date.now();
    const hourAgo = now - 3600000; // 1 hour
    const recentMetrics = this.metricsHistory.filter(m => m.timestamp > hourAgo);

    // Calculate overall metrics
    const totalRequests = this.sumMetricsByName(recentMetrics, 'request_total');
    const successfulRequests = this.sumMetricsByName(recentMetrics, 'request_success');
    const blockedRequests = this.sumMetricsByName(recentMetrics, 'request_blocked');
    const errorRequests = this.sumMetricsByName(recentMetrics, 'request_error');
    
    const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0;
    const blockRate = totalRequests > 0 ? (blockedRequests / totalRequests) * 100 : 0;
    const avgResponseTime = this.averageMetricsByName(recentMetrics, 'response_time');
    const costPerRequest = totalRequests > 0 ? this.sumMetricsByName(recentMetrics, 'cost') / totalRequests : 0;

    // Calculate domain-specific metrics
    const byDomain: Record<string, DomainMetrics> = {};
    const domains = new Set(recentMetrics.map(m => m.domain));
    
    for (const domain of domains) {
      const domainMetrics = recentMetrics.filter(m => m.domain === domain);
      const domainRequests = this.sumMetricsByName(domainMetrics, 'request_total');
      const domainSuccesses = this.sumMetricsByName(domainMetrics, 'request_success');
      const domainBlocks = this.sumMetricsByName(domainMetrics, 'request_blocked');
      const domainErrors = this.sumMetricsByName(domainMetrics, 'request_error');
      const domainSuccessRate = domainRequests > 0 ? (domainSuccesses / domainRequests) * 100 : 0;
      const domainBlockRate = domainRequests > 0 ? (domainBlocks / domainRequests) * 100 : 0;

      let status: DomainMetrics['status'] = 'healthy';
      if (domainBlockRate > 10) {status = 'blocked';}
      else if (domainSuccessRate < 70) {status = 'error';}
      else if (domainSuccessRate < 85) {status = 'degraded';}

      byDomain[domain] = {
        domain,
        requests: domainRequests,
        successes: domainSuccesses,
        blocks: domainBlocks,
        errors: domainErrors,
        avgResponseTime: this.averageMetricsByName(domainMetrics, 'response_time'),
        successRate: domainSuccessRate,
        blockRate: domainBlockRate,
        dataQualityScore: this.averageMetricsByName(domainMetrics, 'data_quality'),
        lastActivity: Math.max(...domainMetrics.map(m => m.timestamp), 0),
        status
      };
    }

    // Calculate target-specific metrics
    const byTarget: Record<string, TargetMetrics> = {};
    const targets = new Set(recentMetrics.map(m => `${m.domain}:${m.target}`));
    
    for (const targetKey of targets) {
      const [domain, target] = targetKey.split(':');
      const targetMetrics = recentMetrics.filter(m => m.domain === domain && m.target === target);
      const targetRequests = this.sumMetricsByName(targetMetrics, 'request_total');
      const targetSuccesses = this.sumMetricsByName(targetMetrics, 'request_success');

      byTarget[targetKey] = {
        target,
        domain,
        requests: targetRequests,
        successes: targetSuccesses,
        dataPoints: targetSuccesses, // Simplified
        completeness: this.averageMetricsByName(targetMetrics, 'data_quality'),
        freshness: 100, // Simplified - would calculate based on timestamp
        qualityScore: this.averageMetricsByName(targetMetrics, 'data_quality'),
        costEfficiency: this.averageMetricsByName(targetMetrics, 'cost'),
        lastSuccess: Math.max(...targetMetrics.filter(m => m.metric === 'request_success').map(m => m.timestamp), 0),
        trending: 'stable' // Simplified - would calculate trend
      };
    }

    // Calculate trends
    const previousSnapshot = this.snapshots[this.snapshots.length - 1];
    const trends = {
      successRate: this.calculateTrend('successRate', successRate, previousSnapshot?.overall.successRate),
      responseTime: this.calculateTrend('responseTime', avgResponseTime, previousSnapshot?.overall.avgResponseTime),
      blockRate: this.calculateTrend('blockRate', blockRate, previousSnapshot?.overall.blockRate),
      dataQuality: this.calculateTrend('dataQuality', this.averageMetricsByName(recentMetrics, 'data_quality'), 100)
    };

    const snapshot: PerformanceSnapshot = {
      timestamp: now,
      overall: {
        totalRequests,
        successfulRequests,
        blockedRequests,
        errorRequests,
        successRate,
        blockRate,
        avgResponseTime,
        costPerRequest
      },
      byDomain,
      byTarget,
      trends
    };

    this.snapshots.push(snapshot);
    this.lastSnapshotTime = now;

    // Emit snapshot event
    this.emit('snapshot_generated', snapshot);

    console.log(`📊 KPI Snapshot generated - Success Rate: ${successRate.toFixed(1)}%, Block Rate: ${blockRate.toFixed(1)}%`);
    
    return snapshot;
  }

  private sumMetricsByName(metrics: KPIMetric[], name: string): number {
    return metrics
      .filter(m => m.metric === name)
      .reduce((sum, m) => sum + m.value, 0);
  }

  private averageMetricsByName(metrics: KPIMetric[], name: string): number {
    const values = metrics
      .filter(m => m.metric === name)
      .map(m => m.value);
    
    return values.length > 0 ? values.reduce((sum, v) => sum + v, 0) / values.length : 0;
  }

  private calculateTrend(metric: string, current: number, previous?: number): TrendData {
    const change = previous !== undefined ? current - previous : 0;
    let trend: TrendData['trend'] = 'stable';
    
    if (Math.abs(change) > 5) { // 5% threshold
      if (metric === 'successRate' || metric === 'dataQuality') {
        trend = change > 0 ? 'improving' : 'degrading';
      } else {
        trend = change > 0 ? 'degrading' : 'improving';
      }
    }

    // Get recent data points for trend visualization
    const recentDataPoints = this.snapshots
      .slice(-20) // Last 20 snapshots
      .map(s => ({
        timestamp: s.timestamp,
        value: this.getSnapshotMetricValue(s, metric)
      }));

    return {
      current,
      previous: previous || 0,
      change,
      trend,
      dataPoints: recentDataPoints
    };
  }

  private getSnapshotMetricValue(snapshot: PerformanceSnapshot, metric: string): number {
    switch (metric) {
      case 'successRate': return snapshot.overall.successRate;
      case 'responseTime': return snapshot.overall.avgResponseTime;
      case 'blockRate': return snapshot.overall.blockRate;
      case 'dataQuality': return Object.values(snapshot.byDomain).reduce((avg, d) => avg + d.dataQualityScore, 0) / Object.keys(snapshot.byDomain).length;
      default: return 0;
    }
  }

  private checkThresholds(metric: KPIMetric): void {
    const { domain, target, metric: metricName, value } = metric;
    
    // Check success rate threshold
    if (metricName === 'request_success' || metricName === 'request_total') {
      const recentMetrics = this.metricsHistory
        .filter(m => m.domain === domain && m.timestamp > Date.now() - 600000) // Last 10 minutes
        .filter(m => m.metric === 'request_total' || m.metric === 'request_success');
      
      const totalRequests = this.sumMetricsByName(recentMetrics, 'request_total');
      const successfulRequests = this.sumMetricsByName(recentMetrics, 'request_success');
      
      if (totalRequests > 5) { // Only check if we have enough data
        const successRate = (successfulRequests / totalRequests) * 100;
        
        if (successRate < this.thresholds.successRate.critical) {
          this.createAlert({
            severity: 'critical',
            type: 'performance',
            title: 'Critical Success Rate Drop',
            message: `Success rate for ${domain} has dropped to ${successRate.toFixed(1)}%`,
            domain,
            target,
            metric: 'success_rate',
            value: successRate,
            threshold: this.thresholds.successRate.critical
          });
        } else if (successRate < this.thresholds.successRate.warning) {
          this.createAlert({
            severity: 'high',
            type: 'performance',
            title: 'Success Rate Warning',
            message: `Success rate for ${domain} has dropped to ${successRate.toFixed(1)}%`,
            domain,
            target,
            metric: 'success_rate',
            value: successRate,
            threshold: this.thresholds.successRate.warning
          });
        }
      }
    }

    // Check response time threshold
    if (metricName === 'response_time' && value > this.thresholds.responseTime.warning) {
      const severity = value > this.thresholds.responseTime.critical ? 'critical' : 'high';
      
      this.createAlert({
        severity,
        type: 'performance',
        title: 'High Response Time',
        message: `Response time for ${domain}/${target} is ${value.toFixed(0)}ms`,
        domain,
        target,
        metric: 'response_time',
        value,
        threshold: severity === 'critical' ? this.thresholds.responseTime.critical : this.thresholds.responseTime.warning
      });
    }

    // Check block rate threshold
    if (metricName === 'request_blocked') {
      const recentMetrics = this.metricsHistory
        .filter(m => m.domain === domain && m.timestamp > Date.now() - 600000);
      
      const totalRequests = this.sumMetricsByName(recentMetrics, 'request_total');
      const blockedRequests = this.sumMetricsByName(recentMetrics, 'request_blocked');
      
      if (totalRequests > 0) {
        const blockRate = (blockedRequests / totalRequests) * 100;
        
        if (blockRate > this.thresholds.blockRate.critical) {
          this.createAlert({
            severity: 'critical',
            type: 'blocking',
            title: 'High Block Rate',
            message: `Block rate for ${domain} is ${blockRate.toFixed(1)}%`,
            domain,
            target,
            metric: 'block_rate',
            value: blockRate,
            threshold: this.thresholds.blockRate.critical
          });
        }
      }
    }
  }

  private createAlert(alertData: Omit<Alert, 'id' | 'timestamp' | 'resolved' | 'resolvedAt'>): void {
    const alertId = `${alertData.domain}_${alertData.metric}_${Date.now()}`;
    
    const alert: Alert = {
      id: alertId,
      timestamp: Date.now(),
      resolved: false,
      ...alertData
    };

    this.activeAlerts.set(alertId, alert);
    this.alertHistory.push(alert);

    // Emit alert event
    this.emit('alert_created', alert);

    console.warn(`🚨 Alert: ${alert.title} - ${alert.message}`);
  }

  resolveAlert(alertId: string): boolean {
    const alert = this.activeAlerts.get(alertId);
    if (alert) {
      alert.resolved = true;
      alert.resolvedAt = Date.now();
      this.activeAlerts.delete(alertId);
      
      this.emit('alert_resolved', alert);
      console.log(`✅ Alert resolved: ${alert.title}`);
      return true;
    }
    return false;
  }

  getCurrentSnapshot(): PerformanceSnapshot | null {
    return this.snapshots[this.snapshots.length - 1] || null;
  }

  getActiveAlerts(): Alert[] {
    return Array.from(this.activeAlerts.values());
  }

  getAlertHistory(limit: number = 100): Alert[] {
    return this.alertHistory.slice(-limit);
  }

  getDomainHealth(domain: string): DomainMetrics | null {
    const snapshot = this.getCurrentSnapshot();
    return snapshot?.byDomain[domain] || null;
  }

  getTopPerformingTargets(limit: number = 10): TargetMetrics[] {
    const snapshot = this.getCurrentSnapshot();
    if (!snapshot) {return [];}

    return Object.values(snapshot.byTarget)
      .sort((a, b) => b.qualityScore - a.qualityScore)
      .slice(0, limit);
  }

  getPerformanceReport(timeframe: 'hour' | 'day' | 'week' = 'hour'): {
    summary: PerformanceSnapshot['overall'];
    trends: PerformanceSnapshot['trends'];
    topIssues: Alert[];
    recommendations: string[];
  } {
    const snapshot = this.getCurrentSnapshot();
    if (!snapshot) {
      return {
        summary: {
          totalRequests: 0,
          successfulRequests: 0,
          blockedRequests: 0,
          errorRequests: 0,
          successRate: 0,
          blockRate: 0,
          avgResponseTime: 0,
          costPerRequest: 0
        },
        trends: {
          successRate: { current: 0, previous: 0, change: 0, trend: 'stable', dataPoints: [] },
          responseTime: { current: 0, previous: 0, change: 0, trend: 'stable', dataPoints: [] },
          blockRate: { current: 0, previous: 0, change: 0, trend: 'stable', dataPoints: [] },
          dataQuality: { current: 0, previous: 0, change: 0, trend: 'stable', dataPoints: [] }
        },
        topIssues: [],
        recommendations: []
      };
    }

    const topIssues = this.getActiveAlerts()
      .sort((a, b) => {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      })
      .slice(0, 5);

    const recommendations: string[] = [];
    
    if (snapshot.overall.successRate < 85) {
      recommendations.push('Success rate is below target. Consider switching to stricter anti-detection mode.');
    }
    
    if (snapshot.overall.blockRate > 5) {
      recommendations.push('Block rate is elevated. Review proxy rotation and request patterns.');
    }
    
    if (snapshot.overall.avgResponseTime > 5000) {
      recommendations.push('Response times are high. Consider optimizing request concurrency.');
    }

    return {
      summary: snapshot.overall,
      trends: snapshot.trends,
      topIssues,
      recommendations
    };
  }

  cleanup(): void {
    console.log('🧹 Cleaning up KPI Monitoring System...');
    this.persistData();
    this.removeAllListeners();
    console.log('✅ KPI Monitoring System cleanup complete');
  }
}