// Real-Time Dashboard Screenshot System
// Captures visual monitoring data for comprehensive scraping analysis

import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { writeFileSync, mkdirSync, existsSync } from 'fs';
import { join } from 'path';
import { PrometheusScrapingMetrics } from './prometheus-metrics.js';

export interface ScreenshotConfig {
  interval: number;           // Screenshot interval in ms
  dashboard_url: string;      // Grafana/dashboard URL
  output_dir: string;         // Screenshot output directory  
  retention_hours: number;    // How long to keep screenshots
  notification_threshold: {   // Alert thresholds for screenshots
    error_rate: number;
    response_time: number;
    blocked_requests: number;
  };
}

export class DashboardScreenshotSystem {
  private config: ScreenshotConfig;
  private metrics: PrometheusScrapingMetrics;
  private browser: Browser | null = null;
  private isRunning = false;
  private screenshotCounter = 0;

  constructor(
    config: ScreenshotConfig,
    metrics: PrometheusScrapingMetrics
  ) {
    this.config = config;
    this.metrics = metrics;
    this.ensureOutputDirectory();
  }

  private ensureOutputDirectory(): void {
    if (!existsSync(this.config.output_dir)) {
      mkdirSync(this.config.output_dir, { recursive: true });
    }
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('📸 Dashboard screenshot system already running');
      return;
    }

    console.log('📸 Starting real-time dashboard screenshot system...');
    
    try {
      this.browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-web-security',
          '--window-size=1920,1080'
        ]
      });

      this.isRunning = true;
      this.scheduleScreenshots();
      
      console.log(`✅ Screenshot system started - capturing every ${this.config.interval/1000}s`);
    } catch (error) {
      console.error('❌ Failed to start screenshot system:', error);
      throw error;
    }
  }

  private scheduleScreenshots(): void {
    if (!this.isRunning || !this.browser) {return;}

    setTimeout(() => {
      void (async () => {
        try {
          await this.captureComprehensiveSnapshot();
          this.scheduleScreenshots(); // Schedule next capture
        } catch (error) {
          console.error('Screenshot capture error:', error);
          this.scheduleScreenshots(); // Continue despite errors
        }
      })();
    }, this.config.interval);
  }

  async captureComprehensiveSnapshot(): Promise<{
    timestamp: number;
    dashboard_screenshot: string;
    metrics_snapshot: unknown;
    system_status: unknown;
    alerts: string[];
  }> {
    if (!this.browser) {throw new Error('Browser not initialized');}

    const timestamp = Date.now();
    const screenshotId = `dashboard_${timestamp}_${++this.screenshotCounter}`;
    
    console.log(`📸 Capturing comprehensive snapshot: ${screenshotId}`);

    // Capture dashboard screenshot
    const dashboardScreenshot = await this.captureDashboardScreenshot(screenshotId);
    
    // Get metrics snapshot
    const metricsSnapshot = await this.metrics.getMetricsSnapshot();
    
    // Capture system status
    const systemStatus = await this.captureSystemStatus();
    
    // Generate alerts
    const alerts = this.generateRealTimeAlerts(metricsSnapshot, systemStatus);
    
    // Save comprehensive report
    const report = {
      timestamp,
      dashboard_screenshot: dashboardScreenshot,
      metrics_snapshot: metricsSnapshot,
      system_status: systemStatus,
      alerts,
      screenshot_id: screenshotId
    };

    this.saveReport(screenshotId, report);
    
    // Cleanup old screenshots
    await this.cleanupOldScreenshots();

    return report;
  }

  private async captureDashboardScreenshot(screenshotId: string): Promise<string> {
    if (!this.browser) {throw new Error('Browser not initialized');}

    const page = await this.browser.newPage();
    
    try {
      // Set viewport for consistent screenshots
      await page.setViewport({ width: 1920, height: 1080 });
      
      // Navigate to dashboard
      if (this.config.dashboard_url) {
        await page.goto(this.config.dashboard_url, { 
          waitUntil: 'networkidle2',
          timeout: 30000 
        });
        
        // Wait for dashboard to fully load
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Take full page screenshot
        const screenshotPath = join(this.config.output_dir, `${screenshotId}.png`);
        await page.screenshot({
          path: screenshotPath,
          fullPage: true,
          quality: 90
        });
        
        return screenshotPath;
      } else {
        // Generate a metrics visualization if no dashboard URL
        return await this.generateMetricsVisualization(page, screenshotId);
      }
    } finally {
      await page.close();
    }
  }

  private async generateMetricsVisualization(page: Page, screenshotId: string): Promise<string> {
    // Generate HTML visualization of metrics
    const metricsSnapshot = await this.metrics.getMetricsSnapshot();
    
    const html = this.generateMetricsHTML(metricsSnapshot);
    
    await page.setContent(html);
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const screenshotPath = join(this.config.output_dir, `${screenshotId}_metrics.png`);
    await page.screenshot({
      path: screenshotPath,
      fullPage: true,
      quality: 90
    });
    
    return screenshotPath;
  }

  private generateMetricsHTML(metrics: Record<string, unknown>): string {
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>MCP Scraper Dashboard</title>
      <style>
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; }
        .metric-card { background: #2d2d2d; padding: 20px; border-radius: 8px; border: 1px solid #444; }
        .metric-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #4CAF50; }
        .metric-value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .metric-label { font-size: 12px; color: #888; }
        .status-good { color: #4CAF50; }
        .status-warning { color: #FF9800; }
        .status-error { color: #f44336; }
        .timestamp { text-align: center; color: #888; margin-top: 30px; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🛡️ MCP X/Reddit Scraper Dashboard</h1>
        <p>Real-time monitoring and safety metrics</p>
      </div>
      
      <div class="metrics-grid">
        <div class="metric-card">
          <div class="metric-title">📊 Request Rate</div>
          <div class="metric-value status-good">${(metrics.requests as { rate?: number })?.rate ?? 0}/min</div>
          <div class="metric-label">Requests per minute</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-title">🛡️ Safety Status</div>
          <div class="metric-value status-good">PROTECTED</div>
          <div class="metric-label">IP protection active</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-title">🔧 Cache Hit Rate</div>
          <div class="metric-value status-good">${(metrics.cache as { hitRate?: number })?.hitRate?.toFixed(1) ?? 0}%</div>
          <div class="metric-label">Cache efficiency</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-title">🌐 Browser Sessions</div>
          <div class="metric-value status-good">${(metrics.browser as { activeSessions?: number })?.activeSessions ?? 0}</div>
          <div class="metric-label">Active sessions</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-title">🎯 Data Quality</div>
          <div class="metric-value status-good">${(metrics.dataQuality as { successRate?: number })?.successRate?.toFixed(1) ?? 100}%</div>
          <div class="metric-label">Extraction success rate</div>
        </div>
        
        <div class="metric-card">
          <div class="metric-title">🤖 AI Analysis</div>
          <div class="metric-value status-good">${(metrics.ai as { requests?: number })?.requests ?? 0}</div>
          <div class="metric-label">Analysis requests</div>
        </div>
      </div>
      
      <div class="timestamp">
        📸 Captured: ${new Date().toISOString()}
      </div>
    </body>
    </html>`;
  }

  private async captureSystemStatus(): Promise<{
    cpu_usage: number;
    memory_usage: number;
    disk_usage: number;
    network_status: string;
    scraper_health: string;
  }> {
    // Get system metrics
    const memUsage = process.memoryUsage();
    
    return {
      cpu_usage: process.cpuUsage().user / 1000000, // Convert to seconds
      memory_usage: (memUsage.heapUsed / memUsage.heapTotal) * 100,
      disk_usage: 0, // Placeholder - would implement disk usage check
      network_status: 'connected',
      scraper_health: this.isRunning ? 'healthy' : 'stopped'
    };
  }

  private generateRealTimeAlerts(metrics: Record<string, unknown>, systemStatus: Record<string, unknown>): string[] {
    const alerts: string[] = [];
    const thresholds = this.config.notification_threshold;
    
    // High error rate
    const errorRate = 100 - ((metrics.dataQuality as { successRate?: number })?.successRate ?? 100);
    if (errorRate > thresholds.error_rate) {
      alerts.push(`🚨 HIGH ERROR RATE: ${errorRate.toFixed(1)}% > ${thresholds.error_rate}%`);
    }
    
    // High memory usage
    const memoryUsage = (systemStatus as { memory_usage?: number }).memory_usage;
    if (typeof memoryUsage === 'number' && memoryUsage > 80) {
      alerts.push(`⚠️ HIGH MEMORY USAGE: ${memoryUsage.toFixed(1)}%`);
    }
    
    // Blocked requests
    const blockedRequests = (metrics.safety as { blockedRequests?: { value: number }[] })?.blockedRequests;
    const blockedCount = blockedRequests?.reduce((sum: number, br) => sum + br.value, 0) ?? 0;
    if (blockedCount > thresholds.blocked_requests) {
      alerts.push(`🛡️ BLOCKED REQUESTS: ${blockedCount} requests blocked`);
    }
    
    // Low cache hit rate
    const cacheHitRate = (metrics.cache as { hitRate?: number })?.hitRate;
    if (typeof cacheHitRate === 'number' && cacheHitRate < 50) {
      alerts.push(`💾 LOW CACHE HIT RATE: ${cacheHitRate.toFixed(1)}%`);
    }
    
    return alerts;
  }

  private saveReport(screenshotId: string, report: Record<string, unknown>): void {
    const reportPath = join(this.config.output_dir, `${screenshotId}_report.json`);
    writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`✅ Comprehensive report saved: ${screenshotId}`);
    
    // Log alerts if any
    const reportAlerts = report.alerts as string[];
    if (reportAlerts.length > 0) {
      console.info('🚨 ALERTS DETECTED:');
      reportAlerts.forEach((alert: string) => console.info(`  ${alert}`));
    }
  }

  private async cleanupOldScreenshots(): Promise<void> {
    // Implementation would clean up screenshots older than retention_hours
    const cutoffTime = Date.now() - (this.config.retention_hours * 60 * 60 * 1000);
    // ... cleanup logic
  }

  async stop(): Promise<void> {
    console.log('📸 Stopping dashboard screenshot system...');
    this.isRunning = false;
    
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
    
    console.log('✅ Screenshot system stopped');
  }

  // Manual screenshot capture
  async captureNow(reason: string = 'manual'): Promise<string> {
    if (!this.isRunning) {
      throw new Error('Screenshot system not running');
    }
    
    const report = await this.captureComprehensiveSnapshot();
    console.log(`📸 Manual screenshot captured: ${reason}`);
    return report.dashboard_screenshot;
  }

  // Get recent screenshots for analysis
  getRecentScreenshots(count: number = 10): string[] {
    // Implementation would return paths to recent screenshots
    return [];
  }
}

// Default configuration
export const DEFAULT_SCREENSHOT_CONFIG: ScreenshotConfig = {
  interval: 60000, // 1 minute
  dashboard_url: '', // Set to Grafana URL if available
  output_dir: './screenshots/dashboard',
  retention_hours: 24,
  notification_threshold: {
    error_rate: 15,        // Alert if error rate > 15%
    response_time: 5000,   // Alert if response time > 5s
    blocked_requests: 5    // Alert if > 5 blocked requests
  }
};