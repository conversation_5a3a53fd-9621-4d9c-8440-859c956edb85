#!/usr/bin/env node
/**
 * MCP X/Reddit Scraper - Modularized Version
 * This is a transitional implementation that organizes handlers by category
 * while maintaining compatibility with existing interfaces
 */

import 'dotenv/config';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import { XScraper } from './scraper.js';
import { RedditScraper } from './reddit-scraper.js';
import { TrendingAnalyzer } from './analyzer.js';
import { getAgentBridge } from './agent-bridge.js';
import { getFabricBridge } from './fabric-bridge.js';
import { TargetManager } from './target-manager.js';
import { AITargetDiscovery } from './ai-discovery.js';
import { ComponentManager } from './component-manager.js';

// Import all schemas
import {
  GetCommentsSchema,
  GetKeyContributorsSchema,
  GetTrendingTopicsSchema,
  AnalyzeSentimentSchema,
  GetTrendingKeywordsSchema,
  AnalyzeKeywordsSchema
} from './schemas/twitter-schemas.js';

import {
  GetSubredditPostsSchema,
  GetRedditCommentsSchema,
  GetRedditTrendingSchema,
  SearchRedditSchema,
  AnalyzeRedditTrendsSchema,
  AnalyzeRedditCommentsSchema
} from './schemas/reddit-schemas.js';

import {
  NavigateSchema,
  ScreenshotSchema,
  ClickSchema,
  FillSchema,
  ScrollSchema,
  AnalyzePageVisuallySchema
} from './schemas/browser-schemas.js';

import {
  CreateChatSessionSchema,
  ChatWithAgentSchema,
  ChatShorthandSchema,
  GetSessionInfoSchema,
  ExportReportSchema,
  ListFabricPatternsSchema,
  ApplyFabricPatternSchema,
  ChainFabricPatternsSchema,
  CreateCustomPatternSchema,
  AnalyzeWithFabricSchema,
  BatchApplyPatternSchema
} from './schemas/ai-schemas.js';

import {
  ListTargetsSchema,
  GetTargetCategoriesSchema,
  AddInfluencerSchema,
  AddSubredditSchema,
  RemoveTargetSchema,
  GetTopTargetsSchema,
  BatchAnalyzeTargetsSchema,
  DiscoverNewTargetsSchema,
  GetRecentDiscoveriesSchema,
  PromoteDiscoveredTargetSchema,
  UpdateTargetRelevanceSchema
} from './schemas/target-schemas.js';

import {
  GetSystemHealthSchema,
  GetKPISnapshotSchema,
  GetBlockingStatsSchema,
  GetEnhancementStatsSchema,
  GenerateEnhancementProposalSchema,
  GetResearchSummarySchema,
  SendNotificationSchema,
  GetDashboardDataSchema,
  TestNotificationChannelSchema
} from './schemas/enhanced-schemas.js';

// Initialize components
const scraper = new XScraper();
const redditScraper = new RedditScraper();
const analyzer = new TrendingAnalyzer();
const agentBridge = getAgentBridge();
const fabricBridge = getFabricBridge();
const targetManager = new TargetManager();
const aiDiscovery = new AITargetDiscovery();
const componentManager = ComponentManager.getInstance();

// Default session management for @chat shorthand
let defaultChatSession: string | null = null;

const server = new Server(
  {
    name: 'x-scraper',
    version: '0.1.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// Tool definitions organized by category
const tools = [
  // Twitter/X Tools
  {
    name: 'get_comments',
    description: 'Scrape comments from a specific X/Twitter user',
    inputSchema: GetCommentsSchema,
  },
  {
    name: 'get_key_contributors',
    description: 'Find key contributors for a specific topic or hashtag',
    inputSchema: GetKeyContributorsSchema,
  },
  {
    name: 'get_trending_topics',
    description: 'Get current trending topics with optional category filter',
    inputSchema: GetTrendingTopicsSchema,
  },
  {
    name: 'analyze_sentiment',
    description: 'Analyze sentiment of posts or comments',
    inputSchema: AnalyzeSentimentSchema,
  },
  
  // Browser Tools
  {
    name: 'navigate',
    description: 'Navigate browser to a specific URL',
    inputSchema: NavigateSchema,
  },
  {
    name: 'screenshot',
    description: 'Take a screenshot of the current page or specific element',
    inputSchema: ScreenshotSchema,
  },
  {
    name: 'click',
    description: 'Click on an element using CSS selector',
    inputSchema: ClickSchema,
  },
  {
    name: 'fill',
    description: 'Fill an input field with text',
    inputSchema: FillSchema,
  },
  {
    name: 'scroll',
    description: 'Scroll the page up or down',
    inputSchema: ScrollSchema,
  },

  // Reddit Tools
  {
    name: 'get_subreddit_posts',
    description: 'Get posts from a specific subreddit',
    inputSchema: GetSubredditPostsSchema,
  },

  // AI Tools
  {
    name: 'create_chat_session',
    description: 'Create a new AI agent chat session',
    inputSchema: CreateChatSessionSchema,
  },
  {
    name: 'chat_with_agent',
    description: 'Chat with the AI agent using a session ID',
    inputSchema: ChatWithAgentSchema,
  },
  {
    name: '@chat',
    description: 'Simple chat interface - your message to AI agent',
    inputSchema: ChatShorthandSchema,
  },

  // Fabric Tools
  {
    name: 'list_fabric_patterns',
    description: 'List available Fabric analysis patterns',
    inputSchema: ListFabricPatternsSchema,
  },
  {
    name: 'apply_fabric_pattern',
    description: 'Apply a Fabric pattern to analyze content',
    inputSchema: ApplyFabricPatternSchema,
  },
  
  // Target Management Tools
  {
    name: 'list_targets',
    description: 'List configured influencer and subreddit targets',
    inputSchema: ListTargetsSchema,
  },

  // Enhanced Tools (basic set for now)
  {
    name: 'get_system_health',
    description: 'Get comprehensive system health metrics and status',
    inputSchema: GetSystemHealthSchema,
  }
];

server.setRequestHandler(ListToolsRequestSchema, async () => ({
  tools: tools,
}));

// Simplified handler implementation - working versions only for now
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  try {
    const { name, arguments: args } = request.params;

    switch (name) {
      // Twitter Tools
      case 'get_comments': {
        const params = GetCommentsSchema.parse(args);
        const comments = await scraper.getComments(
          params.username,
          params.limit,
          params.includeReplies
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(comments, null, 2),
            },
          ],
        };
      }

      case 'get_key_contributors': {
        const params = GetKeyContributorsSchema.parse(args);
        const contributors = await scraper.getKeyContributors(
          params.topic,
          params.limit
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(contributors, null, 2),
            },
          ],
        };
      }

      case 'get_trending_topics': {
        const params = GetTrendingTopicsSchema.parse(args);
        const topics = await scraper.getTrendingTopics(
          params.category,
          params.location
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(topics, null, 2),
            },
          ],
        };
      }

      case 'analyze_sentiment': {
        const params = AnalyzeSentimentSchema.parse(args);
        const results = await analyzer.analyzeSentiment(
          params.posts,
          params.granularity
        );
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(results, null, 2),
            },
          ],
        };
      }

      // Browser Tools
      case 'navigate': {
        const params = NavigateSchema.parse(args);
        await scraper.navigate(params.url);
        return {
          content: [
            {
              type: 'text',
              text: `Successfully navigated to ${params.url}`,
            },
          ],
        };
      }

      case 'screenshot': {
        const params = ScreenshotSchema.parse(args);
        const path = await scraper.screenshot(params.name, params.selector);
        return {
          content: [
            {
              type: 'text',
              text: `Screenshot saved: ${path}`,
            },
          ],
        };
      }

      case 'click': {
        const params = ClickSchema.parse(args);
        await scraper.click(params.selector);
        return {
          content: [
            {
              type: 'text',
              text: `Successfully clicked element: ${params.selector}`,
            },
          ],
        };
      }

      case 'fill': {
        const params = FillSchema.parse(args);
        await scraper.fill(params.selector, params.value);
        return {
          content: [
            {
              type: 'text',
              text: `Successfully filled ${params.selector} with value`,
            },
          ],
        };
      }

      case 'scroll': {
        const params = ScrollSchema.parse(args);
        await scraper.scroll(params.direction, params.amount);
        return {
          content: [
            {
              type: 'text',
              text: `Scrolled ${params.direction} by ${params.amount}px`,
            },
          ],
        };
      }

      // AI Tools
      case 'create_chat_session': {
        CreateChatSessionSchema.parse(args);
        const sessionId = await agentBridge.createChatSession();
        return {
          content: [
            {
              type: 'text',
              text: `Created chat session: ${sessionId}`,
            },
          ],
        };
      }

      case 'chat_with_agent': {
        const params = ChatWithAgentSchema.parse(args);
        
        let sessionId = params.sessionId;
        if (!sessionId) {
          sessionId = await agentBridge.createChatSession();
          console.log(`📋 Created new chat session: ${sessionId}`);
        }

        const response = await agentBridge.chat(sessionId, params.message);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(response, null, 2),
            },
          ],
        };
      }

      case '@chat': {
        const params = ChatShorthandSchema.parse(args);
        
        if (!defaultChatSession) {
          defaultChatSession = await agentBridge.createChatSession();
          console.log(`📋 Created default chat session: ${defaultChatSession}`);
        }

        const response = await agentBridge.chat(defaultChatSession, params.message);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(response, null, 2),
            },
          ],
        };
      }

      // Fabric Tools
      case 'list_fabric_patterns': {
        const params = ListFabricPatternsSchema.parse(args);
        const patterns = await fabricBridge.listPatterns(params.category);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(patterns, null, 2),
            },
          ],
        };
      }

      case 'apply_fabric_pattern': {
        const params = ApplyFabricPatternSchema.parse(args);
        const result = await fabricBridge.applyPattern(
          params.patternName,
          params.content,
          params.model,
          params.temperature
        );
        
        return {
          content: [
            {
              type: 'text',
              text: result.success ? 
                `**Pattern:** ${result.pattern}\n**Execution Time:** ${result.executionTime}ms\n\n**Result:**\n${result.output}` :
                `**Error applying pattern ${result.pattern}:**\n${result.error}`,
            },
          ],
        };
      }

      // Target Management Tools
      case 'list_targets': {
        const params = ListTargetsSchema.parse(args);
        
        const targets: any = {};
        if (params.type === 'twitter' || params.type === 'both') {
          targets.twitter_influencers = targetManager.getInfluencers(params.category);
        }
        if (params.type === 'reddit' || params.type === 'both') {
          targets.reddit_subreddits = targetManager.getSubreddits(params.category);
        }
        if (params.type === undefined) {
          targets.twitter_influencers = targetManager.getInfluencers(params.category);
          targets.reddit_subreddits = targetManager.getSubreddits(params.category);
        }
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(targets, null, 2),
            },
          ],
        };
      }

      // Enhanced Tools
      case 'get_system_health': {
        const params = GetSystemHealthSchema.parse(args);
        const healthReporter = await componentManager.getHealthReporter();
        const health = await healthReporter.calculateSystemHealth();
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(health, null, 2),
            },
          ],
        };
      }

      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error: any) {
    console.error(`Tool execution error for ${request.params.name}:`, error);
    
    if (error.message?.includes('not found') || error.message?.includes('Unknown tool')) {
      throw new McpError(
        ErrorCode.MethodNotFound,
        `Tool '${request.params.name}' not found. Use list_tools to see available tools.`
      );
    }
    
    if (error.message?.includes('validation')) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid parameters for tool '${request.params.name}': ${error.message}`
      );
    }
    
    throw new McpError(
      ErrorCode.InternalError,
      `Failed to execute tool '${request.params.name}': ${error.message}`
    );
  }
});

// Handle process termination gracefully
async function cleanup() {
  console.log('🧹 Starting server cleanup...');
  
  try {
    await scraper.cleanup();
    console.log('✅ Server cleanup complete');
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }
  
  process.exit(0);
}

process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

async function main() {
  const transport = new StdioServerTransport();
  
  console.log('🚀 Starting MCP X/Reddit Scraper Server (Modular Version)...');
  console.log(`📊 Registered ${tools.length} tools`);
  console.log('🎯 Server ready for connections');
  
  await server.connect(transport);
}

main().catch((error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});